# 浮标智能分析功能集成方案

## 1. 总体架构与数据流

```mermaid
flowchart TD
    A[前端用户选择历史数据] --> B[前端整理数据]
    B --> C[前端请求后端分析API]
    C --> D[后端接收数据]
    D --> E[后端调用 deepseek LLM API]
    E --> F[LLM 返回分析结果]
    F --> G[后端处理/格式化结果]
    G --> H[返回前端]
    H --> I[前端展示分报告]
```

## 2. 关键模块与接口设计

### 2.1 前端

- 复用现有传感器历史数据（SensorData[]），增加“智能分析”按钮。
- 选定数据后，调用新接口（如 `POST /api/analysis/report`），传递所选历史数据。
- 展示 LLM 返回的分报告（文本、图表等）。

### 2.2 后端

- 新增分析接口（如 `POST /api/analysis/report`），接收前端传感器历史数据。
- 封装 deepseek LLM API 调用逻辑（含鉴权、异常处理、重试等）。
- 对 LLM 返回结果做结构化处理，返回前端。

### 2.3 deepseek LLM

- 通过官方 API 云端调用，需配置 API Key、URL 等。
- API Key 和 URL 等配置在 .env 中（无法访问 .env 的情况下，修改 .env.example 作为示例）
- 输入：传感器历史数据（JSON/文本）。
- 输出：分析报告（结构化 JSON）。

## 3. 数据格式建议

### 3.1 输入（前端传递给后端）

```json
{
  "buoy_id": "xxx",
  "sensor_type": "温度",
  "data": [
    {"timestamp": "2025-06-01T10:00:00Z", "value": 23.5, "unit": "℃"},
    ...
  ]
}
```

### 3.2 输出（后端返回前端）

```json
{
  "trend": "本周温度整体平稳，6月2日出现异常升高。",
  "anomalies": [
    {"timestamp": "2025-06-02T14:00:00Z", "value": 29.1, "desc": "异常升高"}
  ],
  "forecast": "未来24小时温度预计缓慢上升。",
  "suggestion": "建议关注6月2日异常，检查设备运行状态。"
}
```

## 4. 关键实现点

- **前端**：在历史数据页面增加“智能分析”入口，调用新接口并展示结果（文本、图表）。
- **后端**：实现分析 API，负责数据转发、LLM 调用、结果处理。
- **安全性**：后端管理 deepseek API Key，避免泄露。
- **可扩展性**：分报告内容、格式可根据后续需求调整。

## 5. 前后端分工

| 模块   | 主要职责 |
|--------|----------|
| 前端   | 数据选择、接口调用、结果展示（图表/文本） |
| 后端   | 数据接收、LLM 调用、结果结构化、接口返回 |
| LLM    | 智能分析、趋势预测、异常检测、建议生成 |

## 6. 开发建议

1. 确认 deepseek LLM API 的输入输出格式和调用限制。
2. 设计前后端接口文档，约定数据格式。
3. 评估数据量和接口性能，必要时做分页或异步处理。
4. 后端可增加缓存/限流，防止重复分析和滥用。

## 7. 后端防护与成本控制建议

为防止分析接口被大量请求拖垮服务或导致 deepseek API 调用成本激增，建议后端实现如下防护措施：

### 7.1 限流与频控

- **IP 限流**：对单个 IP 在单位时间内的请求数做限制（如每分钟不超过 3 次）。
- **用户级限流**：对登录用户账号做频率限制（如每用户每天不超过 10 次）。
- **全局限流**：接口整体 QPS 限制，防止突发流量。

可选技术方案：
- Python: 使用 `slowapi`（基于 Starlette/FastAPI 的限流中间件）、`redis` 计数器等。

### 7.2 缓存与去重

- **请求参数去重**：同一用户/同一数据集的分析请求，短时间内只允许一次，结果可缓存返回。
- **结果缓存**：对相同分析请求结果做缓存（如 Redis），有效期可设为 1 小时~1 天，减少重复调用 LLM。

### 7.3 权限与认证

- **接口需鉴权**：仅登录用户可用，防止匿名滥用。
- **管理端可配置限流参数**：便于后期动态调整。

### 7.4 监控与告警

- **接口调用量、异常、LLM 调用次数等需监控**，超阈值自动告警。

### 7.5 失败重试与降级

- **LLM 调用失败可重试 1~2 次**，多次失败后返回友好提示，避免阻塞主流程。

---

如需进一步细化接口文档或开发任务分解，请补充说明。
