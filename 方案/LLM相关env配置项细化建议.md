# LLM相关.env配置项细化建议

## 1. 推荐配置项一览

| 配置项                | 说明                         | 是否必填 | 推荐值/示例                | 备注                         |
|----------------------|------------------------------|----------|----------------------------|------------------------------|
| DEEPSEEK_API_KEY     | DeepSeek LLM API密钥         | 是       | your_deepseek_api_key      | 强烈建议仅后端可见           |
| DEEPSEEK_API_ENDPOINT| DeepSeek LLM API地址         | 是       | https://api.deepseek.com/v1| 通常无需更改                 |
| DEEPSEEK_MODEL       | LLM模型名称/版本             | 否       | deepseek-chat, deepseek-xx | 支持多模型时建议必填         |
| DEEPSEEK_TIMEOUT     | LLM接口超时时间（秒）        | 否       | 30                         | 防止长时间阻塞               |
| DEEPSEEK_MAX_TOKENS  | LLM返回最大token数           | 否       | 2048                       | 需参考API文档                |
| DEEPSEEK_RETRY       | 调用失败重试次数             | 否       | 2                          | 建议1~3次                    |
| DEEPSEEK_CACHE_TTL   | 结果缓存时长（秒）           | 否       | 3600                       | 1小时，防止重复消耗          |
| DEEPSEEK_PROXY       | 代理设置                     | 否       | http://proxy.example.com:8080 | 有代理需求时填写         |
| LLM_PROVIDER         | LLM服务商标识                | 否       | deepseek, openai, aliyun   | 多LLM支持时建议必填          |
| LLM_LOG_LEVEL        | LLM调用日志级别              | 否       | INFO, DEBUG, ERROR         | 便于排查问题                 |

## 2. 配置项详细说明

- **DEEPSEEK_API_KEY**  
  DeepSeek LLM的API密钥，必须严格保密，仅后端可见。建议生产环境通过环境变量注入，不要硬编码在代码中。

- **DEEPSEEK_API_ENDPOINT**  
  DeepSeek LLM的API地址，通常为官方提供的固定地址。如需切换测试/生产环境，可通过此项灵活配置。

- **DEEPSEEK_MODEL**  
  指定调用的LLM模型名称或版本。例如`deepseek-chat`、`deepseek-vision`等。支持多模型时建议必填，便于后端灵活切换。

- **DEEPSEEK_TIMEOUT**  
  LLM接口请求超时时间（秒）。防止LLM响应过慢导致接口阻塞。建议30秒，视业务需求可调整。

- **DEEPSEEK_MAX_TOKENS**  
  限制LLM返回的最大token数，防止单次响应过大导致成本激增或接口超时。需参考API文档设置。

- **DEEPSEEK_RETRY**  
  LLM调用失败时的自动重试次数。建议1~3次，避免因偶发网络/服务异常导致整体失败。

- **DEEPSEEK_CACHE_TTL**  
  LLM分析结果的缓存时长（秒）。如同一请求短时间内重复调用，可直接返回缓存结果，降低成本、提升响应速度。

- **DEEPSEEK_PROXY**  
  如服务器需通过代理访问外部LLM服务（如公司内网、云服务器等），可配置此项。无代理需求可留空。

- **LLM_PROVIDER**  
  标识当前使用的LLM服务商（如deepseek、openai、aliyun等）。如后端支持多家LLM，建议统一用LLM_PROVIDER+LLM_API_KEY等前缀管理。

- **LLM_LOG_LEVEL**  
  控制LLM调用相关日志输出级别。开发/调试阶段可设为DEBUG，生产建议INFO或ERROR。

## 3. 推荐.env.example片段

```env
# DeepSeek LLM配置
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_API_ENDPOINT=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_TIMEOUT=30
DEEPSEEK_MAX_TOKENS=2048
DEEPSEEK_RETRY=2
DEEPSEEK_CACHE_TTL=3600
# 如需代理
# DEEPSEEK_PROXY=http://proxy.example.com:8080

# 多LLM支持（可选）
# LLM_PROVIDER=deepseek
# LLM_LOG_LEVEL=INFO
```

## 4. 配置管理建议

- **安全性**：API密钥等敏感信息仅应存储于后端环境变量，严禁前端暴露。
- **多环境支持**：建议开发、测试、生产环境分别维护.env文件，避免误用生产密钥。
- **灵活扩展**：如需支持多家LLM，建议统一以`LLM_`前缀管理，便于后端动态切换。
- **文档同步**：每次新增/调整配置项，需同步更新.env.example和相关开发文档。
- **配置校验**：后端启动时应校验关键配置项是否存在，缺失时给出明确报错。

---
如需进一步细化多LLM支持、配置热更新等方案，请补充说明。