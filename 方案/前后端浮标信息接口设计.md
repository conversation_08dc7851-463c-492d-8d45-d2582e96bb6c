# 浮标网站接口设计文档

## 概述

本文档设计浮标网站前端和后端之间的接口，用于查询浮标数据和传感器数据。设计仅考虑 HTTP API 部分，暂不包含 MQTT 和 WebSocket 相关实现。主要功能包括：

1. 查询所有浮标数据（用于地图标注）
2. 查询单个浮标的历史位置信息
3. 查询单个浮标的历史传感器数据

## 后端接口设计

### 1. 获取所有浮标列表

**端点**: `GET /api/v1/buoys`

**描述**: 返回所有浮标的基本信息，包括当前位置，用于地图标注

**参数**:
- `skip`: 分页起始位置（可选，默认0）
- `limit`: 返回结果数量限制（可选，默认100）

**响应**:
```json
[
  {
    "id": 1,
    "name": "浮标A",
    "description": "监测点A",
    "latest_location": {
      "longitude": 121.4737,
      "latitude": 31.2304
    },
    "status": "active",
    "last_heartbeat": "2023-04-03T12:00:00Z"
  },
  ...
]
```

### 2. 获取单个浮标详细信息

**端点**: `GET /api/v1/buoys/{buoy_id}`

**描述**: 获取特定浮标的详细信息

**参数**:
- `buoy_id`: 浮标ID（路径参数）

**响应**:
```json
{
  "id": 1,
  "name": "浮标A",
  "description": "监测点A",
  "latest_location": {
    "longitude": 121.4737,
    "latitude": 31.2304
  },
  "status": "active",
  "last_heartbeat": "2023-04-03T12:00:00Z",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-04-03T12:00:00Z"
}
```

### 3. 获取浮标历史位置数据

**端点**: `GET /api/v1/buoys/{buoy_id}/locations`

**描述**: 获取特定浮标的历史位置数据

**参数**:
- `buoy_id`: 浮标ID（路径参数）
- `start_time`: 开始时间（ISO格式，可选）
- `end_time`: 结束时间（ISO格式，可选）
- `limit`: 返回结果数量限制（可选，默认100）

**响应**:
```json
[
  {
    "longitude": 121.4736,
    "latitude": 31.2303
  },
  {
    "longitude": 121.4735,
    "latitude": 31.2302
  },
  ...
]
```

### 4. 获取浮标传感器数据

**端点**: `GET /api/v1/buoys/{buoy_id}/data`

**描述**: 获取特定浮标的传感器数据

**参数**:
- `buoy_id`: 浮标ID（路径参数）
- `data_type`: 数据类型（可选，如 'ph', 'temperature', 'dissolved_oxygen'）
- `start_time`: 开始时间（ISO格式，可选）
- `end_time`: 结束时间（ISO格式，可选）
- `limit`: 返回结果数量限制（可选，默认100）

**响应**:
```json
[
  {
    "id": 1001,
    "buoy_id": 1,
    "timestamp": "2023-04-03T12:00:00Z",
    "data_type": "temperature",
    "value": 25.5,
    "unit": "℃"
  },
  {
    "id": 1002,
    "buoy_id": 1,
    "timestamp": "2023-04-03T12:00:00Z",
    "data_type": "ph",
    "value": 7.2,
    "unit": "pH"
  },
  ...
]
```

### 5. 获取浮标最新传感器数据

**端点**: `GET /api/v1/buoys/{buoy_id}/data/latest`

**描述**: 获取特定浮标的最新一组传感器数据

**参数**:
- `buoy_id`: 浮标ID（路径参数）

**响应**:
```json
[
  {
    "id": 1001,
    "buoy_id": 1,
    "timestamp": "2023-04-03T12:00:00Z",
    "data_type": "temperature",
    "value": 25.5,
    "unit": "℃"
  },
  {
    "id": 1002,
    "buoy_id": 1,
    "timestamp": "2023-04-03T12:00:00Z",
    "data_type": "ph",
    "value": 7.2,
    "unit": "pH"
  },
  ...
]
```

## 后端实现计划

### 1. 数据模型扩展

在现有的 `SensorData` 模型中，已经包含了位置数据，但为了查询方便，建议在服务层添加专门的位置数据查询函数。

### 2. 添加服务层函数

在 `fastapi_app/services/data_service.py` 添加以下函数：

```python
async def get_buoy_locations(
    db: AsyncSession,
    buoy_id: int,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
) -> List[GeoPoint]:
    """获取浮标的历史位置数据"""
    query = select(SensorData).where(
        SensorData.buoy_id == buoy_id,
        SensorData.data_type == "location"
    ).order_by(SensorData.timestamp.desc()).limit(limit)
    
    if start_time:
        query = query.where(SensorData.timestamp >= start_time)
    if end_time:
        query = query.where(SensorData.timestamp <= end_time)
        
    result = await db.execute(query)
    sensor_data = result.scalars().all()
    
    # 转换为GeoPoint格式
    geo_points = []
    for data in sensor_data:
        if data.location_value:
            # 使用工具函数获取文本格式的点坐标
            location_wkt = await get_geometry_as_text(
                db, "sensor_data", "location_value", "id", data.id
            )
            
            # 解析WKT字符串获取经纬度
            if location_wkt:
                geo_point = await wkt_to_geo_point(location_wkt)
                geo_points.append(geo_point)
    
    return geo_points
```

### 3. 添加 API 路由

在 `fastapi_app/api/api_v1/endpoints/data.py` 添加新的路由：

```python
@router.get("/buoys/{buoy_id}/locations", response_model=List[GeoPoint])
async def read_buoy_locations(
    buoy_id: int,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取浮标的历史位置数据"""
    locations = await get_buoy_locations(
        db, 
        buoy_id=buoy_id,
        start_time=start_time,
        end_time=end_time,
        limit=limit
    )
    return locations
```

## 前端接口设计

### 1. API 客户端函数

在 `src/api/index.ts` 文件中已有部分接口函数，需要确保以下接口函数完整实现：

```typescript
// 获取所有浮标
export const getBuoys = () => api.get<Buoy[]>('/buoys');

// 获取单个浮标详情
export const getBuoyById = (id: string) => api.get<Buoy>(`/buoys/${id}`);

// 获取浮标传感器数据
export const getBuoyData = (id: string, params?: { 
  startTime?: string;
  endTime?: string;
  dataType?: string;
  limit?: number;
}) => api.get<SensorData[]>(`/buoys/${id}/data`, { params });

// 获取浮标历史位置数据
export const getBuoyLocations = (id: string, params?: {
  startTime?: string;
  endTime?: string;
  limit?: number;
}) => api.get<GeoPoint[]>(`/buoys/${id}/locations`, { params });

// 获取浮标最新传感器数据
export const getLatestBuoyData = (id: string) => 
  api.get<SensorData[]>(`/buoys/${id}/data/latest`);
```

### 2. 数据类型定义

需要确保 `src/types/index.ts` 中的类型定义完整：

```typescript
// 浮标信息接口
export interface Buoy {
  id: string;
  name: string;
  description: string;
  latestLocation: {
    longitude: number;
    latitude: number;
  };
  status: 'active' | 'inactive' | 'error';
  lastHeartbeat: string;
  createdAt?: string;
  updatedAt?: string;
}

// 位置数据接口
export interface GeoPoint {
  longitude: number;
  latitude: number;
}

// 传感器数据接口
export interface SensorData {
  id: string;
  buoyId: string;
  timestamp: string;
  dataType: string;
  value: number;
  unit: string;
}
```

## 前端实现计划

### 1. 地图组件集成

在 `GlobeView.tsx` 组件中集成浮标数据查询：

- 组件初始化时调用 `getBuoys()` 获取所有浮标
- 使用浮标的 `latestLocation` 在地图上标注点位
- 点击浮标时，记录选中的浮标 ID，并显示详情面板

### 2. 浮标详情组件

创建 `BuoyDetail.tsx` 组件，用于显示浮标详情和历史数据：

- 接收选中的浮标 ID 作为属性
- 使用 `getBuoyById`, `getBuoyLocations` 和 `getBuoyData` 获取详细数据
- 使用可视化组件展示历史位置和传感器数据

### 3. 数据可视化组件

在 `components/visualizations` 目录下添加数据可视化组件：

- `SensorChart.tsx`: 展示传感器历史数据的图表
- `LocationTrack.tsx`: 展示浮标历史位置轨迹

## 实施步骤

1. 先完成后端服务层和 API 路由的编写
2. 测试后端 API 确保数据正确返回
3. 实现前端 API 客户端函数
4. 实现浮标地图标注功能
5. 实现浮标详情和历史数据展示功能
6. 集成测试

## 后续扩展

后续可考虑添加的功能（本设计暂不实现）：

1. 实时数据通过 WebSocket/MQTT 推送
2. 数据筛选和高级搜索功能
3. 数据导出功能
4. 数据异常预警功能 