# Caddyfile 配置文件

buoy.hanjie-tech.cn {
    # API 反向代理到后端服务 - 必须在 file_server 之前
    handle /api/* {
        reverse_proxy backend:8000
    }

    # WebSocket 支持 - 必须在 file_server 之前
    handle /ws* {
        reverse_proxy backend:8000
    }

    # 设置根目录为前端静态文件目录
    root * /srv

    # 处理 SPA 路由，所有未匹配的请求返回 index.html
    try_files {path} /index.html

    # 启用文件服务器
    file_server
    
    # 启用 gzip 压缩
    encode gzip
    
    # 设置安全头
    header {
        # 启用 HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        # 防止点击劫持
        X-Frame-Options "DENY"
        # 防止 MIME 类型嗅探
        X-Content-Type-Options "nosniff"
        # XSS 保护
        X-XSS-Protection "1; mode=block"
        # 引用策略
        Referrer-Policy "strict-origin-when-cross-origin"
    }
    
    # 静态资源缓存
    @static {
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static Cache-Control "public, max-age=31536000, immutable"
    
    # 日志记录
    log {
        output file /var/log/caddy/access.log
        format json
    }
}

# 可选：HTTP 到 HTTPS 重定向（Caddy 自动处理）
# http://buoy.hanjie-tech.cn {
#     redir https://buoy.hanjie-tech.cn{uri} permanent
# }
