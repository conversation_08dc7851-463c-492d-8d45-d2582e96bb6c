# 文档文件
*.pdf
*.docx
*.doc
*.xls
*.xlsx
*.ppt
*.pptx
*.csv

# 环境变量
.env
.env.local
.env.*.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.venv/
venv/
ENV/
env/
.idea/
.vscode/

# Node/TypeScript/React
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.next/
out/
build/
.DS_Store
*.pem
.vercel
.turbo
.env*.local
dist/
dist-ssr/
*.local
.vite/

# 数据库
*.sqlite
*.sqlite3
*.db

# 日志文件
*.log
logs/
log/

# 系统文件
.DS_Store
Thumbs.db

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.temp
.cache/

# 证书文件
*.crt
*.key
*.pem
*.csr
*.srl
mosquitto/certs/
backend/certs/
simulator/certs/

test.sql