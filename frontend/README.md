# 智能浮标互动体验平台 - 前端

本项目是智能浮标互动体验平台的前端部分，使用React、TypeScript和Vite构建。

## 技术栈

- React 18
- TypeScript
- Vite
- Tailwind CSS
- CesiumJS (地图可视化)
- ECharts (数据图表)
- WebSocket (实时通信)
- Axios (HTTP请求)

## 开发环境设置

### 先决条件

- Node.js (v16+)
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

服务器将在 http://localhost:3000 上运行。

## 项目结构

```
src/
├── api/            # API请求函数
├── assets/         # 静态资源
├── components/     # React组件
│   ├── charts/     # 图表组件
│   ├── controls/   # 控制面板组件
│   ├── layout/     # 布局组件
│   ├── map/        # 地图相关组件
│   └── quiz/       # 知识问答组件
├── hooks/          # 自定义React Hooks
├── pages/          # 页面组件
├── types/          # TypeScript类型定义
└── utils/          # 工具函数
```

## 构建生产版本

```bash
npm run build
```

生成的文件将位于 `dist` 目录中。

## 运行测试

```bash
npm test
```

## API使用说明

### 浮标位置历史数据

获取浮标位置历史数据，包含经纬度和时间戳信息。

```typescript
import { getBuoyLocations } from '../api';

// 获取指定浮标的位置历史数据
const fetchLocationHistory = async (buoyId: string) => {
  try {
    const response = await getBuoyLocations(buoyId, {
      startTime: '2023-01-01T00:00:00Z',  // 可选
      endTime: '2023-01-02T00:00:00Z',    // 可选
      limit: 500                          // 可选，默认100
    });
    
    // 返回数据包含经纬度和时间戳
    const locations = response.data;
    
    // 使用示例：显示位置和时间
    locations.forEach(location => {
      console.log(`位置: ${location.longitude}, ${location.latitude}, 时间: ${location.timestamp}`);
    });
    
  } catch (error) {
    console.error('获取位置历史数据失败', error);
  }
};
```

## 数据可视化

BuoyDataChart组件支持显示带时间戳的位置轨迹数据，使用示例：

```tsx
import { BuoyDataChart } from '../components';

// 在组件中使用
<BuoyDataChart
  locationHistory={buoyLocationHistory} // 包含timestamp字段的位置数据
  chartType="location"
  height="300px"
  title="浮标位置轨迹"
/>
```
