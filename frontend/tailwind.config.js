/** @type {import('@tailwindcss/vite').Config} */
export default {
  content: [
    "./frontend/index.html",
    "./frontend/src/**/*.{js,ts,jsx,tsx}",
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: ['class', '[data-theme="dark"]'],
  theme: {
    extend: {
      colors: {
        primary: 'var(--color-primary)',
        secondary: 'var(--color-secondary)',
        background: {
          main: 'var(--color-bg-main)',
          secondary: 'var(--color-bg-secondary)',
        },
        text: {
          primary: 'var(--color-text-primary)',
          secondary: 'var(--color-text-secondary)',
        },
        border: 'var(--color-border)',
        buoy: {
          active: 'var(--color-buoy-active)',
          inactive: 'var(--color-buoy-inactive)',
          error: 'var(--color-buoy-error)',
          default: 'var(--color-buoy-default)',
        },
        scrollbar: {
          thumb: 'var(--color-scrollbar-thumb)',
          track: 'var(--color-scrollbar-track)',
          hover: 'var(--color-scrollbar-hover)',
        },
      },
    },
  },
  plugins: [
    // 使用 v4 API 创建自定义工具类
    ({ addBase }) => {
      addBase({
        ':root': {
          '--scrollbar-thin-width': '6px',
          '--scrollbar-thin-height': '6px',
          '--scrollbar-thin-radius': '3px',
        }
      });
    },
    ({ addUtilities }) => {
      addUtilities({
        '.scrollbar-thin': {
          '&::-webkit-scrollbar': {
            width: 'var(--scrollbar-thin-width)',
            height: 'var(--scrollbar-thin-height)',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'var(--color-scrollbar-track)',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'var(--color-scrollbar-thumb)',
            borderRadius: 'var(--scrollbar-thin-radius)',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            backgroundColor: 'var(--color-scrollbar-hover)',
          },
          '&::-webkit-scrollbar-button': {
            display: 'none',
          },
          'scrollbar-width': 'thin',
          'scrollbar-color': 'var(--color-scrollbar-thumb) var(--color-scrollbar-track)',
        },
        '.scrollbar-hide': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
      });
    }
  ],
} 