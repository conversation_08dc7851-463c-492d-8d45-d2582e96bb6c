import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import cesium from 'vite-plugin-cesium'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())

  // 调试环境变量
  console.log('Vite配置中的环境变量:', {
    VITE_API_BASE_URL: env.VITE_API_BASE_URL,
    VITE_BACKEND_URL: env.VITE_BACKEND_URL,
    VITE_WEBSOCKET_URL: env.VITE_WEBSOCKET_URL,
    VITE_WEBSOCKET_PATH: env.VITE_WEBSOCKET_PATH,
    NODE_ENV: process.env.NODE_ENV,
    mode: mode
  })

  return {
  plugins: [react(), cesium(), tailwindcss()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    assetsInlineLimit: 0,
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      '/api/v1': {
        target: env.VITE_BACKEND_URL || 'http://backend:8000',
        changeOrigin: true,
        secure: false,
        followRedirects: false, // 不跟随重定向
        rewrite: (path) => path,
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('代理错误:', err);
          });
          // proxy.on('proxyReq', (proxyReq, req) => {
          //   console.log('代理请求:', {
          //     url: req.url,
          //     method: req.method,
          //     headers: req.headers,
          //     target: proxyReq.host
          //   });
          // });
          // 添加响应拦截器
          // proxy.on('proxyRes', (proxyRes, req) => {
          //   console.log('代理响应:', {
          //     url: req.url,
          //     method: req.method,
          //     statusCode: proxyRes.statusCode,
          //     statusMessage: proxyRes.statusMessage,
          //     headers: proxyRes.headers
          //   });
          // });
        }
      },
      '/ws': {
        target: env.VITE_WEBSOCKET_URL || 'ws://backend:8000',
        ws: true,
        changeOrigin: true,
        secure: false,
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('WebSocket代理错误:', err);
          });
          proxy.on('proxyReqWs', (proxyReq, req) => {
            console.log('WebSocket代理请求:', {
              url: req.url,
              headers: req.headers,
              target: proxyReq.host
            });
          });
        }
      }
    }
  },
  define: {
    CESIUM_BASE_URL: JSON.stringify('/'),
  },
  }
})
