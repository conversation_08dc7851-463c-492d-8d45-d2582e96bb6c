{"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "settings": {"react": {"version": "detect"}}, "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/ban-ts-comment": "warn", "no-console": ["warn", {"allow": ["error", "warn", "info"]}]}}