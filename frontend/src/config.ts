/**
 * 应用配置
 * 集中管理所有环境变量和配置项
 */

// 调试环境变量
console.log('环境变量调试:', {
  VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
  VITE_BACKEND_URL: import.meta.env.VITE_BACKEND_URL,
  VITE_WEBSOCKET_URL: import.meta.env.VITE_WEBSOCKET_URL,
  VITE_WEBSOCKET_PATH: import.meta.env.VITE_WEBSOCKET_PATH,
  DEV: import.meta.env.DEV,
  MODE: import.meta.env.MODE,
});

// API配置
export const API_CONFIG = {
  // API基础URL，强制使用相对路径
  // 注意：这里我们确保总是使用相对路径，即使环境变量中设置了绝对URL
  BASE_URL: '/api/v1',
  // API超时时间（毫秒）
  TIMEOUT: 60000,  // 增加到 60 秒
};

// WebSocket配置
export const WEBSOCKET_CONFIG = {
  // WebSocket路径，强制使用相对路径
  PATH: '/ws',
  // 完整的WebSocket URL（如果有）
  FULL_URL: null, // 强制使用相对路径，让代理处理
  // WebSocket重连配置
  RECONNECT: {
    // 最大重连尝试次数
    MAX_ATTEMPTS: 10,
    // 重连延迟（毫秒）
    DELAY: 5000,
  },
};

// 日志配置
export const LOG_CONFIG = {
  // 是否启用详细日志
  VERBOSE: import.meta.env.DEV || false,
};

// 导出所有配置
export default {
  API: API_CONFIG,
  WEBSOCKET: WEBSOCKET_CONFIG,
  LOG: LOG_CONFIG,
};
