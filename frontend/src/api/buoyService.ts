import api from './apiClient';
import { parseApiError } from '../utils/errorHandler';
import type {
  Buoy,
  SensorData,
  ImageData,
  ControlCommand,
  ControlResponse,
  AnalysisReport,
  GeoPoint
} from '../types';

// 浮标相关API
export const getBuoys = () => api.get<Buoy[]>('/buoys/');
export const getBuoyById = (id: string) => api.get<Buoy>(`/buoys/${id}`);
export const getBuoyData = (id: string, params?: {
  startTime?: string;
  endTime?: string;
  dataType?: string;
}) => api.get<SensorData[]>(`/data/buoys/${id}/sensor`, { params });

// 获取浮标位置历史数据，包含时间戳
export const getBuoyLocations = (id: string, params?: {
  startTime?: string;
  endTime?: string;
  limit?: number;
}) => api.get<GeoPoint[]>(`/data/buoys/${id}/locations`, { params });

export const getBuoyImages = (id: string, params?: {
  startTime?: string;
  endTime?: string;
}) => api.get<ImageData[]>(`/buoys/${id}/images`, { params });

/**
 * 发送控制指令到浮标
 * @param id 浮标ID
 * @param command 控制指令
 * @returns 控制指令响应
 *
 * @example
 * // 设置浮标灯光
 * controlBuoy('123', {
 *   command: 'set_light',
 *   brightness: 75,
 *   color: '#FF8C00'
 * });
 */
export const controlBuoy = async (id: string, command: ControlCommand): Promise<ControlResponse> => {
  try {
    // 验证亮度范围
    if (command.brightness !== undefined && (command.brightness < 0 || command.brightness > 100)) {
      throw new Error('亮度必须在0-100范围内');
    }

    // 验证颜色格式
    if (command.color !== undefined && !command.color.startsWith('#')) {
      throw new Error('颜色必须是以#开头的十六进制格式');
    }

    // 发送请求
    const response = await api.post<ControlResponse>(`/buoys/${id}/control`, command);
    return response.data;
  } catch (error) {
    // 使用errorHandler中的函数处理错误
    const apiError = parseApiError(error);
    throw new Error(apiError.message);
  }
};

// 分析和预测API
export const getAnalysis = (id: string, type: 'report' | 'prediction', period?: string) =>
  api.get<AnalysisReport>(`/buoys/${id}/analysis`, { params: { type, period } });
