import axios from 'axios';
import type { InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { parseApiError } from '../utils/errorHandler';

// 从配置文件获取API配置
import { API_CONFIG } from '../config';

// 创建一个处理认证过期的函数变量，将在应用初始化时设置
let handleAuthExpired: ((returnPath?: string) => void) | null = null;

// 导出设置处理函数的方法，在应用初始化时调用
export const setAuthExpiredHandler = (handler: (returnPath?: string) => void) => {
  handleAuthExpired = handler;
};

// 创建API客户端实例
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 添加请求拦截器，用于JWT认证
api.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  const token = localStorage.getItem('token');
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 添加响应拦截器，统一处理错误
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    const apiError = parseApiError(error);

    // 处理401未授权错误
    if (apiError.status === 401) {
      // 将当前路径作为参数传递，以便重定向后可以返回
      const currentPath = window.location.pathname;

      // 如果已设置处理函数，则调用它
      if (handleAuthExpired) {
        handleAuthExpired(currentPath);
      } else {
        console.warn('认证过期处理函数未设置，无法处理401错误');
        // 仍然清除token，即使没有处理函数
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
      }
    }

    return Promise.reject(apiError);
  }
);

export default api;
