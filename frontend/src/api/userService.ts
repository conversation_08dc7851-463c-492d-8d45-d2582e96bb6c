import api from './apiClient';
import type { User } from '../types';
import { API_CONFIG } from '../config';

// 用户相关API
export const login = (username: string, password: string) => {
  console.log('发送登录请求到:', `${API_CONFIG.BASE_URL}/auth/login`);
  return api.post('/auth/login', new URLSearchParams({
    username,
    password
  }), {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
};

export const register = (username: string, email: string, password: string) =>
  api.post('/auth/register', { username, email, password });

export const getCurrentUser = () => api.get<User>('/users/me');

export const logout = () => api.post('/auth/logout');

export interface UserProfileUpdate {
  username?: string;
  email?: string;
  avatar?: string;
  bio?: string;
}

export const updateUserProfile = (data: UserProfileUpdate) => api.put('/users/me', data);
export const changePassword = (oldPassword: string, newPassword: string) =>
  api.post('/users/me/password', { current_password: oldPassword, new_password: newPassword });

// 通知API
export const getNotifications = () => api.get('/notifications');
export const markNotificationAsRead = (id: string) =>
  api.put(`/notifications/${id}/read`);

// 反馈API
export const submitFeedback = (content: string) =>
  api.post('/feedback', { content });
