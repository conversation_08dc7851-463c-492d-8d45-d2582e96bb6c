import api from './apiClient';

// 反馈类型枚举
export enum FeedbackType {
  SUGGESTION = 'suggestion',
  PROBLEM = 'problem',
  BUG = 'bug',
  OTHER = 'other'
}

// 反馈状态枚举
export enum FeedbackStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  RESOLVED = 'resolved'
}

// 反馈数据接口
export interface Feedback {
  id: number;
  user_id?: number;
  contact?: string;
  type: FeedbackType;
  content: string;
  status: FeedbackStatus;
  reply?: string;
  created_at: string;
  updated_at: string;
}

// 创建反馈请求接口
export interface FeedbackCreateRequest {
  contact?: string;
  type: FeedbackType;
  content: string;
}

// 更新反馈请求接口（管理员用）
export interface FeedbackUpdateRequest {
  status?: FeedbackStatus;
  reply?: string;
}

// 反馈列表响应接口
export interface FeedbackListResponse {
  items: Feedback[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 反馈统计接口
export interface FeedbackStats {
  total: number;
  pending: number;
  processing: number;
  resolved: number;
  by_type: Record<string, number>;
}

// 反馈类型选项
export const FEEDBACK_TYPE_OPTIONS = [
  { value: FeedbackType.SUGGESTION, label: '建议' },
  { value: FeedbackType.PROBLEM, label: '问题' },
  { value: FeedbackType.BUG, label: 'BUG' },
  { value: FeedbackType.OTHER, label: '其他' }
];

// 反馈状态选项
export const FEEDBACK_STATUS_OPTIONS = [
  { value: FeedbackStatus.PENDING, label: '未处理' },
  { value: FeedbackStatus.PROCESSING, label: '处理中' },
  { value: FeedbackStatus.RESOLVED, label: '已处理' }
];

// 获取状态显示文本
export const getStatusText = (status: FeedbackStatus): string => {
  const option = FEEDBACK_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.label || status;
};

// 获取类型显示文本
export const getTypeText = (type: FeedbackType): string => {
  const option = FEEDBACK_TYPE_OPTIONS.find(opt => opt.value === type);
  return option?.label || type;
};

// 反馈API服务
export const feedbackService = {
  // 提交反馈
  async createFeedback(data: FeedbackCreateRequest): Promise<Feedback> {
    const response = await api.post('/feedback/', data);
    return response.data;
  },

  // 获取当前用户的反馈列表
  async getMyFeedback(params?: {
    page?: number;
    size?: number;
    status?: FeedbackStatus;
  }): Promise<FeedbackListResponse> {
    const response = await api.get('/feedback/my', { params });
    return response.data;
  },

  // 获取所有反馈列表（管理员）
  async getAllFeedback(params?: {
    page?: number;
    size?: number;
    status?: FeedbackStatus;
    type?: FeedbackType;
    search?: string;
  }): Promise<FeedbackListResponse> {
    const response = await api.get('/feedback/', { params });
    return response.data;
  },

  // 更新反馈（管理员）
  async updateFeedback(id: number, data: FeedbackUpdateRequest): Promise<Feedback> {
    const response = await api.put(`/feedback/${id}`, data);
    return response.data;
  },

  // 获取反馈统计（管理员）
  async getFeedbackStats(): Promise<FeedbackStats> {
    const response = await api.get('/feedback/stats');
    return response.data;
  }
};
