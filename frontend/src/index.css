@import "tailwindcss";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 主题色彩变量 - 将被ThemeContext动态设置 */
    --color-primary: #3B82F6;
    --color-secondary: #10B981;
    --color-bg-main: #FFFFFF;
    --color-bg-secondary: #F9FAFB;
    --color-text-primary: #111827;
    --color-text-secondary: #6B7280;
    --color-border: #E5E7EB;
    --color-buoy-active: #FBBF24;
    --color-buoy-inactive: #9CA3AF;
    --color-buoy-error: #EF4444;
    --color-buoy-default: #3B82F6;

    /* 滚动条颜色 */
    --color-scrollbar-thumb: rgba(136, 136, 136, 0.6);
    --color-scrollbar-track: rgba(241, 241, 241, 0.3);
    --color-scrollbar-hover: rgba(85, 85, 85, 0.8);
  }

  /* 深色主题 - 通过[data-theme="dark"]选择器应用 */
  [data-theme="dark"] {
    --color-primary: #60A5FA;
    --color-secondary: #34D399;
    --color-bg-main: #1F2937;
    --color-bg-secondary: #111827;
    --color-text-primary: #F9FAFB;
    --color-text-secondary: #D1D5DB;
    --color-border: #374151;
    --color-buoy-active: #FBBF24;
    --color-buoy-inactive: #6B7280;
    --color-buoy-error: #F87171;
    --color-buoy-default: #60A5FA;

    /* 深色主题滚动条 */
    --color-scrollbar-thumb: rgba(102, 102, 102, 0.6);
    --color-scrollbar-track: rgba(45, 45, 45, 0.3);
    --color-scrollbar-hover: rgba(140, 140, 140, 0.8);
  }
}

/* 为滚动条设置统一样式 */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: var(--color-scrollbar-track);
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: var(--color-scrollbar-thumb);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-scrollbar-hover);
}

/* 移除滚动条按钮 */
.scrollbar-thin::-webkit-scrollbar-button {
  display: none;
}

/* Firefox 滚动条 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar-thumb) var(--color-scrollbar-track);
}

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 辅助工具类 */
@layer utilities {
  .text-primary {
    color: var(--color-text-primary);
  }
  
  .text-secondary {
    color: var(--color-text-secondary);
  }
  
  .bg-primary {
    background-color: var(--color-primary);
  }
  
  .bg-secondary {
    background-color: var(--color-secondary);
  }
  
  .bg-main {
    background-color: var(--color-bg-main);
  }
  
  .bg-secondary-bg {
    background-color: var(--color-bg-secondary);
  }
  
  .border-theme {
    border-color: var(--color-border);
  }
  
  /* 自定义的暗模式文本类 */
  .sidebar-text {
    color: #1F2937;
  }
  
  [data-theme="dark"] .sidebar-text {
    color: #F3F4F6 !important;
  }
  
  /* 直接覆盖暗模式下的文本颜色 */
  [data-theme="dark"] .dark\:text-gray-100,
  [data-theme="dark"] .dark\:text-gray-300,
  [data-theme="dark"] .dark\:text-gray-400 {
    color: #F3F4F6 !important;
  }
}
