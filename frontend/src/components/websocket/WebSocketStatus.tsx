import { useWebSocket } from '../../contexts/WebSocketContext';
import { WebSocketConnectionState } from '../../types';
import { useTheme } from '../../contexts/ThemeContext';

interface WebSocketStatusProps {
  className?: string;
  showLabel?: boolean;
}

/**
 * WebSocket状态指示器组件
 * 显示当前WebSocket连接状态
 */
export const WebSocketStatus = ({ 
  className = '', 
  showLabel = true 
}: WebSocketStatusProps) => {
  const { connectionState } = useWebSocket();
  const { theme } = useTheme();

  // 根据连接状态获取颜色
  const getStatusColor = (state: WebSocketConnectionState): string => {
    switch (state) {
      case WebSocketConnectionState.OPEN:
        return 'bg-green-500';
      case WebSocketConnectionState.CONNECTING:
      case WebSocketConnectionState.RECONNECTING:
        return 'bg-yellow-500';
      case WebSocketConnectionState.CLOSING:
        return 'bg-orange-500';
      case WebSocketConnectionState.ERROR:
        return 'bg-red-500';
      case WebSocketConnectionState.CLOSED:
      default:
        return 'bg-gray-500';
    }
  };

  // 根据连接状态获取文本
  const getStatusText = (state: WebSocketConnectionState): string => {
    switch (state) {
      case WebSocketConnectionState.OPEN:
        return '已连接';
      case WebSocketConnectionState.CONNECTING:
        return '连接中';
      case WebSocketConnectionState.RECONNECTING:
        return '重连中';
      case WebSocketConnectionState.CLOSING:
        return '关闭中';
      case WebSocketConnectionState.ERROR:
        return '错误';
      case WebSocketConnectionState.CLOSED:
      default:
        return '已断开';
    }
  };

  // 状态颜色
  const statusColor = getStatusColor(connectionState);
  // 状态文本
  const statusText = getStatusText(connectionState);

  return (
    <div className={`flex items-center ${className}`}>
      {/* 状态指示点 */}
      <div 
        className={`w-3 h-3 rounded-full ${statusColor} ${theme === 'dark' ? 'shadow-glow-sm' : ''}`}
        title={statusText}
      />
      
      {/* 状态文本 */}
      {showLabel && (
        <span className="ml-2 text-sm">
          {statusText}
        </span>
      )}
    </div>
  );
};

export default WebSocketStatus;
