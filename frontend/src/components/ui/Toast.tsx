import { createContext, ReactNode, useContext, useState, useCallback, useEffect } from 'react';

type ToastType = 'success' | 'error' | 'info' | 'warning';

// 扩展Toast接口，添加是否已读属性和来源信息
export interface Toast {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
  read?: boolean;
  from?: string;
  time?: string;
}

interface ToastContextType {
  showToast: (message: string, type: ToastType, duration?: number) => string;
  hideToast: (id: string) => void;
  toasts: Toast[];
  history: Toast[];  // 添加历史通知数组
  markAsRead: (id: string) => void;  // 添加标记已读方法
  markAllAsRead: () => void;  // 添加全部标记已读方法
  clearHistory: () => void;  // 添加清空历史方法
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const ToastProvider = ({ children }: { children: ReactNode }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const [history, setHistory] = useState<Toast[]>([]);

  // 显示通知，并添加到历史记录
  const showToast = useCallback((message: string, type: ToastType, duration = 5000) => {
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newToast: Toast = { 
      id, 
      message, 
      type, 
      duration,
      read: false,
      from: type === 'info' || type === 'success' ? 'System' : 'Alert',
      time: new Date().toLocaleTimeString()
    };
    
    setToasts(prev => [...prev, newToast]);
    
    // 添加到历史记录
    setHistory(prev => [newToast, ...prev].slice(0, 20)); // 只保留最近20条
    
    return id;
  }, []);

  // 隐藏通知
  const hideToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  // 标记通知为已读
  const markAsRead = useCallback((id: string) => {
    setHistory(prev => 
      prev.map(toast => 
        toast.id === id ? { ...toast, read: true } : toast
      )
    );
  }, []);

  // 标记所有通知为已读
  const markAllAsRead = useCallback(() => {
    setHistory(prev => 
      prev.map(toast => ({ ...toast, read: true }))
    );
  }, []);

  // 清空历史记录
  const clearHistory = useCallback(() => {
    setHistory([]);
  }, []);

  // 通知自动消失
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    
    toasts.forEach(toast => {
      if (toast.duration) {
        const timer = setTimeout(() => {
          hideToast(toast.id);
        }, toast.duration);
        timers.push(timer);
      }
    });
    
    return () => timers.forEach(timer => clearTimeout(timer));
  }, [toasts, hideToast]);

  return (
    <ToastContext.Provider value={{ 
      showToast, 
      hideToast, 
      toasts, 
      history, 
      markAsRead, 
      markAllAsRead,
      clearHistory
    }}>
      {children}
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
        {toasts.map(toast => (
          <div
            key={toast.id}
            className={`max-w-md py-3 px-4 rounded-md shadow-lg flex items-center justify-between transition-all duration-300 ease-in-out ${
              toast.type === 'success' ? 'bg-green-500' :
              toast.type === 'error' ? 'bg-red-500' :
              toast.type === 'warning' ? 'bg-yellow-500' :
              'bg-blue-500'
            } text-white`}
          >
            <p>{toast.message}</p>
            <button
              onClick={() => hideToast(toast.id)}
              className="ml-4 text-white hover:text-gray-200"
            >
              <span className="sr-only">关闭</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
              </svg>
            </button>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}; 