import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { LoadingSpinner } from './LoadingSpinner';

interface DataLoadingIndicatorProps {
  isLoading: boolean;
  label?: string;
  className?: string;
  children: React.ReactNode;
  overlay?: boolean;
}

/**
 * 数据加载指示器组件
 * 在数据加载过程中显示加载指示器，加载完成后显示子组件
 */
export const DataLoadingIndicator: React.FC<DataLoadingIndicatorProps> = ({
  isLoading,
  label = '加载中...',
  className = '',
  children,
  overlay = false
}) => {
  const { theme } = useTheme();
  
  // 如果不是加载状态，直接显示子组件
  if (!isLoading) {
    return <>{children}</>;
  }
  
  // 如果是覆盖模式，显示半透明背景和加载指示器
  if (overlay) {
    return (
      <div className={`relative ${className}`}>
        {children}
        <div className={`absolute inset-0 flex items-center justify-center ${theme === 'dark' ? 'bg-gray-900/70' : 'bg-white/70'} z-10`}>
          <LoadingSpinner size="md" label={label} />
        </div>
      </div>
    );
  }
  
  // 默认模式，只显示加载指示器
  return (
    <div className={`flex items-center justify-center p-4 ${className}`}>
      <LoadingSpinner size="md" label={label} />
    </div>
  );
};

export default DataLoadingIndicator;
