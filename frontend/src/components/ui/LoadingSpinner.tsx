import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  color?: string;
  label?: string;
}

/**
 * 通用加载指示器组件
 * 可以自定义大小、颜色和标签
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = '',
  color,
  label
}) => {
  const { theme } = useTheme();
  
  // 根据尺寸确定样式
  const sizeClasses = {
    sm: 'w-4 h-4 border-2',
    md: 'w-8 h-8 border-2',
    lg: 'w-12 h-12 border-3'
  };
  
  // 根据主题和传入的颜色确定颜色
  const spinnerColor = color || (theme === 'dark' ? 'border-blue-500' : 'border-blue-400');
  
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div 
        className={`${sizeClasses[size]} ${spinnerColor} border-t-transparent rounded-full animate-spin`}
        role="status"
        aria-label="加载中"
      />
      {label && (
        <span className={`mt-2 text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
          {label}
        </span>
      )}
    </div>
  );
};

export default LoadingSpinner;
