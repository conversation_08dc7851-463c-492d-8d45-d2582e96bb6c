import { ReactNode } from 'react';
import Footer from './Footer';
import Header from './Header';

type LayoutProps = {
  children: ReactNode;
  hideHeader?: boolean;
  hideFooter?: boolean;
  className?: string;
  withContainer?: boolean; // 是否使用内容容器
  containerClassName?: string; // 容器额外的类名
  title?: string; // 页面标题
  actions?: ReactNode; // 标题栏右侧的操作按钮
};

const Layout = ({
  children,
  hideHeader = false,
  hideFooter = false,
  className = '',
  withContainer = false,
  containerClassName = '',
  title,
  actions
}: LayoutProps) => {

  return (
    <div className={`flex flex-col min-h-screen bg-main text-primary ${className}`}>
      {!hideHeader && <Header />}

      <main className="flex-grow w-full overflow-auto">
        {withContainer ? (
          <div className="py-6">
            <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ${containerClassName}`}>
              {/* 页面标题 */}
              {title && (
                <div className="flex justify-between items-center mb-8">
                  <h1 className="text-3xl font-bold text-primary">{title}</h1>
                  {actions && <div className="flex items-center">{actions}</div>}
                </div>
              )}

              {children}
            </div>
          </div>
        ) : (
          children
        )}
      </main>

      {!hideFooter && <Footer />}
    </div>
  );
};

export default Layout;