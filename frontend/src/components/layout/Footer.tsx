const Footer = () => {
  const currentYear = new Date().getFullYear();

  // 根据主题定义样式
  const bgClass = 'bg-background-secondary';
  const textClass = 'text-text-primary';
  const accentBgClass = 'bg-secondary';
  const linkClass = 'text-text-secondary hover:text-primary text-sm transition-all duration-300';
  const headingClass = 'text-sm font-semibold text-text-primary mb-2 transition-all duration-300';
  const copyrightClass = 'block mt-1 text-sm text-text-secondary transition-all duration-300';

  return (
    <footer className={`${bgClass} ${textClass} transition-all duration-300 ease-in-out h-auto z-10 shadow-lg`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center max-h-40 transition-all duration-300">
          <div className="py-1 mb-0 md:mb-0 opacity-100 transition-all duration-300">
            <div className="flex items-center">
              <div className={`w-6 h-6 ${accentBgClass} rounded-full mr-2 flex items-center justify-center transition-all duration-300`}>
                <div className="w-3 h-3 bg-text-primary transform rotate-45 transition-all duration-300"></div>
              </div>
              <span className="text-lg font-semibold text-text-primary transition-all duration-300">智能浮标监测系统</span>
            </div>
            <p className={copyrightClass}>
              &copy; {currentYear} 瀚界科技
            </p>
          </div>

          <div className="flex flex-wrap justify-center transition-all duration-500">
            <div className="px-4 py-2 transition-all duration-300">
              <h3 className={headingClass}>链接</h3>
              <ul className="space-y-2 transition-all duration-300">
                <li>
                  <a href="https://hanjie-tech.cn/" target="_blank" rel="noopener noreferrer" className={linkClass}>
                    上海瀚界科技发展有限公司
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;