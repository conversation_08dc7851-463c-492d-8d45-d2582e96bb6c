import { Link } from 'react-router-dom';
import { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { ThemeToggle } from './ThemeToggle';
import { ConditionalWebSocketStatusIndicator } from '../../components/websocket';
import { FeedbackForm } from '../feedback';

const Header = () => {
  const { user, isAuthenticated } = useAuth();
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);

  // 根据主题使用变量而不是硬编码的颜色
  const bgClass = 'bg-background-secondary';
  const textClass = 'text-text-primary';
  const linkHoverClass = 'hover:bg-primary hover:bg-opacity-20';
  const linkTextClass = 'text-text-primary';

  return (
    <header className={`${bgClass} ${textClass} shadow-lg h-10 hover:h-16 transition-all duration-300 ease-in-out group`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-10 group-hover:h-16 items-center transition-all duration-300">
          <div className="flex items-center">
            <div className="w-6 h-6 group-hover:w-8 group-hover:h-8 bg-secondary rounded-full mr-2 flex items-center justify-center transition-all duration-300">
              <div className="w-3 h-3 group-hover:w-4 group-hover:h-4 bg-text-primary transform rotate-45 transition-all duration-300"></div>
            </div>
            <Link to="/" className="text-xl group-hover:text-2xl font-bold text-text-primary hover:text-primary transition-all duration-300">智能浮标监测系统</Link>
          </div>

          <div className="flex items-center">
            <nav className="flex space-x-2 group-hover:space-x-4 transition-all duration-300">
              {isAuthenticated ? (
                <>
                  <Link to="/home" className={`${linkTextClass} ${linkHoverClass} hover:text-primary px-2 py-1 group-hover:px-3 group-hover:py-2 rounded-md transition-all duration-300 text-sm group-hover:text-base`}>
                    首页
                  </Link>

                  {user?.role === 'admin' && (
                    <Link to="/admin" className={`${linkTextClass} ${linkHoverClass} hover:text-primary px-2 py-1 group-hover:px-3 group-hover:py-2 rounded-md transition-all duration-300 text-sm group-hover:text-base`}>
                      管理系统
                    </Link>
                  )}

                  <Link to="/account" className={`${linkTextClass} ${linkHoverClass} hover:text-primary px-2 py-1 group-hover:px-3 group-hover:py-2 rounded-md transition-all duration-300 text-sm group-hover:text-base`}>
                    我的账户
                  </Link>

                  <button
                    onClick={() => setShowFeedbackForm(true)}
                    className={`${linkTextClass} ${linkHoverClass} hover:text-primary px-2 py-1 group-hover:px-3 group-hover:py-2 rounded-md transition-all duration-300 text-sm group-hover:text-base`}
                  >
                    用户反馈
                  </button>
                </>
              ) : (
                <Link to="/auth" className={`${linkTextClass} ${linkHoverClass} hover:text-primary px-2 py-1 group-hover:px-3 group-hover:py-2 rounded-md transition-all duration-300 text-sm group-hover:text-base`}>
                  登录/注册
                </Link>
              )}
            </nav>

            {/* 条件版WebSocket状态指示器 - 只在WebSocketProvider存在时显示 */}
            {isAuthenticated && (
              <div className="ml-4">
                <ConditionalWebSocketStatusIndicator showLabel={false} />
              </div>
            )}

            {/* 主题切换按钮 */}
            <div className="ml-4">
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      {/* 反馈表单 */}
      <FeedbackForm
        isOpen={showFeedbackForm}
        onClose={() => setShowFeedbackForm(false)}
      />
    </header>
  );
};

export default Header;