import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useToast } from '../ui/Toast';
import { 
  feedbackService, 
  Feedback, 
  FeedbackStatus, 
  getStatusText, 
  getTypeText,
  FEEDBACK_STATUS_OPTIONS 
} from '../../api/feedbackService';

const MyFeedbackList: React.FC = () => {
  const { theme } = useTheme();
  const { showToast } = useToast();
  
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState<FeedbackStatus | ''>('');

  // 主题颜色
  const colors = {
    background: {
      main: theme === 'dark' ? '#1f2937' : '#ffffff',
      secondary: theme === 'dark' ? '#374151' : '#f9fafb',
    },
    text: {
      primary: theme === 'dark' ? '#f9fafb' : '#111827',
      secondary: theme === 'dark' ? '#d1d5db' : '#6b7280',
    },
    border: theme === 'dark' ? '#4b5563' : '#d1d5db',
  };

  // 状态颜色
  const getStatusColor = (status: FeedbackStatus) => {
    switch (status) {
      case FeedbackStatus.PENDING:
        return 'text-yellow-600 bg-yellow-100';
      case FeedbackStatus.PROCESSING:
        return 'text-blue-600 bg-blue-100';
      case FeedbackStatus.RESOLVED:
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const loadFeedbacks = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        size: 10,
        ...(statusFilter && { status: statusFilter as FeedbackStatus })
      };
      
      const response = await feedbackService.getMyFeedback(params);
      setFeedbacks(response.items);
      setTotalPages(response.pages);
    } catch (error) {
      console.error('加载反馈列表失败:', error);
      showToast('加载反馈列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFeedbacks();
  }, [currentPage, statusFilter]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 筛选器 */}
      <div className="flex items-center space-x-4">
        <label className="text-sm font-medium" style={{ color: colors.text.primary }}>
          状态筛选：
        </label>
        <select
          value={statusFilter}
          onChange={(e) => {
            setStatusFilter(e.target.value as FeedbackStatus | '');
            setCurrentPage(1);
          }}
          className="px-3 py-1 rounded-md border text-sm"
          style={{
            backgroundColor: colors.background.main,
            borderColor: colors.border,
            color: colors.text.primary
          }}
        >
          <option value="">全部</option>
          {FEEDBACK_STATUS_OPTIONS.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* 反馈列表 */}
      {feedbacks.length === 0 ? (
        <div className="text-center py-8" style={{ color: colors.text.secondary }}>
          暂无反馈记录
        </div>
      ) : (
        <div className="space-y-4">
          {feedbacks.map((feedback) => (
            <div
              key={feedback.id}
              className="border rounded-lg p-4"
              style={{
                backgroundColor: colors.background.main,
                borderColor: colors.border
              }}
            >
              {/* 头部信息 */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span
                    className="px-2 py-1 rounded-full text-xs font-medium"
                    style={{ color: colors.text.secondary }}
                  >
                    {getTypeText(feedback.type)}
                  </span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(feedback.status)}`}
                  >
                    {getStatusText(feedback.status)}
                  </span>
                </div>
                <span className="text-xs" style={{ color: colors.text.secondary }}>
                  {formatDate(feedback.created_at)}
                </span>
              </div>

              {/* 反馈内容 */}
              <div className="mb-3">
                <p style={{ color: colors.text.primary }}>
                  {feedback.content}
                </p>
              </div>

              {/* 管理员回复 */}
              {feedback.reply && (
                <div
                  className="mt-3 p-3 rounded-md"
                  style={{ backgroundColor: colors.background.secondary }}
                >
                  <div className="text-sm font-medium mb-1" style={{ color: colors.text.primary }}>
                    管理员回复：
                  </div>
                  <p className="text-sm" style={{ color: colors.text.secondary }}>
                    {feedback.reply}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 rounded-md border disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              backgroundColor: colors.background.main,
              borderColor: colors.border,
              color: colors.text.primary
            }}
          >
            上一页
          </button>
          
          <span className="text-sm" style={{ color: colors.text.secondary }}>
            第 {currentPage} 页，共 {totalPages} 页
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 rounded-md border disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              backgroundColor: colors.background.main,
              borderColor: colors.border,
              color: colors.text.primary
            }}
          >
            下一页
          </button>
        </div>
      )}
    </div>
  );
};

export default MyFeedbackList;
