import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../ui/Toast';
import { feedbackService, FeedbackType, FEEDBACK_TYPE_OPTIONS, FeedbackCreateRequest } from '../../api/feedbackService';

interface FeedbackFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const FeedbackForm: React.FC<FeedbackFormProps> = ({ isOpen, onClose, onSuccess }) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const { showToast } = useToast();
  
  const [formData, setFormData] = useState<FeedbackCreateRequest>({
    type: FeedbackType.OTHER,
    content: '',
    contact: user?.email || ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 主题颜色
  const colors = {
    background: {
      main: theme === 'dark' ? '#1f2937' : '#ffffff',
      secondary: theme === 'dark' ? '#374151' : '#f9fafb',
    },
    text: {
      primary: theme === 'dark' ? '#f9fafb' : '#111827',
      secondary: theme === 'dark' ? '#d1d5db' : '#6b7280',
    },
    border: theme === 'dark' ? '#4b5563' : '#d1d5db',
    input: {
      background: theme === 'dark' ? '#374151' : '#ffffff',
      border: theme === 'dark' ? '#4b5563' : '#d1d5db',
      focus: theme === 'dark' ? '#3b82f6' : '#2563eb',
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.content.trim()) {
      showToast('请填写反馈内容', 'error');
      return;
    }

    setIsSubmitting(true);
    try {
      await feedbackService.createFeedback(formData);
      showToast('反馈提交成功，感谢您的建议！', 'success');
      
      // 重置表单
      setFormData({
        type: FeedbackType.OTHER,
        content: '',
        contact: user?.email || ''
      });
      
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('提交反馈失败:', error);
      showToast('提交反馈失败，请稍后重试', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: keyof FeedbackCreateRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div
        className="w-full max-w-md rounded-lg shadow-xl"
        style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: colors.border }}>
          <h3 className="text-lg font-medium" style={{ color: colors.text.primary }}>
            用户反馈
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isSubmitting}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* 反馈类型 */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: colors.text.primary }}>
              反馈类型
            </label>
            <select
              value={formData.type}
              onChange={(e) => handleChange('type', e.target.value)}
              className="w-full px-3 py-2 rounded-md border focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{
                backgroundColor: colors.input.background,
                borderColor: colors.input.border,
                color: colors.text.primary
              }}
              disabled={isSubmitting}
            >
              {FEEDBACK_TYPE_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 联系方式 */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: colors.text.primary }}>
              联系方式 <span style={{ color: colors.text.secondary }}>(可选)</span>
            </label>
            <input
              type="text"
              value={formData.contact || ''}
              onChange={(e) => handleChange('contact', e.target.value)}
              placeholder="邮箱或其他联系方式"
              className="w-full px-3 py-2 rounded-md border focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{
                backgroundColor: colors.input.background,
                borderColor: colors.input.border,
                color: colors.text.primary
              }}
              disabled={isSubmitting}
            />
          </div>

          {/* 反馈内容 */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: colors.text.primary }}>
              反馈内容 <span className="text-red-500">*</span>
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => handleChange('content', e.target.value)}
              placeholder="请详细描述您的建议、问题或遇到的BUG..."
              rows={5}
              className="w-full px-3 py-2 rounded-md border focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              style={{
                backgroundColor: colors.input.background,
                borderColor: colors.input.border,
                color: colors.text.primary
              }}
              disabled={isSubmitting}
              maxLength={2000}
            />
            <div className="text-xs mt-1" style={{ color: colors.text.secondary }}>
              {formData.content.length}/2000
            </div>
          </div>

          {/* 按钮 */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border rounded-md font-medium transition-colors"
              style={{
                borderColor: colors.border,
                color: colors.text.secondary,
                backgroundColor: 'transparent'
              }}
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              disabled={isSubmitting || !formData.content.trim()}
            >
              {isSubmitting ? '提交中...' : '提交反馈'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FeedbackForm;
