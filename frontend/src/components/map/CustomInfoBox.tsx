import { useState, useEffect, useRef } from 'react';
import * as Cesium from 'cesium';
import { Rnd, RndResizeCallback, RndDragCallback } from 'react-rnd';
import { flyToBuoy } from '../../utils/cesium';
import { useBuoyContext } from '../../contexts/BuoyContext';
import {
  InfoBoxHeader,
  BuoyInfoSection,
  InfoBoxTabs,
  InfoBoxContent
} from './infobox';
import {
  useContainerSize,
  usePositionAndMode
} from './infobox/hooks';
/* import { useToast } from '../../components/ui'; */

interface CustomInfoBoxProps {
  viewer: Cesium.Viewer | null;
  displayMode?: 'free' | 'left' | 'right'; // 更新显示模式类型
  isLocked?: boolean; // 添加外部传入的锁定状态
}

const CustomInfoBox: React.FC<CustomInfoBoxProps> = ({
  viewer,
  displayMode: initialMode = 'right', // 默认吸附右侧
  isLocked: externalLocked = false, // 从外部传入的锁定状态
}) => {
  // 使用Context获取所有需要的数据
  const {
    selectedBuoyId,
    buoys,
    setSelectedBuoyId,
    sensorData,
    locationHistory,
    loadingSensorData,
    loadingLocationHistory,
    showHistoryTrack,
    setShowHistoryTrack
  } = useBuoyContext();

  // 从Context中找到选中的浮标
  const selectedBuoy = selectedBuoyId ? buoys.find(b => b.id === selectedBuoyId) || null : null;
  const isVisible = !!selectedBuoyId;

  const [activeTab, setActiveTab] = useState<'sensor' | 'location' | 'control'>('sensor');
  const [isLocked, setIsLocked] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);
  // const { showToast } = useToast();

  // 使用自定义hooks获取容器尺寸和位置模式
  const containerSize = useContainerSize(containerRef, viewer);
  const {
    mode, setMode,
    size, setSize,
    position, setPosition
  } = usePositionAndMode(containerSize, initialMode);

  // 当外部锁定状态变化时，同步到内部状态
  useEffect(() => {
    setIsLocked(externalLocked);
  }, [externalLocked]);

  // 当浮标改变时重置为第一个标签
  useEffect(() => {
    if (selectedBuoy) {
      setActiveTab('sensor');
    }
  }, [selectedBuoy?.id]);

  // 切换显示模式
  const toggleDisplayMode = () => {
    if (mode === 'free') {
      setMode('left');
    } else if (mode === 'left') {
      setMode('right');
    } else if (mode === 'right') {
      setMode('free');
    }
  };

  // 如果没有浮标数据或不可见，不渲染组件
  if (!selectedBuoy || !isVisible) return null;

  // 聚焦到浮标
  const handleFocus = () => {
    if (viewer && selectedBuoy) {
      // 如果浮标已锁定，先解除锁定再进行飞行
      if (isLocked) {
        try {
          viewer.trackedEntity = undefined;
          setIsLocked(false);
        } catch (e) {
          console.error('解除浮标跟踪时出错:', e);
        }
      }

      // 添加延时以确保解除锁定完全生效
      setTimeout(() => {
        try {
          flyToBuoy(viewer, selectedBuoy);
        } catch (e) {
          console.error('飞行到浮标时出错:', e);
          // 如果飞行失败，尝试重置视图
          viewer.scene.camera.setView({
            destination: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90),
          });
        }
      }, 100);
    }
  };

  // 处理拖动开始
  const handleDragStart: RndDragCallback = () => {
    // 确保只有在用户实际拖动（不是点击按钮）时才切换模式
    // 检查事件对象，确保是鼠标拖动而不是按钮点击
    // 已移除未使用参数
    if (mode === 'left' || mode === 'right') {
      setMode('free');
    }
  };

  // 切换锁定浮标视角
  const handleLock = () => {
    if (viewer && selectedBuoy) {
      try {
        const buoyEntity = viewer.entities.getById(selectedBuoy.id.toString());
        if (buoyEntity) {
          // 如果要锁定浮标，确保视图处于稳定状态
          if (!isLocked) {
            // 先中止任何进行中的飞行
            viewer.camera.cancelFlight();
            // 短暂延迟后再设置跟踪实体
            setTimeout(() => {
              try {
                viewer.trackedEntity = buoyEntity;
                setIsLocked(true);
              } catch (e) {
                console.error('锁定浮标时出错:', e);
              }
            }, 50);
          } else {
            // 解除锁定
            viewer.trackedEntity = undefined;
            setIsLocked(false);
          }
        }
      } catch (e) {
        console.error('切换锁定状态时出错:', e);
      }
    }
  };

  // 关闭InfoBox
  const handleClose = () => {
    setIsLocked(false); // 关闭InfoBox时取消锁定
    if (viewer) {
      viewer.trackedEntity = undefined; // 确保Cesium Viewer也取消锁定
    }
    setSelectedBuoyId(null); // 直接使用Context中的方法
  };

  // 处理拖动结束
  const handleDragStop: RndDragCallback = (_e, d) => {
    setPosition({ x: d.x, y: d.y });
  };

  // 处理调整大小结束
  const handleResizeStop: RndResizeCallback = (_e, _direction, ref, _delta, position) => {
    setSize({
      width: parseInt(ref.style.width),
      height: parseInt(ref.style.height)
    });
    setPosition(position);
  };

  // 根据显示模式进行不同的渲染
  return (
    <div ref={containerRef} style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1000, pointerEvents: 'none' }}>
      <Rnd
        size={{ width: size.width, height: size.height }}
        position={{ x: position.x, y: position.y }}
        onDragStart={handleDragStart}
        onDragStop={handleDragStop}
        onResizeStop={handleResizeStop}
        disableDragging={false} // 允许拖动，但在 handleDragStart 中进行判断
        enableResizing={mode === 'free' ? {
          bottom: true,
          bottomLeft: true,
          bottomRight: true,
          left: true,
          right: true,
          top: true,
          topLeft: true,
          topRight: true
        } : false}
        className="bg-black/75 text-white p-2 rounded-md shadow-lg border border-gray-800 overflow-auto z-50 scrollbar-thin"
        dragHandleClassName="handle"
        style={{ zIndex: 1000, pointerEvents: 'auto' }}
      >
        {/* 使用InfoBoxHeader子组件 */}
        <InfoBoxHeader
          title={selectedBuoy.name}
          isLocked={isLocked}
          mode={mode}
          onFocus={handleFocus}
          onLock={handleLock}
          onToggleMode={toggleDisplayMode}
          onClose={handleClose}
        />

        {/* 使用BuoyInfoSection子组件 */}
        <BuoyInfoSection buoy={selectedBuoy} />

        {/* 使用InfoBoxTabs子组件 */}
        <InfoBoxTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        
        {/* 历史轨迹显示控制 */}
        <div className="mt-2 p-2 bg-gray-800/50 rounded">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="showHistoryTrack"
              checked={showHistoryTrack}
              onChange={(e) => setShowHistoryTrack(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="showHistoryTrack" className="text-sm">显示历史轨迹</label>
          </div>
        </div>

        {/* 使用InfoBoxContent子组件，传递loading状态和浮标数据 */}
        <InfoBoxContent
          activeTab={activeTab}
          sensorData={sensorData}
          locationHistory={locationHistory}
          buoy={selectedBuoy}
          loadingSensorData={loadingSensorData}
          loadingLocationHistory={loadingLocationHistory}
        />
      </Rnd>
    </div>
  );
};

export default CustomInfoBox;