import React from 'react';

type Mode = 'free' | 'left' | 'right';

interface InfoBoxHeaderProps {
  title: string;
  isLocked: boolean;
  mode: Mode;
  onFocus: () => void;
  onLock: () => void;
  onToggleMode: () => void;
  onClose: () => void;
}

const InfoBoxHeader: React.FC<InfoBoxHeaderProps> = ({ 
  title, 
  isLocked, 
  mode,
  onFocus, 
  onLock, 
  onToggleMode, 
  onClose 
}) => {
  // 获取按钮显示状态的图标和提示文本
  const getButtonIcon = () => {
    if (mode === 'free') {
      return {
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-4">
            <path d="M12 2v20"/>
            <path d="m15 19-3 3-3-3"/>
            <path d="m19 9 3 3-3 3"/>
            <path d="M2 12h20"/>
            <path d="m5 9-3 3 3 3"/>
            <path d="m9 5 3-3 3 3"/>
          </svg>
        ),
        title: "切换到右侧吸附"
      };
    } else if (mode === 'right') {
      return {
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-4">
            <rect width="18" height="18" x="3" y="3" rx="2"/>
            <path d="M15 3v18"/>
          </svg>
        ),
        title: "切换到左侧吸附"
      };
    } else {
      return {
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-4">
          <rect width="18" height="18" x="3" y="3" rx="2"/>
          <path d="M9 3v18"/>
        </svg>
        ),
        title: "切换到自由模式"
      };
    }
  };

  const buttonIcon = getButtonIcon();

  return (
    <div className="flex justify-between items-center mb-4 handle cursor-move">
      <div className="flex">
        <button 
          onClick={onFocus}
          className="text-white hover:text-blue-400 transition-colors mr-2"
          title="聚焦浮标"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-4">
            <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
            <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
            <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
            <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
            <circle cx="12" cy="12" r="1"/>
            <path d="M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0"/>
          </svg>
        </button>
        <button
          onClick={onLock}
          className={`text-white hover:text-blue-400 transition-colors mr-2 ${isLocked ? 'text-red-400' : ''}`}
          title={isLocked ? "取消锁定浮标" : "锁定浮标"}
        >
          {isLocked ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-4">
              <line x1="2" x2="5" y1="12" y2="12"/>
              <line x1="19" x2="22" y1="12" y2="12"/>
              <line x1="12" x2="12" y1="2" y2="5"/>
              <line x1="12" x2="12" y1="19" y2="22"/>
              <circle cx="12" cy="12" r="7"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="size-4">
              <line x1="2" x2="5" y1="12" y2="12"/>
              <line x1="19" x2="22" y1="12" y2="12"/>
              <line x1="12" x2="12" y1="2" y2="5"/>
              <line x1="12" x2="12" y1="19" y2="22"/>
              <path d="M7.11 7.11C5.83 8.39 5 10.1 5 12c0 3.87 3.13 7 7 7 1.9 0 3.61-.83 4.89-2.11"/>
              <path d="M18.71 13.96c.19-.63.29-1.29.29-1.96 0-3.87-3.13-7-7-7-.67 0-1.33.1-1.96.29"/>
              <line x1="2" x2="22" y1="2" y2="22"/>
            </svg>
          )}
        </button>
        <button
          onClick={(e) => {
            // 阻止事件冒泡，防止触发拖拽
            e.stopPropagation();
            console.log(`InfoBoxHeader - 模式切换按钮点击，当前模式: ${mode}`);
            onToggleMode();
          }}
          className="text-white hover:text-blue-400 transition-colors mr-2"
          title={buttonIcon.title}
        >
          {buttonIcon.icon}
        </button>
      </div>
      
      <h2 className="text-sm font-medium truncate flex-1 text-center">{title}</h2>
      
      <button 
        onClick={onClose}
        className="text-white hover:text-red-400 transition-colors"
        title="关闭"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="size-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  );
};

export default InfoBoxHeader; 