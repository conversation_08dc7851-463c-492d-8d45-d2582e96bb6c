import React, { useState, useEffect, useRef } from 'react';
import { Buoy, ControlCommand } from '../../../types';
import { useToast } from '../../ui';
import { LoadingSpinner } from '../../ui';
import { validateControlParams } from '../../../utils/testUtils';
import { useBuoyContext } from '../../../contexts/BuoyContext';

interface BuoyWebSocketControlPanelProps {
  buoy: Buoy;
  onControlSuccess?: () => void;
}

/**
 * 浮标WebSocket控制面板
 * 使用WebSocket发送控制命令并接收响应
 */
const BuoyWebSocketControlPanel: React.FC<BuoyWebSocketControlPanelProps> = ({ buoy, onControlSuccess }) => {
  // 状态管理
  const [brightness, setBrightness] = useState<number>(50);
  const [color, setColor] = useState<string>('#FFFFFF');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [responseMessage, setResponseMessage] = useState<string | null>(null);
  const [currentSettings, setCurrentSettings] = useState<{brightness?: number; color?: string} | null>(null);

  // 使用Toast组件显示消息
  const { showToast } = useToast();

  // 使用BuoyContext获取WebSocket状态和控制响应状态
  const { controlResponse, wsIsConnected, wsSend } = useBuoyContext();

  // 使用ref跟踪已处理的控制响应ID
  const processedResponseRef = useRef<string | null>(null);

  // 处理亮度变化
  const handleBrightnessChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setBrightness(value);
  };

  // 处理颜色变化
  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setColor(e.target.value);
  };

  // 监听控制响应变化
  useEffect(() => {
    // 如果没有控制响应，不处理
    if (!controlResponse) return;

    // 生成响应ID，用于去重
    const responseId = `${controlResponse.type}-${controlResponse.buoyId}-${controlResponse.timestamp || Date.now()}`;

    // 如果已经处理过这个响应，跳过
    if (processedResponseRef.current === responseId) {
      return;
    }

    // 记录已处理的响应ID
    processedResponseRef.current = responseId;

    // 显示响应消息
    if (controlResponse.message) {
      setResponseMessage(controlResponse.message);
      showToast(
        controlResponse.message,
        controlResponse.status === 'success' ? 'success' : 'error'
      );
    }

    // 更新当前设置
    if (controlResponse.current_settings) {
      setCurrentSettings(controlResponse.current_settings);
    }

    // 设置成功状态
    if (controlResponse.status === 'success') {
      setSuccess(true);

      // 调用成功回调
      if (onControlSuccess) {
        onControlSuccess();
      }
    } else {
      setError(controlResponse.message || '控制命令执行失败');
    }

    // 结束加载状态
    setIsLoading(false);

  }, [controlResponse, showToast, onControlSuccess]);

  // 重置成功状态的计时器
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (success) {
      timer = setTimeout(() => {
        setSuccess(false);
        setResponseMessage(null);
      }, 5000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [success]);

  // 发送控制指令
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 检查WebSocket连接状态
    if (!wsIsConnected()) {
      setError('WebSocket未连接，请先连接WebSocket');
      showToast('WebSocket未连接，请先连接WebSocket', 'error');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);
    setResponseMessage(null);

    try {
      // 前端验证参数
      const validation = validateControlParams(brightness, color);
      if (!validation.valid) {
        throw new Error(validation.message);
      }

      // 构造控制指令
      const command: ControlCommand = {
        command: 'set_light',
        brightness: brightness,
        color: color
      };

      // 构造控制主题
      const topic = `/topic/buoys/${buoy.id}/control`;

      // 发送WebSocket控制指令
      wsSend(topic, command);

      // 显示发送成功消息
      showToast('控制指令已发送，等待响应...', 'info');

      // 注意：不在这里设置success，而是等待响应
      // 控制响应将通过BuoyContext中的controlResponse状态传递
    } catch (err) {
      console.error('发送控制指令失败:', err);
      // 显示具体的错误信息
      const errorMessage = err instanceof Error ? err.message : '发送控制指令失败，请稍后重试';
      setError(errorMessage);
      showToast(errorMessage, 'error');
      setIsLoading(false);
    }
  };

  return (
    <div className="p-2">
      <h3 className="text-lg font-medium mb-4 text-white">浮标控制面板</h3>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* 亮度控制 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-white">
            亮度: {brightness}%
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={brightness}
            onChange={handleBrightnessChange}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-200">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </div>

        {/* 颜色控制 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-white">
            颜色:
          </label>
          <div className="flex items-center space-x-3">
            <input
              type="color"
              value={color}
              onChange={handleColorChange}
              className="h-8 w-8 rounded cursor-pointer"
            />
            <span className="text-sm text-white">{color}</span>
          </div>
        </div>

        {/* 提交按钮 */}
        <div className="pt-2">
          <button
            type="submit"
            disabled={isLoading || !wsIsConnected()}
            className={`w-full py-2 px-4 rounded-md ${
              isLoading || !wsIsConnected()
                ? 'bg-blue-700 cursor-not-allowed opacity-70'
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white font-medium transition-colors`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <LoadingSpinner size="sm" />
                <span className="ml-2">发送中...</span>
              </div>
            ) : !wsIsConnected() ? (
              'WebSocket未连接'
            ) : (
              '发送控制指令'
            )}
          </button>
        </div>
      </form>

      {/* 错误提示 */}
      {error && (
        <div className="mt-4 p-2 bg-red-900/70 text-white rounded-md text-sm">
          <span className="font-medium">错误：</span> {error}
        </div>
      )}

      {/* 成功提示 */}
      {success && (
        <div className="mt-4 p-2 bg-green-900/70 text-white rounded-md text-sm">
          <span className="font-medium">成功：</span> {responseMessage || '控制指令已成功执行'}
        </div>
      )}

      {/* 当前设置 */}
      {currentSettings && (
        <div className="mt-4 p-2 bg-blue-900/70 text-white rounded-md text-sm">
          <p className="font-medium">当前浮标设置:</p>
          <ul className="mt-1 space-y-1">
            {currentSettings.brightness !== undefined && (
              <li>亮度: {currentSettings.brightness}%</li>
            )}
            {currentSettings.color && (
              <li className="flex items-center">
                颜色: {currentSettings.color}
                <span
                  className="ml-2 w-4 h-4 rounded-full inline-block border border-white"
                  style={{ backgroundColor: currentSettings.color }}
                />
              </li>
            )}
          </ul>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-6 text-xs text-gray-200">
        <p className="font-medium">说明：</p>
        <ul className="list-disc pl-5 space-y-1 mt-1">
          <li>亮度范围：0-100%</li>
          <li>颜色使用十六进制格式（如 #FF8C00）</li>
          <li>控制指令将通过WebSocket发送到浮标</li>
          <li>浮标执行后会返回实时响应</li>
        </ul>
      </div>
    </div>
  );
};

export default BuoyWebSocketControlPanel;
