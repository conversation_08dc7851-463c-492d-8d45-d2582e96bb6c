import React from 'react';
import BuoyDataChart from '../../visualizations/BuoyDataChart';
import BuoyLocation<PERSON>hart from '../../visualizations/BuoyLocationChart';
import BuoyWebSocketControlPanel from './BuoyWebSocketControlPanel';
import { LoadingSpinner } from '../../ui';
import { SensorData, GeoPoint, Buoy } from '../../../types';

interface InfoBoxContentProps {
  activeTab: 'sensor' | 'location' | 'control';
  sensorData: SensorData[];
  locationHistory: GeoPoint[];
  buoy: Buoy;
  loadingSensorData?: boolean;
  loadingLocationHistory?: boolean;
  onControlSuccess?: () => void;
}

const InfoBoxContent: React.FC<InfoBoxContentProps> = ({
  activeTab,
  sensorData,
  locationHistory,
  buoy,
  loadingSensorData = false,
  loadingLocationHistory = false,
  onControlSuccess
}) => {
  return (
    <div className="h-64">
      {activeTab === 'sensor' && (
        loadingSensorData ? (
          <div className="flex justify-center items-center h-full">
            <LoadingSpinner size="md" label="加载传感器数据..." />
          </div>
        ) : (
          <BuoyDataChart
            sensorData={sensorData}
            chartType="sensor"
            height="100%"
            title=""
            inCesiumInfoBox={true}
          />
        )
      )}
      {activeTab === 'location' && (
        loadingLocationHistory ? (
          <div className="flex justify-center items-center h-full">
            <LoadingSpinner size="md" label="加载轨迹数据..." />
          </div>
        ) : (
          <BuoyLocationChart
            locationHistory={locationHistory}
            width="100%"
            title=""
            inCesiumInfoBox={true}
          />
        )
      )}
      {activeTab === 'control' && (
        <BuoyWebSocketControlPanel
          buoy={buoy}
          onControlSuccess={onControlSuccess}
        />
      )}
    </div>
  );
};

export default InfoBoxContent;