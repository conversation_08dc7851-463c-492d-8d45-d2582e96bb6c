import { useState, useEffect } from 'react';

interface ContainerSize {
  width: number;
  height: number;
}

interface Size {
  width: number;
  height: number;
}

interface Position {
  x: number;
  y: number;
}

type Mode = 'left' | 'right' | 'free';

interface PositionAndModeResult {
  mode: Mode;
  setMode: (mode: Mode) => void;
  size: Size;
  setSize: (size: Size) => void;
  position: Position;
  setPosition: (position: Position) => void;
}

export const usePositionAndMode = (
  containerSize: ContainerSize,
  initialMode: Mode = 'right'
): PositionAndModeResult => {
  const [mode, _setMode] = useState<Mode>(initialMode);

  // 包装 setMode 函数，添加日志
  const setMode = (newMode: Mode) => {
    // console.log(`usePositionAndMode - 设置模式从 ${mode} 到 ${newMode}`);
    _setMode(newMode);
  };

  const [size, setSize] = useState<Size>({
    width: 320, // 默认宽度
    height: 600, // 默认高度
  });
  const [position, setPosition] = useState<Position>({
    x: 50,
    y: 50
  });

  // 在容器尺寸和显示模式变化时更新位置和大小
  useEffect(() => {
    // 确保容器尺寸已获取，如果没有获取到，不执行任何操作
    if (containerSize.width === 0 || containerSize.height === 0) {
      return;
    }

    // 只在非自由模式下更新位置和大小
    if (mode !== 'free') {
      // 吸附模式下计算位置（左侧或右侧）
      const dockHeight = containerSize.height * 0.75;
      const centerY = (containerSize.height - dockHeight) / 2;

      if (mode === 'right') {
        setPosition({
          x: containerSize.width - 340, // 320px + 20px margin
          y: centerY
        });
      } else if (mode === 'left') {
        setPosition({
          x: 20, // 20px margin
          y: centerY
        });
      }

      setSize({
        width: 320,
        height: dockHeight
      });
    }
  }, [containerSize, mode]);

  // 响应模式变化的逻辑
  useEffect(() => {
    // 当容器尺寸改变时，适应显示模式
    const updatePositionAndSize = () => {
      if (containerSize.width === 0 || containerSize.height === 0) {
        return;
      }

      // 只在吸附模式下更新位置和大小
      if (mode === 'left' || mode === 'right') {
        // 计算吸附模式下的高度 (75% 的容器高度)
        const dockHeight = containerSize.height * 0.75;
        setSize({
          width: 320,
          height: dockHeight
        });

        // 计算垂直居中的位置
        const centerY = (containerSize.height - dockHeight) / 2;

        if (mode === 'right') {
          setPosition({
            x: containerSize.width - 340, // 320px + 20px margin
            y: centerY
          });
        } else {
          setPosition({
            x: 20, // 20px margin
            y: centerY
          });
        }
      }
    };

    // 调整位置和大小，但仅在模式变化时
    // 这样可以确保在free模式下不会因为容器尺寸变化而重置大小
    updatePositionAndSize();
  }, [mode, containerSize]);

  return {
    mode,
    setMode,
    size,
    setSize,
    position,
    setPosition
  };
};