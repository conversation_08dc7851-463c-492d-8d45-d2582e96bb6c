import { useState, useEffect, RefObject } from 'react';
import * as Cesium from 'cesium';

interface ContainerSize {
  width: number;
  height: number;
}

export const useContainerSize = (
  containerRef: RefObject<HTMLDivElement | null>,
  viewer: Cesium.Viewer | null
): ContainerSize => {
  const [containerSize, setContainerSize] = useState<ContainerSize>({ width: 0, height: 0 });

  useEffect(() => {
    const updateContainerSize = () => {
      if (containerRef.current) {
        // 尝试获取容器尺寸
        const rect = containerRef.current.getBoundingClientRect();
        
        // 如果容器尺寸为0，尝试从viewer获取尺寸
        if (rect.width === 0 || rect.height === 0) {
          if (viewer && viewer.container) {
            const viewerRect = viewer.container.getBoundingClientRect();
            setContainerSize({ 
              width: viewerRect.width, 
              height: viewerRect.height 
            });
            return;
          }
          
          // 如果都失败，使用window尺寸作为后备
          if (rect.width === 0 || rect.height === 0) {
            setContainerSize({ 
              width: window.innerWidth, 
              height: window.innerHeight 
            });
            return;
          }
        }
        
        setContainerSize({ 
          width: rect.width, 
          height: rect.height 
        });
      }
    };
    
    // 立即运行一次
    updateContainerSize();
    
    // 等待DOM完全加载后再次运行
    const timeout = setTimeout(updateContainerSize, 100);
    
    // 创建ResizeObserver监听父容器尺寸变化
    let resizeObserver: ResizeObserver | null = null;
    try {
      resizeObserver = new ResizeObserver(() => {
        updateContainerSize();
      });
      
      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
      }
      
      // 同时监听window尺寸变化作为备用
      window.addEventListener('resize', updateContainerSize);
    } catch (e) {
      console.error("ResizeObserver错误:", e);
      // 如果ResizeObserver不可用，回退到window resize事件
      window.addEventListener('resize', updateContainerSize);
    }
    
    return () => {
      clearTimeout(timeout);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      window.removeEventListener('resize', updateContainerSize);
    };
  }, [viewer, containerRef]);

  return containerSize;
}; 