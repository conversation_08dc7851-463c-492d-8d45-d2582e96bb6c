import React from 'react';
import { formatUTCToLocal } from '../../../utils/dateUtils';

interface BuoyType {
  id: string | number;
  name: string;
  description: string;
  status: string;
  location: {
    latitude: number;
    longitude: number;
  };
  last_heartbeat: string | null;
}

interface BuoyInfoSectionProps {
  buoy: BuoyType;
}

const BuoyInfoSection: React.FC<BuoyInfoSectionProps> = ({ buoy }) => {
  // 获取浮标状态样式
  const getStatusStyle = (status: string): string => {
    switch (status) {
      case 'active':
        return 'text-green-500';
      case 'inactive':
        return 'text-gray-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-blue-500';
    }
  };

  // 获取浮标状态文本
  const getStatusText = (status: string): string => {
    switch (status) {
      case 'active':
        return '在线';
      case 'inactive':
        return '离线';
      case 'error':
        return '故障';
      default:
        return '未知';
    }
  };

  // 使用工具函数格式化UTC时间戳为本地时间

  return (
    <>
      {/* 浮标描述 */}
      <div className="mb-4">
        <p className="text-sm mb-2">{buoy.description}</p>
      </div>

      {/* 浮标基本信息 */}
      <div className="mb-4 space-y-2 text-sm">
        <div className="flex items-center">
          <span className="font-medium mr-2">状态:</span>
          <span className={getStatusStyle(buoy.status)}>
            {getStatusText(buoy.status)}
          </span>
        </div>
        <div>
          <span className="font-medium mr-2">位置:</span>
          <span>经度 {buoy.location.longitude.toFixed(5)}°, 纬度 {buoy.location.latitude.toFixed(5)}°</span>
        </div>
        <div>
          <span className="font-medium mr-2">最后心跳:</span>
          <span>{formatUTCToLocal(buoy.last_heartbeat)}</span>
        </div>
      </div>

      {/* 分隔线 */}
      <hr className="border-gray-700 my-1" />
    </>
  );
};

export default BuoyInfoSection;