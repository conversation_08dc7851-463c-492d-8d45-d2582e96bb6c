import React from 'react';

interface InfoBoxTabsProps {
  activeTab: 'sensor' | 'location' | 'control';
  onTabChange: (tab: 'sensor' | 'location' | 'control') => void;
}

const InfoBoxTabs: React.FC<InfoBoxTabsProps> = ({ activeTab, onTabChange }) => {
  return (
    <div className="flex mb-4 border-b border-gray-700 justify-center">
      <button
        className={`text-sm py-2 px-4 mx-2 ${activeTab === 'sensor' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400'}`}
        onClick={() => onTabChange('sensor')}
      >
        传感器数据
      </button>
      <button
        className={`text-sm py-2 px-4 mx-2 ${activeTab === 'location' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400'}`}
        onClick={() => onTabChange('location')}
      >
        位置轨迹
      </button>
      <button
        className={`text-sm py-2 px-4 mx-2 ${activeTab === 'control' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400'}`}
        onClick={() => onTabChange('control')}
      >
        控制面板
      </button>
    </div>
  );
};

export default InfoBoxTabs;