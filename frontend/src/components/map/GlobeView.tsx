import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import * as Cesium from 'cesium';
import 'cesium/Build/Cesium/Widgets/widgets.css';
import { initCesium } from '../../utils/cesium';
import { Buoy, GeoPoint } from '../../types';
import { loadBuoysToMap } from '../../utils/cesium';
import CustomInfoBox from './CustomInfoBox';
import { useBuoyContext } from '../../contexts/BuoyContext';

interface GlobViewProps {
  buoys?: Buoy[];
  onBuoySelect?: (buoyId: string | null) => void;
  infoBoxMode?: 'free' | 'left' | 'right';
}

const GlobView: React.FC<GlobViewProps> = ({ 
  buoys: propBuoys, 
  onBuoySelect: propOnBuoySelect,
  infoBoxMode = 'left'
}) => {
  // 使用Context
  const { buoys: contextBuoys, selectedBuoyId, setSelectedBuoyId, locationHistory, showHistoryTrack } = useBuoyContext();
  // 确定使用的实际值（优先使用props，其次使用context）
  const actualBuoys = useMemo(() => propBuoys || contextBuoys || [], [propBuoys, contextBuoys]);
  const actualOnBuoySelect = propOnBuoySelect || setSelectedBuoyId;

  const globeViewerRef = useRef<HTMLDivElement>(null);
  const viewerInstanceRef = useRef<Cesium.Viewer | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLightingEnabled, setIsLightingEnabled] = useState<boolean>(false);
  // 添加 isEntityLocked 状态用于跟踪当前实体是否已锁定
  const [isEntityLocked, setIsEntityLocked] = useState<boolean>(false);
  
  useEffect(() => {
    // 初始化Cesium
    initCesium();
    
    // 确保DOM元素存在
    if (!globeViewerRef.current) return;
    
    try {
      // 创建共享时钟模型
      const clockViewModel = new Cesium.ClockViewModel();

      // 配置3D地球视图选项
      const options = {
        fullscreenButton: true,
        sceneModePicker: true,
        baseLayerPicker: true,
        infoBox: false, // 禁用原生InfoBox
        geocoder: true,
        homeButton: true,
        navigationHelpButton: false,
        animation: false,
        timeline: false,
        clockViewModel: clockViewModel,
        sceneMode: Cesium.SceneMode.SCENE2D,
      };
      
      // 创建Cesium Viewer
      const globeViewer = new Cesium.Viewer(globeViewerRef.current, options);
      viewerInstanceRef.current = globeViewer;

      // 禁用双击跟踪实体的默认行为
      globeViewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      );

      // 设置2D视图
      globeViewer.scene.camera.setView({
        destination: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90),
      });

      // 移除默认版权信息
      if (globeViewer.cesiumWidget.creditContainer) {
        (globeViewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none';
      }

      // 启用地形深度测试，这会遮挡浮标的标志，因此关闭
      // globeViewer.scene.globe.depthTestAgainstTerrain = true;
      
      // 添加大气和光照效果
      globeViewer.scene.globe.enableLighting = true;
      globeViewer.scene.skyAtmosphere.show = true;

      // 添加错误事件监听器以防止渲染错误导致页面崩溃
      globeViewer.scene.renderError.addEventListener((scene, error) => {
        console.error('Cesium渲染错误:', error);
        // 尝试重置视图以恢复渲染
        try {
          scene.camera.setView({
            destination: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90),
          });
          // 在错误发生时解除实体跟踪
          globeViewer.trackedEntity = undefined;
          setIsEntityLocked(false); // 更新锁定状态
        } catch (e) {
          console.error('无法恢复视图:', e);
        }
      });

      // 监听 trackedEntityChanged 事件来同步锁定状态
      globeViewer.trackedEntityChanged.addEventListener((entity) => {
        setIsEntityLocked(!!entity); // 当有实体被跟踪时，更新锁定状态为true，否则为false
      });

      // 添加点击事件处理器
      const handler = new Cesium.ScreenSpaceEventHandler(globeViewer.canvas);
      handler.setInputAction(
        (movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
          handleEntityClick(movement);
        },
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      );

      // 清理函数
      return () => {
        if (globeViewer && !globeViewer.isDestroyed()) {
          handler.destroy();
          globeViewer.destroy();
          viewerInstanceRef.current = null;
        }
      };
    } catch (e) {
      setError(`初始化3D地球出错: ${e instanceof Error ? e.message : String(e)}`);
      console.error('3D地球初始化错误:', e);
    }
  }, []);
  
  // 处理实体点击事件
  const handleEntityClick = (movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    if (!viewerInstanceRef.current) return;
    
    const pickedObject = viewerInstanceRef.current.scene.pick(movement.position);
    
    if (
      Cesium.defined(pickedObject) && 
      pickedObject.id instanceof Cesium.Entity
    ) {
      // 获取实体ID
      const entityId = pickedObject.id.id as string;
      // 根据ID查找相应的浮标
      const buoy = actualBuoys.find((b: Buoy) => b.id === entityId);
      if (buoy) {
        // 调用回调函数，通知父组件或Context选择变更
        actualOnBuoySelect(buoy.id);
        
        // InfoBox 可见性由 CustomInfoBox 基于 selectedBuoyId 控制，不需要再单独设置
      }
    } else {
      // 点击空白处取消浮标选择
      if (viewerInstanceRef.current) {
        // 安全地解除跟踪，防止产生渲染错误
        try {
          viewerInstanceRef.current.trackedEntity = undefined;
          setIsEntityLocked(false); // 更新锁定状态
        } catch (e) {
          console.error('解除跟踪时出错:', e);
          // 尝试重置视图以修复问题
          viewerInstanceRef.current.scene.camera.setView({
            destination: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90),
          });
        }
      }
      // 调用回调函数，通知父组件取消选择
      actualOnBuoySelect(null);
    }
  };

  // 绘制历史轨迹的函数
  const loadBuoyHistoryTrack = useCallback((viewer: Cesium.Viewer, historyData: GeoPoint[]) => {
    if (!historyData || historyData.length < 1) return;

    // 移除之前的轨迹（如果存在）
    viewer.entities.removeById('historyTrack');

    // 获取当前选中的浮标
    const selectedBuoy = actualBuoys.find(b => b.id === selectedBuoyId);

    // 创建完整的轨迹点数组，包含历史数据和当前位置
    const allTrackPoints = [...historyData];

    // 如果有选中的浮标且有当前位置，将当前位置添加到轨迹末尾
    if (selectedBuoy && selectedBuoy.location) {
      const currentPosition: GeoPoint = {
        longitude: selectedBuoy.location.longitude,
        latitude: selectedBuoy.location.latitude,
        timestamp: new Date().toISOString()
      };

      // 检查当前位置是否已经在历史数据中（避免重复）
      const isCurrentPositionInHistory = historyData.some(point =>
        Math.abs(Number(point.longitude) - Number(currentPosition.longitude)) < 0.0001 &&
        Math.abs(Number(point.latitude) - Number(currentPosition.latitude)) < 0.0001
      );

      // 如果当前位置不在历史数据中，添加到轨迹末尾
      if (!isCurrentPositionInHistory) {
        allTrackPoints.push(currentPosition);
      }
    }

    // 如果轨迹点少于2个，不绘制轨迹线
    if (allTrackPoints.length < 2) return;

    // 创建轨迹线
    const positions = allTrackPoints.map(point =>
      Cesium.Cartesian3.fromDegrees(Number(point.longitude), Number(point.latitude))
    );

    viewer.entities.add({
      id: 'historyTrack',
      polyline: {
        positions: positions,
        width: 5,
        material: new Cesium.ColorMaterialProperty(
          Cesium.Color.RED.withAlpha(0.5)
        ),
        clampToGround: true
      }
    });
  }, [actualBuoys, selectedBuoyId]);

  useEffect(() => {
    if (viewerInstanceRef.current) {
      // 首先加载浮标数据到地图上
      if (Array.isArray(actualBuoys) && actualBuoys.length > 0) {
        loadBuoysToMap(viewerInstanceRef.current, actualBuoys, selectedBuoyId);
      }
      
      // 然后处理历史轨迹显示或移除
      if (selectedBuoyId && showHistoryTrack) {
        loadBuoyHistoryTrack(viewerInstanceRef.current, locationHistory);
      } else {
        viewerInstanceRef.current.entities.removeById('historyTrack');
      }
      
      // 如果处于锁定状态且有选中的浮标，更新相机位置以跟随浮标
      if (isEntityLocked && selectedBuoyId) {
        const selectedBuoy = actualBuoys.find(b => b.id === selectedBuoyId);
        if (selectedBuoy && selectedBuoy.location) {
          const entity = viewerInstanceRef.current.entities.getById(selectedBuoyId);
          if (entity) {
            viewerInstanceRef.current.trackedEntity = entity;
          }
        }
      }
    }
  }, [actualBuoys, selectedBuoyId, locationHistory, showHistoryTrack, isEntityLocked, loadBuoyHistoryTrack]); // 合并依赖项
  
  // 已将历史轨迹数据变化的逻辑合并到前面的 useEffect 中
  
  // 每当光照状态变化时更新现有的Viewer实例
  useEffect(() => {
    if (viewerInstanceRef.current) {
      viewerInstanceRef.current.scene.globe.enableLighting = isLightingEnabled;
    }
  }, [isLightingEnabled]);
  
  const toggleLighting = () => {
    setIsLightingEnabled(!isLightingEnabled);
  };

  return (
    <>
      {error ? (
        <div className="w-full h-full flex items-center justify-center bg-red-50 text-red-600 p-4">
          <div className="text-center">
            <p className="text-lg font-bold mb-2">3D地球加载错误</p>
            <p>{error}</p>
          </div>
        </div>
      ) : (
        <>
          <div ref={globeViewerRef} className="w-full h-full relative">
            <div className="absolute top-1 left-1 z-10">
              <button
                className="px-1 py-1 bg-gray-700/70 text-white rounded shadow transition-all duration-50 ease-in-out hover:bg-blue-500 hover:ring-2 hover:ring-blue-300 hover:ring-opacity-75"
                onClick={toggleLighting}
              >
                {isLightingEnabled ? (
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-5">
                    <path d="M12 2.25a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0V3a.75.75 0 0 1 .75-.75ZM7.5 12a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM18.894 6.166a.75.75 0 0 0-1.06-1.06l-1.591 1.59a.75.75 0 1 0 1.06 1.061l1.591-1.59ZM21.75 12a.75.75 0 0 1-.75.75h-2.25a.75.75 0 0 1 0-1.5H21a.75.75 0 0 1 .75.75ZM17.834 18.894a.75.75 0 0 0 1.06-1.06l-1.59-1.591a.75.75 0 1 0-1.061 1.06l1.59 1.591ZM12 18a.75.75 0 0 1 .75.75V21a.75.75 0 0 1-1.5 0v-2.25A.75.75 0 0 1 12 18ZM7.758 17.303a.75.75 0 0 0-1.061-1.06l-1.591 1.59a.75.75 0 0 0 1.06 1.061l1.591-1.59ZM6 12a.75.75 0 0 1-.75.75H3a.75.75 0 0 1 0-1.5h2.25A.75.75 0 0 1 6 12ZM6.697 7.757a.75.75 0 0 0 1.06-1.06l-1.59-1.591a.75.75 0 0 0-1.061 1.06l1.59 1.591Z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="size-5">
                    <path d="M12 .75a8.25 8.25 0 0 0-4.135 15.39c.686.398 1.115 1.008 1.134 1.623a.75.75 0 0 0 .577.706c.352.083.71.148 1.074.195.323.041.6-.218.6-.544v-4.661a6.714 6.714 0 0 1-.937-.171.75.75 0 1 1 .374-1.453 5.261 5.261 0 0 0 2.626 0 .75.75 0 1 1 .374 1.452 6.712 6.712 0 0 1-.937.172v4.66c0 .327.277.586.6.545.364-.047.722-.112 1.074-.195a.75.75 0 0 0 .577-.706c.02-.615.448-1.225 1.134-1.623A8.25 8.25 0 0 0 12 .75Z" />
                    <path fillRule="evenodd" d="M9.013 19.9a.75.75 0 0 1 .877-.597 11.319 11.319 0 0 0 4.22 0 .75.75 0 1 1 .28 1.473 12.819 12.819 0 0 1-4.78 0 .75.75 0 0 1-.597-.876ZM9.754 22.344a.75.75 0 0 1 .824-.668 13.682 13.682 0 0 0 2.844 0 .75.75 0 1 1 .156 1.492 15.156 15.156 0 0 1-3.156 0 .75.75 0 0 1-.668-.824Z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            </div>
            <CustomInfoBox
              viewer={viewerInstanceRef.current}
              displayMode={infoBoxMode}
              isLocked={isEntityLocked} // 传递锁定状态到CustomInfoBox
            />
          </div>
        </>
      )}
    </>
  );
};

export default GlobView; 