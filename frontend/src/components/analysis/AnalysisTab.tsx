import React, { useState } from 'react';
import { useBuoyContext } from '../../contexts/BuoyContext';
import { useTheme } from '../../contexts/ThemeContext';
import { AnalysisRequestItem } from '../../api/analysisService';
import { useAnalysisReport } from '../../hooks/useAnalysisReport';
import AnalysisReportPanel from './AnalysisReportPanel';
import { useToast } from '../ui/Toast';
import { formatUTCToLocal } from '../../utils/dateUtils';

interface AnalysisTabProps {
  sensorData: import('../../types/buoy.types').SensorData[];
}

/**
 * 智能分析标签页组件
 * 独立的智能分析功能，包含传感器选择和分析报告展示
 */
const AnalysisTab: React.FC<AnalysisTabProps> = ({ sensorData }) => {
  const { theme } = useTheme();
  const { loading, error, data, fetchReport } = useAnalysisReport();

  // 触发分析
  const handleAnalysis = (items: AnalysisRequestItem[]) => {
    fetchReport({ items });
  };

  return (
    <div>
      <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
        智能分析
      </h2>
      
      {sensorData.length === 0 ? (
        <div className={`text-center py-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
          暂无传感器数据可供分析
        </div>
      ) : (
        <>
          <SensorAnalysisSelector
            sensorData={sensorData}
            onAnalysis={handleAnalysis}
            analysisLoading={loading}
          />
          <AnalysisReportPanel
            report={data}
            loading={loading}
            error={error}
          />
        </>
      )}
    </div>
  );
};

/**
 * 智能分析多选与按钮子组件
 */
interface SensorAnalysisSelectorProps {
  sensorData: import('../../types/buoy.types').SensorData[];
  onAnalysis: (items: AnalysisRequestItem[]) => void;
  analysisLoading?: boolean;
}

const SensorAnalysisSelector: React.FC<SensorAnalysisSelectorProps> = ({ 
  sensorData, 
  onAnalysis, 
  analysisLoading 
}) => {
  // 改为按传感器类型选择
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const toast = useToast();
  const { selectedBuoyId } = useBuoyContext();

  // 获取所有传感器类型及其最新数据
  const sensorTypeMap = React.useMemo(() => {
    const map = new Map<string, { latestValue: number; unit: string; timestamp: string }>();
    sensorData.forEach(d => {
      if (typeof d.value === 'number') {
        const existing = map.get(d.data_type);
        if (!existing || new Date(d.timestamp) > new Date(existing.timestamp)) {
          map.set(d.data_type, {
            latestValue: d.value,
            unit: d.unit,
            timestamp: d.timestamp
          });
        }
      }
    });
    return map;
  }, [sensorData]);

  // 选择切换
  const handleSelect = (type: string) => {
    setSelectedTypes(prev =>
      prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]
    );
  };

  // 组装API请求体
  const buildRequestItems = (): AnalysisRequestItem[] => {
    if (!selectedBuoyId) {
      toast.showToast('请先选择一个浮标', 'warning');
      return [];
    }

    return sensorData
      .filter(d => selectedTypes.includes(d.data_type) && typeof d.value === 'number')
      .map(d => ({
        buoy_id: selectedBuoyId,
        sensor_type: d.data_type,
        data: {
          timestamp: d.timestamp,
          value: d.value as number,
          unit: d.unit,
        },
      }));
  };

  // 智能分析主流程
  const handleAnalysis = () => {
    if (!selectedBuoyId) {
      toast.showToast('请先选择一个浮标', 'warning');
      return;
    }
    if (selectedTypes.length === 0) {
      toast.showToast('请至少选择一个传感器类型', 'warning');
      return;
    }
    const items = buildRequestItems();
    if (items.length > 0) {
      onAnalysis(items);
      toast.showToast('正在分析，请稍候...', 'info');
    }
  };

  // 渲染多选列表
  return (
    <div className="mb-4">
      <div className="flex flex-col gap-2 mb-3">
        {Array.from(sensorTypeMap.entries()).map(([type, { latestValue, unit, timestamp }]) => (
          <label 
            key={type} 
            className="flex items-start space-x-2 text-xs sm:text-sm px-2 sm:px-3 py-2 rounded border cursor-pointer hover:bg-gray-50 transition-colors"
            style={{
              borderColor: selectedTypes.includes(type) ? '#3b82f6' : '#d1d5db',
              background: selectedTypes.includes(type) ? '#e0e7ff' : 'transparent'
            }}
          >
            <input
              type="checkbox"
              checked={selectedTypes.includes(type)}
              onChange={() => handleSelect(type)}
              disabled={analysisLoading}
              className="mt-0.5 flex-shrink-0"
            />
            <div className="flex flex-col min-w-0 flex-1">
              <span className="font-medium truncate">{type}</span>
              <span className="text-xs text-gray-500 truncate">
                最新值: {latestValue}{unit}
              </span>
              <span className="text-xs text-gray-400 truncate">
                {formatUTCToLocal(timestamp)}
              </span>
            </div>
          </label>
        ))}
      </div>
      <button
        className={`w-full px-3 py-2 rounded bg-blue-500 text-white text-xs sm:text-sm font-medium hover:bg-blue-600 transition disabled:opacity-50`}
        disabled={selectedTypes.length === 0 || analysisLoading}
        onClick={handleAnalysis}
      >
        {analysisLoading ? '分析中...' : '智能分析'}
      </button>
    </div>
  );
};

export default AnalysisTab;
