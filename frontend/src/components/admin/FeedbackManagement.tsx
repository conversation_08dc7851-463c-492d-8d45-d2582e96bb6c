import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useToast } from '../ui/Toast';
import { 
  feedbackService, 
  Feedback, 
  FeedbackStatus, 
  FeedbackType,
  FeedbackStats,
  getStatusText, 
  getTypeText,
  FEEDBACK_STATUS_OPTIONS,
  FEEDBACK_TYPE_OPTIONS 
} from '../../api/feedbackService';

const FeedbackManagement: React.FC = () => {
  const { theme } = useTheme();
  const { showToast } = useToast();
  
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '' as FeedbackStatus | '',
    type: '' as FeedbackType | '',
    search: ''
  });
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const [replyText, setReplyText] = useState('');
  const [updating, setUpdating] = useState(false);

  // 主题颜色
  const colors = {
    background: {
      main: theme === 'dark' ? '#1f2937' : '#ffffff',
      secondary: theme === 'dark' ? '#374151' : '#f9fafb',
    },
    text: {
      primary: theme === 'dark' ? '#f9fafb' : '#111827',
      secondary: theme === 'dark' ? '#d1d5db' : '#6b7280',
    },
    border: theme === 'dark' ? '#4b5563' : '#d1d5db',
  };

  // 状态颜色
  const getStatusColor = (status: FeedbackStatus) => {
    switch (status) {
      case FeedbackStatus.PENDING:
        return 'text-yellow-600 bg-yellow-100';
      case FeedbackStatus.PROCESSING:
        return 'text-blue-600 bg-blue-100';
      case FeedbackStatus.RESOLVED:
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const loadFeedbacks = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        size: 10,
        ...(filters.status && { status: filters.status }),
        ...(filters.type && { type: filters.type }),
        ...(filters.search && { search: filters.search })
      };
      
      const response = await feedbackService.getAllFeedback(params);
      setFeedbacks(response.items);
      setTotalPages(response.pages);
    } catch (error) {
      console.error('加载反馈列表失败:', error);
      showToast('加载反馈列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await feedbackService.getFeedbackStats();
      setStats(statsData);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  useEffect(() => {
    loadFeedbacks();
  }, [currentPage, filters]);

  useEffect(() => {
    loadStats();
  }, []);

  const handleFilterChange = (key: keyof typeof filters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const handleUpdateFeedback = async (id: number, status: FeedbackStatus, reply?: string) => {
    try {
      setUpdating(true);
      await feedbackService.updateFeedback(id, { status, reply });
      showToast('反馈更新成功', 'success');
      loadFeedbacks();
      loadStats();
      setSelectedFeedback(null);
      setReplyText('');
    } catch (error) {
      console.error('更新反馈失败:', error);
      showToast('更新反馈失败', 'error');
    } finally {
      setUpdating(false);
    }
  };

  const openReplyModal = (feedback: Feedback) => {
    setSelectedFeedback(feedback);
    setReplyText(feedback.reply || '');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading && !feedbacks.length) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="p-4 rounded-lg border" style={{ backgroundColor: colors.background.main, borderColor: colors.border }}>
            <div className="text-2xl font-bold" style={{ color: colors.text.primary }}>{stats.total}</div>
            <div className="text-sm" style={{ color: colors.text.secondary }}>总反馈数</div>
          </div>
          <div className="p-4 rounded-lg border" style={{ backgroundColor: colors.background.main, borderColor: colors.border }}>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <div className="text-sm" style={{ color: colors.text.secondary }}>待处理</div>
          </div>
          <div className="p-4 rounded-lg border" style={{ backgroundColor: colors.background.main, borderColor: colors.border }}>
            <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
            <div className="text-sm" style={{ color: colors.text.secondary }}>处理中</div>
          </div>
          <div className="p-4 rounded-lg border" style={{ backgroundColor: colors.background.main, borderColor: colors.border }}>
            <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
            <div className="text-sm" style={{ color: colors.text.secondary }}>已处理</div>
          </div>
        </div>
      )}

      {/* 筛选器 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1" style={{ color: colors.text.primary }}>
            状态筛选
          </label>
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="w-full px-3 py-2 rounded-md border"
            style={{
              backgroundColor: colors.background.main,
              borderColor: colors.border,
              color: colors.text.primary
            }}
          >
            <option value="">全部状态</option>
            {FEEDBACK_STATUS_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1" style={{ color: colors.text.primary }}>
            类型筛选
          </label>
          <select
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="w-full px-3 py-2 rounded-md border"
            style={{
              backgroundColor: colors.background.main,
              borderColor: colors.border,
              color: colors.text.primary
            }}
          >
            <option value="">全部类型</option>
            {FEEDBACK_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium mb-1" style={{ color: colors.text.primary }}>
            搜索
          </label>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="搜索反馈内容、联系方式或回复..."
            className="w-full px-3 py-2 rounded-md border"
            style={{
              backgroundColor: colors.background.main,
              borderColor: colors.border,
              color: colors.text.primary
            }}
          />
        </div>
      </div>

      {/* 反馈列表 */}
      {feedbacks.length === 0 ? (
        <div className="text-center py-8" style={{ color: colors.text.secondary }}>
          暂无反馈记录
        </div>
      ) : (
        <div className="space-y-4">
          {feedbacks.map((feedback) => (
            <div
              key={feedback.id}
              className="border rounded-lg p-4"
              style={{
                backgroundColor: colors.background.main,
                borderColor: colors.border
              }}
            >
              {/* 头部信息 */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-medium" style={{ color: colors.text.primary }}>
                    #{feedback.id}
                  </span>
                  <span
                    className="px-2 py-1 rounded-full text-xs font-medium"
                    style={{ color: colors.text.secondary }}
                  >
                    {getTypeText(feedback.type)}
                  </span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(feedback.status)}`}
                  >
                    {getStatusText(feedback.status)}
                  </span>
                  {feedback.contact && (
                    <span className="text-xs" style={{ color: colors.text.secondary }}>
                      联系方式: {feedback.contact}
                    </span>
                  )}
                </div>
                <span className="text-xs" style={{ color: colors.text.secondary }}>
                  {formatDate(feedback.created_at)}
                </span>
              </div>

              {/* 反馈内容 */}
              <div className="mb-3">
                <p style={{ color: colors.text.primary }}>
                  {feedback.content}
                </p>
              </div>

              {/* 管理员回复 */}
              {feedback.reply && (
                <div
                  className="mb-3 p-3 rounded-md"
                  style={{ backgroundColor: colors.background.secondary }}
                >
                  <div className="text-sm font-medium mb-1" style={{ color: colors.text.primary }}>
                    管理员回复：
                  </div>
                  <p className="text-sm" style={{ color: colors.text.secondary }}>
                    {feedback.reply}
                  </p>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => openReplyModal(feedback)}
                  className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                >
                  {feedback.reply ? '编辑回复' : '回复'}
                </button>
                
                {feedback.status !== FeedbackStatus.PROCESSING && (
                  <button
                    onClick={() => handleUpdateFeedback(feedback.id, FeedbackStatus.PROCESSING)}
                    className="px-3 py-1 bg-yellow-600 text-white rounded-md text-sm hover:bg-yellow-700"
                    disabled={updating}
                  >
                    标记为处理中
                  </button>
                )}
                
                {feedback.status !== FeedbackStatus.RESOLVED && (
                  <button
                    onClick={() => handleUpdateFeedback(feedback.id, FeedbackStatus.RESOLVED)}
                    className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                    disabled={updating}
                  >
                    标记为已处理
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 rounded-md border disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              backgroundColor: colors.background.main,
              borderColor: colors.border,
              color: colors.text.primary
            }}
          >
            上一页
          </button>
          
          <span className="text-sm" style={{ color: colors.text.secondary }}>
            第 {currentPage} 页，共 {totalPages} 页
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 rounded-md border disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              backgroundColor: colors.background.main,
              borderColor: colors.border,
              color: colors.text.primary
            }}
          >
            下一页
          </button>
        </div>
      )}

      {/* 回复模态框 */}
      {selectedFeedback && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div
            className="w-full max-w-md rounded-lg shadow-xl"
            style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}
          >
            <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: colors.border }}>
              <h3 className="text-lg font-medium" style={{ color: colors.text.primary }}>
                回复反馈 #{selectedFeedback.id}
              </h3>
              <button
                onClick={() => setSelectedFeedback(null)}
                className="text-gray-400 hover:text-gray-600"
                disabled={updating}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: colors.text.primary }}>
                  回复内容
                </label>
                <textarea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="请输入回复内容..."
                  rows={4}
                  className="w-full px-3 py-2 rounded-md border resize-none"
                  style={{
                    backgroundColor: colors.background.main,
                    borderColor: colors.border,
                    color: colors.text.primary
                  }}
                  disabled={updating}
                />
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setSelectedFeedback(null)}
                  className="flex-1 px-4 py-2 border rounded-md"
                  style={{
                    borderColor: colors.border,
                    color: colors.text.secondary
                  }}
                  disabled={updating}
                >
                  取消
                </button>
                <button
                  onClick={() => handleUpdateFeedback(selectedFeedback.id, FeedbackStatus.RESOLVED, replyText)}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  disabled={updating || !replyText.trim()}
                >
                  {updating ? '提交中...' : '提交回复'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FeedbackManagement;
