import React, { useState, useEffect } from 'react';
import { FiEdit, FiTrash2, FiPlus, FiSearch } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';
import { useToast } from '../ui/Toast';
import ConfirmDialog from './ConfirmDialog';
import UserForm from './UserForm';
import { getAllUsers, deleteUser } from '../../api/adminService';
import type { User } from '../../types';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showUserForm, setShowUserForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  
  const { showToast } = useToast();

  // 加载用户数据
  const loadUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getAllUsers();
      setUsers(response.data);
    } catch (err) {
      console.error('加载用户列表失败:', err);
      setError('加载用户列表失败，请稍后重试');
      showToast('加载用户列表失败，请稍后重试', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadUsers();
  }, []);

  // 处理搜索
  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 处理编辑用户
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setShowUserForm(true);
  };

  // 处理删除用户
  const handleDeleteClick = (user: User) => {
    setUserToDelete(user);
    setDeleteConfirmOpen(true);
  };

  // 确认删除用户
  const confirmDelete = async () => {
    if (!userToDelete) return;
    
    setDeleteLoading(true);
    try {
      await deleteUser(userToDelete.id);
      setUsers(users.filter(u => u.id !== userToDelete.id));
      showToast(`用户 ${userToDelete.username} 已成功删除`, 'success');
    } catch (err) {
      console.error('删除用户失败:', err);
      showToast('删除用户失败，请稍后重试', 'error');
    } finally {
      setDeleteLoading(false);
      setDeleteConfirmOpen(false);
      setUserToDelete(null);
    }
  };

  // 处理用户表单提交（创建/编辑）
  const handleUserFormSubmit = (updatedUser: User) => {
    if (editingUser) {
      // 更新现有用户
      setUsers(users.map(u => u.id === updatedUser.id ? updatedUser : u));
    } else {
      // 添加新用户
      setUsers([...users, updatedUser]);
    }
    setShowUserForm(false);
    setEditingUser(null);
  };

  const { colors } = useTheme();
  return (
    <div style={{ backgroundColor: colors.background.main }}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold" style={{ color: colors.text.primary }}>用户管理</h2>
        <button
          onClick={() => {
            setEditingUser(null);
            setShowUserForm(true);
          }}
          className="px-4 py-2 rounded-md flex items-center text-white"
          style={{ backgroundColor: colors.primary }}
        >
          <FiPlus className="mr-2" />
          添加用户
        </button>
      </div>

      {/* 搜索框 */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FiSearch style={{ color: colors.text.secondary }} />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border rounded-md leading-5 focus:outline-none sm:text-sm"
            placeholder="搜索用户名或邮箱..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ backgroundColor: colors.background.main, color: colors.text.primary, borderColor: colors.border, borderWidth: '1px' }}
          />
        </div>
      </div>

      {/* 用户列表 */}
      {loading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2" style={{ borderColor: colors.primary }}></div>
        </div>
      ) : error ? (
        <div className="text-center py-10" style={{ color: colors.buoy.error }}>{error}</div>
      ) : filteredUsers.length === 0 ? (
        <div className="text-center py-10" style={{ color: colors.text.secondary }}>
          {searchTerm ? '没有找到匹配的用户' : '暂无用户数据'}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full" style={{ borderColor: colors.border }}>
            <thead style={{ backgroundColor: colors.background.secondary }}>
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  用户名
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  邮箱
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  角色
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  状态
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider" style={{ color: colors.text.secondary }}>
                  操作
                </th>
              </tr>
            </thead>
            <tbody style={{ backgroundColor: colors.background.main, borderColor: colors.border }}>
              {filteredUsers.map((user) => (
                <tr key={user.id} style={{ borderColor: colors.border }}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium" style={{ color: colors.text.primary }}>{user.username}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm" style={{ color: colors.text.secondary }}>{user.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                      style={{
                        backgroundColor: user.role === 'admin' ? colors.primary + '33' : colors.secondary + '33',
                        color: user.role === 'admin' ? colors.primary : colors.secondary
                      }}>
                      {user.role === 'admin' ? '管理员' : '普通用户'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                      style={{
                        backgroundColor: colors.secondary + '33',
                        color: colors.secondary
                      }}>
                      活跃
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEditUser(user)}
                      className="mr-4"
                      style={{ color: colors.primary }}
                    >
                      <FiEdit className="inline" /> 编辑
                    </button>
                    <button
                      onClick={() => handleDeleteClick(user)}
                      className=""
                      disabled={user.role === 'admin'} // 禁止删除管理员
                      style={{ color: colors.buoy.error }}
                    >
                      <FiTrash2 className="inline" /> 删除
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* 用户表单（创建/编辑） */}
      {showUserForm && (
        <UserForm
          user={editingUser}
          onSubmit={handleUserFormSubmit}
          onCancel={() => {
            setShowUserForm(false);
            setEditingUser(null);
          }}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={deleteConfirmOpen}
        title="确认删除用户"
        message={`您确定要删除用户 "${userToDelete?.username}" 吗？此操作无法撤销。`}
        confirmText="删除"
        cancelText="取消"
        onConfirm={confirmDelete}
        onCancel={() => setDeleteConfirmOpen(false)}
        isLoading={deleteLoading}
      />
    </div>
  );
};

export default UserManagement;
