import React from 'react';
import { FiCpu, FiDatabase, FiUsers, FiActivity } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';
import StatsCard from './StatsCard';

const SystemMonitor: React.FC = () => {
  const { colors } = useTheme();
  return (
    <div style={{ backgroundColor: colors.background.main }}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4" style={{ color: colors.text.primary }}>系统监控</h2>
        <p style={{ color: colors.text.secondary }}>
          系统监控功能正在开发中，将提供系统运行状态、数据库使用情况和用户活跃度统计等信息。
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <StatsCard
          title="CPU 使用率"
          value="25%"
          icon={<FiCpu />}
          description="系统负载正常"
        />
        <StatsCard
          title="内存使用率"
          value="40%"
          icon={<FiActivity />}
          description="2GB / 5GB"
        />
        <StatsCard
          title="磁盘使用率"
          value="60%"
          icon={<FiDatabase />}
          description="30GB / 50GB"
        />
        <StatsCard
          title="活跃用户"
          value="12"
          icon={<FiUsers />}
          description="过去24小时"
          trend={{
            value: 20,
            isPositive: true,
            label: "较上周"
          }}
        />
        <StatsCard
          title="API请求量"
          value="1,254"
          icon={<FiActivity />}
          description="过去24小时"
          trend={{
            value: 5,
            isPositive: true,
            label: "较昨日"
          }}
        />
        <StatsCard
          title="平均响应时间"
          value="120ms"
          icon={<FiActivity />}
          description="过去24小时"
          trend={{
            value: 10,
            isPositive: false,
            label: "较昨日"
          }}
        />
      </div>

      <div className="overflow-hidden shadow rounded-lg mb-8" style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}>
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium mb-4" style={{ color: colors.text.primary }}>系统状态图表</h3>
          <div className="h-64 flex items-center justify-center rounded-md" style={{ backgroundColor: colors.background.secondary }}>
            <p style={{ color: colors.text.secondary }}>系统状态图表正在开发中...</p>
          </div>
        </div>
      </div>

      <div className="overflow-hidden shadow rounded-lg mb-8" style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}>
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium mb-4" style={{ color: colors.text.primary }}>用户活跃度统计</h3>
          <div className="h-64 flex items-center justify-center rounded-md" style={{ backgroundColor: colors.background.secondary }}>
            <p style={{ color: colors.text.secondary }}>用户活跃度统计图表正在开发中...</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemMonitor;
