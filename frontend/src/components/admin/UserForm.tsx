import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useToast } from '../ui/Toast';
import { createUser, updateUser } from '../../api/adminService';
import type { User } from '../../types';

interface UserFormProps {
  user: User | null; // 如果为null，则是创建新用户
  onSubmit: (user: User) => void;
  onCancel: () => void;
}

const UserForm: React.FC<UserFormProps> = ({ user, onSubmit, onCancel }) => {
  const [username, setUsername] = useState(user?.username || '');
  const [email, setEmail] = useState(user?.email || '');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [role, setRole] = useState(user?.role || 'user');
  const [bio, setBio] = useState(user?.bio || '');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const { showToast } = useToast();
  const isEditing = !!user;

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!username.trim()) {
      newErrors.username = '用户名不能为空';
    }
    
    if (!email.trim()) {
      newErrors.email = '邮箱不能为空';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = '邮箱格式不正确';
    }
    
    if (!isEditing) {
      if (!password) {
        newErrors.password = '密码不能为空';
      } else if (password.length < 6) {
        newErrors.password = '密码长度不能少于6个字符';
      }
      
      if (password !== confirmPassword) {
        newErrors.confirmPassword = '两次输入的密码不一致';
      }
    } else if (password && password !== confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      if (isEditing) {
        // 更新用户
        const userData = {
          username,
          email,
          ...(password ? { password } : {}),
          bio,
        };
        
        const response = await updateUser(user.id, userData);
        showToast('用户信息更新成功', 'success');
        onSubmit(response.data);
      } else {
        // 创建新用户
        const response = await createUser(username, email, password, role);
        showToast('用户创建成功', 'success');
        onSubmit(response.data);
      }
    } catch (err) {
      console.error('保存用户失败:', err);
      showToast('保存用户失败，请稍后重试', 'error');
    } finally {
      setLoading(false);
    }
  };

  const { colors } = useTheme();
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" style={{ backgroundColor: colors.background.main }}>
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* 背景遮罩 */}
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 opacity-75" style={{ backgroundColor: colors.background.secondary }}></div>
        </div>

        {/* 对话框居中技巧 */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        {/* 表单内容 */}
        <div
          className="inline-block align-bottom rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
          style={{ backgroundColor: colors.background.main, border: `1px solid ${colors.border}` }}
        >
          <form onSubmit={handleSubmit}>
            <div className="px-4 pt-5 pb-4 sm:p-6 sm:pb-4" style={{ backgroundColor: colors.background.main }}>
              <div className="mb-4">
                <h3 className="text-lg leading-6 font-medium" id="modal-headline" style={{ color: colors.text.primary }}>
                  {isEditing ? '编辑用户' : '创建新用户'}
                </h3>
              </div>
              
              <div className="mb-4">
                <label htmlFor="username" className="block text-sm font-medium" style={{ color: colors.text.primary }}>
                  用户名 <span style={{ color: colors.buoy.error }}>*</span>
                </label>
                <input
                  type="text"
                  id="username"
                  className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm`}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  style={{ borderColor: errors.username ? colors.buoy.error : colors.border, backgroundColor: colors.background.main, color: colors.text.primary }}
                />
                {errors.username && (
                  <p className="mt-1 text-sm" style={{ color: colors.buoy.error }}>{errors.username}</p>
                )}
              </div>
              
              <div className="mb-4">
                <label htmlFor="email" className="block text-sm font-medium" style={{ color: colors.text.primary }}>
                  邮箱 <span style={{ color: colors.buoy.error }}>*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm`}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  style={{ borderColor: errors.email ? colors.buoy.error : colors.border, backgroundColor: colors.background.main, color: colors.text.primary }}
                />
                {errors.email && (
                  <p className="mt-1 text-sm" style={{ color: colors.buoy.error }}>{errors.email}</p>
                )}
              </div>
              
              <div className="mb-4">
                <label htmlFor="password" className="block text-sm font-medium" style={{ color: colors.text.primary }}>
                  密码 {!isEditing && <span style={{ color: colors.buoy.error }}>*</span>}
                </label>
                <input
                  type="password"
                  id="password"
                  className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm`}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder={isEditing ? '留空表示不修改' : ''}
                  style={{ borderColor: errors.password ? colors.buoy.error : colors.border, backgroundColor: colors.background.main, color: colors.text.primary }}
                />
                {errors.password && (
                  <p className="mt-1 text-sm" style={{ color: colors.buoy.error }}>{errors.password}</p>
                )}
              </div>
              
              <div className="mb-4">
                <label htmlFor="confirmPassword" className="block text-sm font-medium" style={{ color: colors.text.primary }}>
                  确认密码 {!isEditing && <span style={{ color: colors.buoy.error }}>*</span>}
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  className={`mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm`}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder={isEditing ? '留空表示不修改' : ''}
                  style={{ borderColor: errors.confirmPassword ? colors.buoy.error : colors.border, backgroundColor: colors.background.main, color: colors.text.primary }}
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm" style={{ color: colors.buoy.error }}>{errors.confirmPassword}</p>
                )}
              </div>
              
              {!isEditing && (
                <div className="mb-4">
                  <label htmlFor="role" className="block text-sm font-medium" style={{ color: colors.text.primary }}>
                    角色
                  </label>
                  <select
                    id="role"
                    className="mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm"
                    value={role}
                    onChange={(e) => setRole(e.target.value as 'user' | 'admin')}
                    style={{ borderColor: colors.border, backgroundColor: colors.background.main, color: colors.text.primary }}
                  >
                    <option value="user">普通用户</option>
                    <option value="admin">管理员</option>
                  </select>
                </div>
              )}
              
              <div className="mb-4">
                <label htmlFor="bio" className="block text-sm font-medium" style={{ color: colors.text.primary }}>
                  个人简介
                </label>
                <textarea
                  id="bio"
                  rows={3}
                  className="mt-1 block w-full border rounded-md shadow-sm py-2 px-3 focus:outline-none sm:text-sm"
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  style={{ borderColor: colors.border, backgroundColor: colors.background.main, color: colors.text.primary }}
                ></textarea>
              </div>
            </div>
            
            <div className="px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse" style={{ backgroundColor: colors.background.secondary }}>
              <button
                type="submit"
                className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none sm:ml-3 sm:w-auto sm:text-sm ${
                  loading ? 'opacity-75 cursor-not-allowed' : ''
                }`}
                disabled={loading}
                style={{ backgroundColor: colors.primary }}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    保存中...
                  </>
                ) : (
                  '保存'
                )}
              </button>
              <button
                type="button"
                className="mt-3 w-full inline-flex justify-center rounded-md shadow-sm px-4 py-2 text-base font-medium focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                onClick={onCancel}
                disabled={loading}
                style={{ backgroundColor: colors.background.main, color: colors.text.primary, border: `1px solid ${colors.border}` }}
              >
                取消
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserForm;
