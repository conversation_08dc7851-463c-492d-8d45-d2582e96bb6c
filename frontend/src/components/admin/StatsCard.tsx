import React, { ReactNode } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
  className?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  description,
  trend,
  className = '',
}) => {
  const { colors } = useTheme();
  return (
    <div className={`overflow-hidden shadow rounded-lg ${className}`} style={{ backgroundColor: colors.background.secondary, border: `1px solid ${colors.border}` }}>
      <div className="p-5">
        <div className="flex items-center">
          {icon && (
            <div className="flex-shrink-0 mr-3">
              <div className="flex items-center justify-center h-10 w-10 rounded-md text-white" style={{ backgroundColor: colors.primary }}>
                {icon}
              </div>
            </div>
          )}
          <div>
            <div className="text-sm font-medium truncate" style={{ color: colors.text.secondary }}>
              {title}
            </div>
            <div className="mt-1 text-3xl font-semibold" style={{ color: colors.text.primary }}>
              {value}
            </div>
          </div>
        </div>
      </div>
      {(description || trend) && (
        <div className="px-5 py-3" style={{ backgroundColor: colors.background.secondary }}>
          <div className="text-sm">
            {description && (
              <span style={{ color: colors.text.secondary }}>{description}</span>
            )}
            {trend && (
              <span
                className="ml-2 font-medium"
                style={{ color: trend.isPositive ? colors.secondary : colors.buoy.error }}
              >
                {trend.isPositive ? '↑' : '↓'} {trend.value}% {trend.label}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default StatsCard;
