import React, { useEffect, useRef, use<PERSON><PERSON>back } from 'react';
import * as echarts from 'echarts/core'; // 按需引入核心模块
import { LineChart, ScatterChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent, // 引入 DataZoom
  GraphicComponent // 引入 Graphic 用于无数据提示
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { SensorData, GeoPoint } from '../../types';
import { useTheme } from '../../contexts/ThemeContext'; // 添加主题导入
import { formatUTCToLocal } from '../../utils/dateUtils'; // 导入时间格式化工具

// ECharts 按需引入注册
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent,
  GraphicComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON><PERSON>
]);

// --- 类型导入 (从 'echarts' 导入) ---
import { XAXisOption, YAXisOption, CallbackDataParams, SliderDataZoomOption } from 'echarts/types/dist/shared';


import type {
    EChartsOption,
    SeriesOption,
    LegendComponentOption, // 从 'echarts' 导入
    TooltipComponentOption, // 从 'echarts' 导入
    GridComponentOption, // 从 'echarts' 导入
    LineSeriesOption,   // 从 'echarts' 导入
    ScatterSeriesOption, // 从 'echarts' 导入
} from 'echarts';
// 注意: DefaultTooltipFormatterCallbackParams 可能不存在或不推荐直接使用，使用 CallbackDataParams[] 代替


// --- Interfaces ---

interface BuoyDataChartProps {
  sensorData?: SensorData[];
  locationHistory?: GeoPoint[];
  chartType: 'sensor' | 'location' | 'both';
  height?: string;
  width?: string;
  inCesiumInfoBox?: boolean;
  title?: string;
}

// --- Helper Functions ---

// Process Sensor Data (修改为按时间排序)
const processSensorData = (data?: SensorData[]): Record<string, { time: string[]; values: number[]; unit?: string }> | null => {
  if (!data || data.length === 0) return null;

  // 首先按传感器类型分组
  const groupedBySensor: Record<string, {
    rawData: Array<{ timestamp: string; value: number; unit?: string }>;
    unit?: string
  }> = {};

  // 收集每种传感器类型的原始数据
  data.forEach((item) => {
    if (typeof item.value === 'number') { // 仅处理数值类型的传感器数据
      if (!groupedBySensor[item.data_type]) {
        groupedBySensor[item.data_type] = { rawData: [], unit: item.unit };
      }
      groupedBySensor[item.data_type].rawData.push({
        timestamp: item.timestamp,
        value: item.value,
        unit: item.unit
      });
      if (!groupedBySensor[item.data_type].unit && item.unit) {
        groupedBySensor[item.data_type].unit = item.unit;
      }
    }
  });

  // 如果没有有效数据，返回null
  if (Object.keys(groupedBySensor).length === 0) return null;

  // 对每种传感器类型的数据按时间戳排序，并转换为最终格式
  const result: Record<string, { time: string[]; values: number[]; unit?: string }> = {};

  Object.entries(groupedBySensor).forEach(([sensorType, data]) => {
    // 按时间戳排序（从早到晚）
    data.rawData.sort((a, b) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeA - timeB; // 升序排序，确保从左到右时间递增
    });

    // 转换为最终格式
    result[sensorType] = {
      time: data.rawData.map(item => formatUTCToLocal(item.timestamp)),
      values: data.rawData.map(item => item.value),
      unit: data.unit
    };
  });

  return result;
};

// Process Location Data (修改为按时间排序)
const processLocationData = (data?: GeoPoint[]): { coordinates: [number, number][]; timestamps: string[] } | null => {
  if (!data || data.length === 0) return null;

  // 收集有效的位置数据
  const validLocations: Array<{
    coordinates: [number, number];
    timestamp: string;
    rawTimestamp: string;
  }> = [];

  data.forEach(loc => {
    if (typeof loc.longitude === 'number' && typeof loc.latitude === 'number') {
      validLocations.push({
        coordinates: [loc.longitude, loc.latitude],
        timestamp: loc.timestamp ? formatUTCToLocal(loc.timestamp) : '未知时间',
        rawTimestamp: loc.timestamp || ''
      });
    }
  });

  if (validLocations.length === 0) return null;

  // 按时间戳排序（从早到晚）
  validLocations.sort((a, b) => {
    // 处理"未知时间"的情况
    if (a.rawTimestamp === '' && b.rawTimestamp === '') return 0;
    if (a.rawTimestamp === '') return 1; // 未知时间放在后面
    if (b.rawTimestamp === '') return -1; // 未知时间放在后面

    const timeA = new Date(a.rawTimestamp).getTime();
    const timeB = new Date(b.rawTimestamp).getTime();
    return timeA - timeB; // 升序排序，确保从左到右时间递增
  });

  // 转换为最终格式
  const coordinates: [number, number][] = validLocations.map(loc => loc.coordinates);
  const timestamps: string[] = validLocations.map(loc => loc.timestamp);

  return { coordinates, timestamps };
};

// Get common Legend Style (使用导入的 LegendComponentOption)
const getLegendStyle = (isInfoBoxStyle: boolean): LegendComponentOption => {
    return {
        orient: 'horizontal',
        bottom: -5,
        textStyle: {
            fontSize: isInfoBoxStyle ? 12 : 12,
            color: isInfoBoxStyle ? '#ffffff' : '#333333',
        },
        itemGap: isInfoBoxStyle ? 15 : 10,
        type: 'scroll',
    };
};

// Get common Tooltip Style (使用导入的 TooltipComponentOption)
const getBaseTooltipStyle = (): TooltipComponentOption => ({
    trigger: 'axis', // Default trigger
    confine: true,
    backgroundColor: 'rgba(50,50,50,0.7)',
    borderColor: '#333',
    borderWidth: 0,
    textStyle: {
        fontSize: 12,
        color: '#fff'
    }
});

// --- Chart Option Creation Functions ---

const createSensorChartOption = (
    sensorData?: SensorData[],
    title?: string,
    isInfoBoxStyle: boolean = false,
    isDarkTheme: boolean = false // 添加主题参数
): EChartsOption => {
  const processedData = processSensorData(sensorData);
  const chartTitle = title;

  // 根据主题设置颜色
  const textColor = isDarkTheme ? '#fff' : '#333';
  const axisLineColor = isDarkTheme ? '#555' : '#ccc';
  const splitLineColor = isDarkTheme ? '#444' : '#eee';
  const noDataTextColor = isDarkTheme ? '#aaa' : '#999';

  if (!processedData) {
    return {
      title: { text: chartTitle, left: 'center', textStyle: { color: isInfoBoxStyle ? '#fff' : textColor } },
      graphic: { type: 'text', left: 'center', top: 'middle', style: { text: 'No sensor data available', fontSize: 14, fill: noDataTextColor } }
    };
  }

  const series: LineSeriesOption[] = []; // 使用导入的 LineSeriesOption
  const legendData: string[] = [];
  let xAxisData: string[] = [];
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];

  Object.entries(processedData).forEach(([sensorType, data], index) => {
    if (index === 0) {
      xAxisData = data.time;
    }
    legendData.push(sensorType);
    series.push({
      name: sensorType, type: 'line', data: data.values, smooth: true,
      symbol: 'circle', symbolSize: 4, sampling: 'lttb',
      itemStyle: { color: colors[index % colors.length] },
      areaStyle: { opacity: 0.1 }
    });
  });

  return {
    title: { text: chartTitle, left: 'center', textStyle: { color: isInfoBoxStyle ? '#fff' : textColor } },
    tooltip: {
        ...getBaseTooltipStyle(),
        trigger: 'axis',
        // 使用 CallbackDataParams[] 作为 axis trigger 的参数类型
        formatter: ((params) => {
            // params 类型现在是 CallbackDataParams[] | CallbackDataParams，需要检查
            if (!params || (Array.isArray(params) && params.length === 0)) return '';

            const paramsArray = Array.isArray(params) ? params : [params]; // 统一处理为数组
            if(paramsArray.length === 0 || !paramsArray[0].name) return ''; // 添加检查

            let result = `${paramsArray[0].name}<br/>`; // X 轴标签 (时间)
            paramsArray.forEach((param) => { // param 类型是 CallbackDataParams
              const sensorType = param.seriesName ?? '';
              const value = param.value;
              // 确保 param.value 是数字或可以转换的类型
              const displayValue = typeof value === 'number' ? value.toFixed(2) : value; // 保留两位小数或原样显示
              const unit = processedData[sensorType]?.unit || ''; // 从 processedData 获取单位
              const marker = param.marker || ''; // 获取标记，可能为空
              result += `${marker} ${sensorType}: ${displayValue} ${unit}<br/>`;
            });
            return result;
        })
    },
    legend: {
      ...getLegendStyle(isInfoBoxStyle),
      data: legendData,
      textStyle: {
        fontSize: isInfoBoxStyle ? 12 : 12,
        color: isInfoBoxStyle ? '#ffffff' : textColor
      }
    },
    grid: { left: '3%', right: '4%', bottom: '60px', top: '50px', containLabel: true },
    toolbox: {
      feature: { saveAsImage: { title: 'Save Image', backgroundColor: '#fff' } },
      iconStyle: { borderColor: isInfoBoxStyle ? '#ccc' : (isDarkTheme ? '#ccc' : '#666') },
      right: 15
    },
    xAxis: {
      type: 'category', boundaryGap: false, data: xAxisData,
      axisLabel: { rotate: 0, fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor },
      axisLine: { lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
    },
    yAxis: {
      type: 'value',
      splitLine: { lineStyle: { type: 'dashed', color: isInfoBoxStyle ? '#444' : splitLineColor } },
      axisLabel: { fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor },
      axisLine: { show: true, lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
    },
    series: series,
    dataZoom: [
        { type: 'inside', start: 0, end: 100 },
        {
          type: 'slider',
          height: 20,
          bottom: 25,
          start: 0,
          end: 100,
          textStyle: { color: isInfoBoxStyle ? '#ccc' : textColor },
          borderColor: isInfoBoxStyle ? '#555' : axisLineColor,
          fillerColor: isDarkTheme ? 'rgba(120,120,120,0.2)' : 'rgba(200,200,200,0.2)'
        } as SliderDataZoomOption // 类型断言
    ],
  };
};

const createLocationChartOption = (
    locationHistory?: GeoPoint[],
    title?: string,
    isInfoBoxStyle: boolean = false,
    isDarkTheme: boolean = false // 添加主题参数
): EChartsOption => {
  const locationData = processLocationData(locationHistory);
  const chartTitle = title || 'Location History';

  // 根据主题设置颜色
  const textColor = isDarkTheme ? '#fff' : '#333';
  const axisLineColor = isDarkTheme ? '#555' : '#ccc';
  const splitLineColor = isDarkTheme ? '#444' : '#eee';
  const noDataTextColor = isDarkTheme ? '#aaa' : '#999';

  if (!locationData) {
    return {
      title: { text: chartTitle, left: 'center', textStyle: { color: isInfoBoxStyle ? '#fff' : textColor } },
      graphic: { type: 'text', left: 'center', top: 'middle', style: { text: 'No location data available', fontSize: 14, fill: noDataTextColor } }
    };
  }

  const { coordinates, timestamps } = locationData;

  return {
    title: { text: chartTitle, left: 'center', textStyle: { color: isInfoBoxStyle ? '#fff' : textColor } },
    tooltip: {
        ...getBaseTooltipStyle(),
        trigger: 'item', // Trigger on points
        // 使用 CallbackDataParams 作为 item trigger 的参数类型
        formatter: ((params) => {
            // params 类型现在是 CallbackDataParams[] | CallbackDataParams，需要检查
             if (Array.isArray(params)) return ''; // Item trigger 应该只有一个参数
             const param = params as CallbackDataParams; // 断言为单个参数

             const dataIndex = param.dataIndex ?? -1;
             // value 是坐标数组 [lon, lat]
             const value = Array.isArray(param.value) ? param.value as [number, number] : [NaN, NaN];
             const timestamp = dataIndex >= 0 && dataIndex < timestamps.length ? timestamps[dataIndex] : 'Unknown Time';
             const marker = param.marker || '';
             // 使用 toFixed(4) 显示经纬度
             return `Time: ${timestamp}<br/>${marker}Lon: ${value[0]?.toFixed(4)}<br/>${marker}Lat: ${value[1]?.toFixed(4)}`;
        })
    },
    legend: {
      ...getLegendStyle(isInfoBoxStyle),
      data: ['History', 'Trajectory'],
      bottom: 5,
      textStyle: {
        fontSize: isInfoBoxStyle ? 12 : 12,
        color: isInfoBoxStyle ? '#ffffff' : textColor
      }
    },
    grid: { left: '3%', right: '4%', bottom: '30px', top: '60px', containLabel: true },
    toolbox: {
      feature: { saveAsImage: { title: 'Save Image', backgroundColor: '#fff' } },
      iconStyle: { borderColor: isInfoBoxStyle ? '#ccc' : (isDarkTheme ? '#ccc' : '#666') },
      right: 15
    },
    xAxis: { // 类型是 XAXisOption (从 'echarts' 导入)
      type: 'value',
      name: 'Longitude',
      nameTextStyle: { fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor },
      scale: true,
      splitLine: { lineStyle: { type: 'dashed', color: isInfoBoxStyle ? '#444' : splitLineColor } },
      axisLabel: { fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor, formatter: '{value}°' }, // 添加单位
      axisLine: { show: true, lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
    },
    yAxis: { // 类型是 YAXisOption (从 'echarts' 导入)
      type: 'value',
      name: 'Latitude',
      nameTextStyle: { fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor },
      scale: true,
      splitLine: { lineStyle: { type: 'dashed', color: isInfoBoxStyle ? '#444' : splitLineColor } },
      axisLabel: { fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor, formatter: '{value}°' }, // 添加单位
      axisLine: { show: true, lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
    },
    series: [
      // 使用导入的 ScatterSeriesOption 和 LineSeriesOption
      { name: 'History', type: 'scatter', symbolSize: 5, data: coordinates, itemStyle: { color: '#5470c6' } } as ScatterSeriesOption,
      { name: 'Trajectory', type: 'line', smooth: true, symbol: 'none', data: coordinates, lineStyle: { width: 2, color: '#91cc75' } } as LineSeriesOption
    ]
  };
};

const createBothChartOption = (
    sensorData?: SensorData[],
    locationHistory?: GeoPoint[],
    title?: string,
    isInfoBoxStyle: boolean = false,
    isDarkTheme: boolean = false // 添加主题参数
): EChartsOption => {
  const processedSensor = processSensorData(sensorData);
  const processedLocation = processLocationData(locationHistory);
  const chartTitle = title || 'Buoy Data Overview';

  // 根据主题设置颜色
  const textColor = isDarkTheme ? '#fff' : '#333';
  const axisLineColor = isDarkTheme ? '#555' : '#ccc';
  const splitLineColor = isDarkTheme ? '#444' : '#eee';
  const noDataTextColor = isDarkTheme ? '#aaa' : '#999';

  // 获取基础配置时传入空标题和主题状态，避免重复
  const sensorOption = createSensorChartOption(sensorData, '', isInfoBoxStyle, isDarkTheme);
  const locationOption = createLocationChartOption(locationHistory, '', isInfoBoxStyle, isDarkTheme);

  // 从基础配置中提取 Legend 数据，处理可能为 undefined 的情况
  const sensorLegendData = (sensorOption.legend as LegendComponentOption)?.data || [];
  const locationLegendData = (locationOption.legend as LegendComponentOption)?.data || [];

  // 从基础配置中提取 Series 数据，处理可能为 undefined 的情况
  const sensorSeries = (sensorOption.series as LineSeriesOption[])?.map(s => ({ ...s, xAxisIndex: 0, yAxisIndex: 0 })) || [];
  const locationSeries = (locationOption.series as (ScatterSeriesOption | LineSeriesOption)[])?.map(s => ({ ...s, xAxisIndex: 1, yAxisIndex: 1 })) || [];


  const combinedOption: EChartsOption = {
    title: { text: chartTitle, left: 'center', textStyle: { color: isInfoBoxStyle ? '#fff' : textColor } },
    legend: {
        ...getLegendStyle(isInfoBoxStyle),
        data: [...sensorLegendData, ...locationLegendData],
        top: 30, bottom: 'auto',
        textStyle: {
          fontSize: isInfoBoxStyle ? 12 : 12,
          color: isInfoBoxStyle ? '#ffffff' : textColor
        }
    },
    tooltip: { // Combined tooltip needs careful handling, maybe stick to axis trigger?
        ...getBaseTooltipStyle(),
        trigger: 'axis', // Set to axis for consistency, might not show location points individually well
        // TODO: Consider a custom formatter if detailed point info is needed for both charts simultaneously
    },
    grid: [
      // 使用 GridComponentOption 类型断言
      { left: '7%', right: '5%', top: '15%', height: '35%', containLabel: true } as GridComponentOption, // Sensor grid (index 0)
      { left: '7%', right: '5%', top: '55%', height: '35%', containLabel: true } as GridComponentOption  // Location grid (index 1)
    ],
    toolbox: {
      feature: { saveAsImage: { title: 'Save Image', backgroundColor: '#fff' } },
      iconStyle: { borderColor: isInfoBoxStyle ? '#ccc' : (isDarkTheme ? '#ccc' : '#666') },
      right: 15
    },
    xAxis: [
      // Sensor X Axis (index 0) - 使用 XAXisOption 类型断言
      {
        ...(sensorOption.xAxis as XAXisOption),
        gridIndex: 0,
        axisLabel: {
          ...(sensorOption.xAxis as XAXisOption)?.axisLabel,
          rotate: 0,
          color: isInfoBoxStyle ? '#ccc' : textColor
        },
        axisLine: { lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
      },
      // Location X Axis (Longitude) (index 1) - 使用 XAXisOption 类型断言
      {
        ...(locationOption.xAxis as XAXisOption),
        gridIndex: 1,
        nameTextStyle: { fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor },
        axisLabel: {
          ...(locationOption.xAxis as XAXisOption)?.axisLabel,
          color: isInfoBoxStyle ? '#ccc' : textColor
        },
        axisLine: { lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
      }
    ],
    yAxis: [
      // Sensor Y Axis (index 0) - 使用 YAXisOption 类型断言
      {
        ...(sensorOption.yAxis as YAXisOption),
        gridIndex: 0,
        name: '',
        axisLabel: {
          ...(sensorOption.yAxis as YAXisOption)?.axisLabel,
          color: isInfoBoxStyle ? '#ccc' : textColor
        },
        splitLine: { lineStyle: { type: 'dashed', color: isInfoBoxStyle ? '#444' : splitLineColor } },
        axisLine: { lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
      }, // Hide name if needed
      // Location Y Axis (Latitude) (index 1) - 使用 YAXisOption 类型断言
      {
        ...(locationOption.yAxis as YAXisOption),
        gridIndex: 1,
        nameTextStyle: { fontSize: 10, color: isInfoBoxStyle ? '#ccc' : textColor },
        axisLabel: {
          ...(locationOption.yAxis as YAXisOption)?.axisLabel,
          color: isInfoBoxStyle ? '#ccc' : textColor
        },
        splitLine: { lineStyle: { type: 'dashed', color: isInfoBoxStyle ? '#444' : splitLineColor } },
        axisLine: { lineStyle: { color: isInfoBoxStyle ? '#555' : axisLineColor} }
      }
    ],
    series: [
        ...sensorSeries,
        ...locationSeries
    ],
    dataZoom: [
        // 使用 SliderDataZoomOption 类型断言
        { type: 'inside', xAxisIndex: [0], start: 0, end: 100 } as SliderDataZoomOption,
        {
          type: 'slider',
          xAxisIndex: [0],
          height: 15,
          bottom: '46%',
          start: 0,
          end: 100,
          textStyle: { color: isInfoBoxStyle ? '#ccc' : textColor },
          borderColor: isInfoBoxStyle ? '#555' : axisLineColor,
          fillerColor: isDarkTheme ? 'rgba(120,120,120,0.2)' : 'rgba(200,200,200,0.2)'
        } as SliderDataZoomOption
    ]
  };

  // --- 处理无数据的情况 ---
  const grids = combinedOption.grid as GridComponentOption[]; // 断言为数组
  const xAxes = combinedOption.xAxis as XAXisOption[]; // 断言为数组
  const yAxes = combinedOption.yAxis as YAXisOption[]; // 断言为数组
  const dataZooms = combinedOption.dataZoom as SliderDataZoomOption[]; // 断言为数组
  let currentSeries = combinedOption.series as SeriesOption[]; // 断言为数组
  let currentLegendData = (combinedOption.legend as LegendComponentOption).data; // 不进行具体类型断言

  if (!processedSensor) {
      if (grids[0]) grids[0].show = false;
      if (xAxes[0]) xAxes[0].show = false;
      if (yAxes[0]) yAxes[0].show = false;
      if (grids[1]) { grids[1].top = '15%'; grids[1].height = '75%'; } // Adjust location grid
      // 修复: 使用更具体的类型断言过滤 series (修复 no-explicit-any)
      currentSeries = currentSeries.filter(s => (s as ScatterSeriesOption | LineSeriesOption).xAxisIndex === 1);
      currentLegendData = locationLegendData;
      // Hide sensor dataZoom
      if (dataZooms[0]) dataZooms[0].show = false; // Inside zoom
      if (dataZooms[1]) dataZooms[1].show = false; // Slider zoom

  }
  if (!processedLocation) {
      if (grids[1]) grids[1].show = false;
      if (xAxes[1]) xAxes[1].show = false;
      if (yAxes[1]) yAxes[1].show = false;
      if (grids[0]) { grids[0].top = '15%'; grids[0].height = '75%'; } // Adjust sensor grid
      // 修复: 使用更具体的类型断言过滤 series (修复 no-explicit-any)
      currentSeries = currentSeries.filter(s => (s as LineSeriesOption).xAxisIndex === 0);
      currentLegendData = sensorLegendData;
      // Adjust sensor dataZoom slider position if location is hidden
      if (dataZooms[1]) {
          dataZooms[1].bottom = '10px'; // 移动到底部
      }
      // No need to hide dataZooms[0] (inside)
  }

  // 更新 option 中的 series 和 legend data
  combinedOption.series = currentSeries;
  if (combinedOption.legend) {
      (combinedOption.legend as LegendComponentOption).data = currentLegendData;
  }


   if (!processedSensor && !processedLocation) {
      return { // Return a simple "no data" message
         title: { text: chartTitle, left: 'center', textStyle: { color: isInfoBoxStyle ? '#fff' : textColor } },
         graphic: { type: 'text', left: 'center', top: 'middle', style: { text: 'No data available', fontSize: 14, fill: noDataTextColor } }
      };
   }

  return combinedOption;
};


// --- React Component ---

const BuoyDataChart: React.FC<BuoyDataChartProps> = ({
  sensorData,
  locationHistory,
  chartType,
  height = '300px',
  width = '100%',
  inCesiumInfoBox = false,
  title,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const disposeChartOnUnmount = useRef(true);
  const { theme } = useTheme(); // 获取当前主题

  // --- Chart Initialization and Resizing ---
  useEffect(() => {
    let instance: echarts.ECharts | null = null;
    let resizeObserver: ResizeObserver | null = null;
    const currentChartRef = chartRef.current; // 修复 react-hooks/exhaustive-deps

    if (currentChartRef) {
        // 初始化图表实例
        instance = echarts.init(currentChartRef);
        disposeChartOnUnmount.current = true;
        chartInstance.current = instance;

        // Use ResizeObserver for better container resize detection
        resizeObserver = new ResizeObserver(() => {
            instance?.resize();
        });
        resizeObserver.observe(currentChartRef); // 使用局部变量

        // Cleanup
        return () => {
            if (resizeObserver && currentChartRef) { // 使用局部变量
                 resizeObserver.unobserve(currentChartRef);
            }
            resizeObserver = null; // Release observer

            // 组件卸载时清理图表实例
            if (instance) {
                instance.dispose();
                chartInstance.current = null;
            }
        };
    }
    return () => {}; // Empty cleanup if instance wasn't created/found
  }, []); // Keep dependency array empty for init/dispose logic


  // --- Update Chart Options When Props Change ---
  const getChartOption = useCallback((): EChartsOption => {
    const applyInfoBoxStyle = inCesiumInfoBox;
    const isDarkTheme = theme === 'dark'; // 判断是否为暗色主题

    switch (chartType) {
      case 'sensor':
        return createSensorChartOption(sensorData, title, applyInfoBoxStyle, isDarkTheme);
      case 'location':
        return createLocationChartOption(locationHistory, title, applyInfoBoxStyle, isDarkTheme);
      case 'both':
        return createBothChartOption(sensorData, locationHistory, title, applyInfoBoxStyle, isDarkTheme);
      default: {
        console.warn("BuoyDataChart: Invalid chartType prop:", chartType);
        const textColor = isDarkTheme ? '#fff' : '#333';
        return {
          title: { text: title || 'Chart', left: 'center', textStyle: { color: textColor } },
          graphic: { type: 'text', left: 'center', top: 'middle', style: { text: 'Invalid chart type', fontSize: 14, fill: '#f00' } }
        };
      }
    }
  }, [sensorData, locationHistory, chartType, title, inCesiumInfoBox, theme]); // 添加theme到依赖

  useEffect(() => {
    if (chartInstance.current) {
      const option = getChartOption();
      // Use notMerge = true to clear previous options before applying new ones
      // This is important when switching chart types or when data structure changes significantly
      chartInstance.current.setOption(option, true);
    }
  }, [getChartOption]); // Re-run when option generation logic changes

  // 监听窗口大小变化
  useEffect(() => {
    const handleWindowResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleWindowResize);

    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, []);

  return (
    <div
      ref={chartRef}
      className="buoy-data-chart"
      style={{ width, height, overflow: 'hidden' }} // overflow: hidden prevents dataZoom slider from showing outside
    />
  );
};

export default BuoyDataChart;