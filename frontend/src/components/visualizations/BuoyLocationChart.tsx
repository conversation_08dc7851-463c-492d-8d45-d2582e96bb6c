import { useEffect, useRef, useState, useLayoutEffect } from 'react';
import * as echarts from 'echarts';
import { GeoPoint } from '../../types';
import { useTheme } from '../../contexts/ThemeContext';
import { formatUTCToLocal } from '../../utils/dateUtils';

// 组件接口定义
interface BuoyLocationChartProps {
  // 历史位置数据数组
  locationHistory: GeoPoint[];
  // 图表宽度
  width?: string;
  // 是否在Cesium InfoBox中使用
  inCesiumInfoBox?: boolean;
  // 图表标题
  title?: string;
  // 默认轨迹线模式：'gradient'(渐变色) 或 'solid'(单色)
  defaultTrackMode?: 'gradient' | 'solid';
}

// 自定义参数类型定义
interface TooltipDataParams {
  data: {
    time?: string;
    value?: number[];
  };
  value: number[];
  seriesName?: string;  // 添加系列名称属性
}

/**
 * 浮标位置历史轨迹图表组件
 * 横坐标为经度，纵坐标为纬度
 * 轨迹点使用颜色深浅表示时间早晚
 */
const BuoyLocationChart: React.FC<BuoyLocationChartProps> = ({
  locationHistory = [],
  inCesiumInfoBox = false,
  title = '浮标位置历史',
  defaultTrackMode = 'gradient',
}) => {
  // 图表容器引用
  const chartRef = useRef<HTMLDivElement>(null);
  // 保存echarts实例
  const chartInstance = useRef<echarts.ECharts | null>(null);
  // 轨迹线显示模式状态
  const [trackMode, setTrackMode] = useState<'gradient' | 'solid'>(defaultTrackMode);
  // 容器的实际尺寸
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  // 获取当前主题
  const { theme } = useTheme();

  // 处理位置数据
  const processLocationData = () => {
    if (locationHistory.length === 0) return null;

    // 按时间排序位置数据
    const sortedData = [...locationHistory].sort((a, b) =>
      new Date(a.timestamp || '').getTime() - new Date(b.timestamp || '').getTime()
    );

    // 提取经纬度数据用于绘制轨迹
    const coordinates = sortedData.map((loc) => [loc.longitude, loc.latitude]);

    // 提取时间戳用于颜色映射
    const timestamps = sortedData.map((loc) =>
      loc.timestamp ? new Date(loc.timestamp).getTime() : 0
    );

    // 计算时间戳的最小值和最大值，用于颜色映射
    const minTime = Math.min(...timestamps);
    const maxTime = Math.max(...timestamps);

    // 归一化时间戳到 0-1 范围，用于颜色映射
    const normalizedTimes = timestamps.map(time =>
      maxTime > minTime ? (time - minTime) / (maxTime - minTime) : 0
    );

    // 格式化后的时间字符串，用于tooltip显示
    const formattedTimes = sortedData.map(loc =>
      loc.timestamp ? formatUTCToLocal(loc.timestamp) : '未知时间'
    );

    return {
      coordinates,
      timestamps,
      normalizedTimes,
      formattedTimes,
      minTime,
      maxTime
    };
  };

  // 创建图表选项
  const createChartOption = () => {
    const locationData = processLocationData();
    // 根据主题设置颜色
    const isDarkTheme = theme === 'dark';
    const textColor = isDarkTheme ? '#fff' : '#333';
    const axisLineColor = isDarkTheme ? '#555' : '#ccc';
    const splitLineColor = isDarkTheme ? '#444' : '#eee';
    const noDataTextColor = isDarkTheme ? '#aaa' : '#999';

    if (!locationData) {
      return {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 16,
            color: inCesiumInfoBox ? '#fff' : textColor
          }
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无位置数据',
            fontSize: 14,
            fill: noDataTextColor
          }
        }
      };
    }

    const { coordinates, normalizedTimes, formattedTimes } = locationData;

    // 创建带时间信息的轨迹点数据
    const scatterData = coordinates.map((coord, index) => ({
      value: [...coord, normalizedTimes[index]],
      time: formattedTimes[index]
    }));

    // 创建轨迹线段数据，每段线都有不同的颜色
    const lines = [];
    for (let i = 0; i < coordinates.length - 1; i++) {
      // 线段开始和结束点的时间值
      const startTime = normalizedTimes[i];
      const endTime = normalizedTimes[i + 1];

      // 计算线段的平均时间值用于颜色渐变
      const avgTime = (startTime + endTime) / 2;

      // 创建线段数据
      lines.push({
        coords: [coordinates[i], coordinates[i + 1]],
        lineStyle: {
          width: 2,
          opacity: 0.6 + (avgTime * 0.4), // 根据时间调整透明度，越新的线段越不透明
          color: `rgba(24, 144, 255, ${0.3 + avgTime * 0.7})` // 根据时间调整颜色饱和度
        }
      });
    }

    // 创建轨迹线系列
    const trackSeries = [];

    // 根据当前模式选择轨迹线显示方式
    if (trackMode === 'gradient') {
      // 渐变色模式 - 使用多条颜色不同的线段
      trackSeries.push({
        type: 'lines',
        name: '轨迹线(渐变)',
        coordinateSystem: 'cartesian2d',
        zlevel: 1,
        effect: {
          show: false
        },
        data: lines,
        emphasis: {
          lineStyle: {
            width: 3
          }
        },
        tooltip: {
          show: false  // 轨迹线不显示tooltip
        }
      });
    } else {
      // 单色模式 - 使用单条连续的线
      trackSeries.push({
        type: 'line',
        name: '轨迹线(单色)',
        data: coordinates,
        showSymbol: false,
        smooth: true,
        zlevel: 1,
        lineStyle: {
          width: 2,
          opacity: 0.8,
          color: '#1890ff'
        },
        emphasis: {
          lineStyle: {
            width: 3,
            opacity: 1
          }
        },
        tooltip: {
          show: false  // 轨迹线不显示tooltip
        }
      });
    }

    // 添加轨迹点系列
    trackSeries.push({
      type: 'scatter',
      name: '轨迹点',
      data: scatterData,
      symbolSize: 8,
      zlevel: 2,
      emphasis: {
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        scale: true
      },
      tooltip: {
        show: true  // 轨迹点显示tooltip
      }
    });

    // 计算经纬度范围，为了保持一致的刻度
    let minLon = Infinity, maxLon = -Infinity, minLat = Infinity, maxLat = -Infinity;

    coordinates.forEach(coord => {
      minLon = Math.min(minLon, coord[0]);
      maxLon = Math.max(maxLon, coord[0]);
      minLat = Math.min(minLat, coord[1]);
      maxLat = Math.max(maxLat, coord[1]);
    });

    // 计算经纬度范围，并取较大值确保两个轴的比例一致
    const lonRange = maxLon - minLon;
    const latRange = maxLat - minLat;
    const maxRange = Math.max(lonRange, latRange);

    // 扩展范围确保等比例
    const lonPadding = (maxRange - lonRange) / 2;
    const latPadding = (maxRange - latRange) / 2;

    // 调整最终的显示范围，有一定的外边距
    const padding = maxRange * 0.05;
    const adjustedMinLon = minLon - lonPadding - padding;
    const adjustedMaxLon = maxLon + lonPadding + padding;
    const adjustedMinLat = minLat - latPadding - padding;
    const adjustedMaxLat = maxLat + latPadding + padding;

    return {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          color: inCesiumInfoBox ? '#fff' : textColor
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: TooltipDataParams) => {
          // 只处理轨迹点的tooltip
          if (params.seriesName !== '轨迹点') {
            return '';
          }

          const time = params.data?.time || '未知时间';

          // 检查params.value是否存在且为数组
          if (!params.value || !Array.isArray(params.value) || params.value.length < 2) {
            return `时间: ${time}`;
          }

          // 确保值存在后再调用toFixed方法
          const longitude = typeof params.value[0] === 'number' ? params.value[0].toFixed(4) : '未知';
          const latitude = typeof params.value[1] === 'number' ? params.value[1].toFixed(4) : '未知';

          return `时间: ${time}<br/>` +
                 `经度: ${longitude}°E<br/>` +
                 `纬度: ${latitude}°N`;
        }
      },
      grid: {
        left: '60px',
        right: '6%',
        bottom: '60px',
        top: '50px',
        containLabel: false
      },
      toolbox: {
        show: true,
        feature: {
          myTrackModeSwitch: {
            show: true,
            title: trackMode === 'gradient' ? '切换为平滑轨迹' : '切换为线段轨迹',
            icon: trackMode === 'gradient'
              ? 'image://data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="' + (isDarkTheme ? '#fff' : '#333') + '" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-line-icon lucide-chart-line"><path d="M3 3v16a2 2 0 0 0 2 2h16"/><path d="m19 9-5 5-4-4-3 3"/></svg>')
              : 'image://data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="' + (isDarkTheme ? '#fff' : '#333') + '" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-spline-icon lucide-chart-spline"><path d="M3 3v16a2 2 0 0 0 2 2h16"/><path d="M7 16c.5-2 1.5-7 4-7 2 0 2 3 4 3 2.5 0 4.5-5 5-7"/></svg>'),
            onclick: function() {
              // 切换轨迹模式
              const newMode = trackMode === 'gradient' ? 'solid' : 'gradient';
              setTrackMode(newMode);
            }
          }
        },
        right: '5%',
        top: '5%',
        iconStyle: {
          borderColor: inCesiumInfoBox ? '#ccc' : (isDarkTheme ? '#ccc' : '#666')
        }
      },
      xAxis: {
        type: 'value',
        name: '',
        nameLocation: 'middle',
        nameGap: 0,
        min: adjustedMinLon,
        max: adjustedMaxLon,
        interval: (adjustedMaxLon - adjustedMinLon) / 5,
        scale: false,
        axisLabel: {
          formatter: (value: number) => `${value.toFixed(4)}°E`,
          margin: 12,
          rotate: 30,
          hideOverlap: true,
          color: inCesiumInfoBox ? '#ccc' : textColor,
          fontSize: 10
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: inCesiumInfoBox ? '#444' : splitLineColor
          }
        },
        axisLine: {
          lineStyle: {
            color: inCesiumInfoBox ? '#555' : axisLineColor
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameLocation: 'middle',
        nameGap: 0,
        min: adjustedMinLat,
        max: adjustedMaxLat,
        interval: (adjustedMaxLat - adjustedMinLat) / 5,
        scale: false,
        axisLabel: {
          formatter: (value: number) => `${value.toFixed(4)}°N`,
          margin: 10,
          hideOverlap: true,
          color: inCesiumInfoBox ? '#ccc' : textColor,
          fontSize: 10,
          rotate: 30
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: inCesiumInfoBox ? '#444' : splitLineColor
          }
        },
        axisLine: {
          lineStyle: {
            color: inCesiumInfoBox ? '#555' : axisLineColor
          }
        }
      },
      // 确保坐标系保持等比例
      aspectRatio: 1,
      animation: false,
      visualMap: [
        {
          show: false,
          min: 0,
          max: 1,
          seriesIndex: trackSeries.length - 1, // 应用于轨迹点系列（最后添加的系列）
          calculable: true,
          precision: 2,
          inRange: {
            color: ['rgba(24, 144, 255, 0.3)', 'rgba(24, 144, 255, 1)']
          }
        }
      ],
      series: trackSeries
    };
  };

  // 测量容器尺寸
  useLayoutEffect(() => {
    if (!chartRef.current) return;

    const updateContainerSize = () => {
      const container = chartRef.current;
      if (container) {
        const rect = container.getBoundingClientRect();
        setContainerSize({
          width: rect.width,
          height: rect.height
        });
      }
    };

    updateContainerSize();

    // 监听容器大小变化
    const resizeObserver = new ResizeObserver(updateContainerSize);
    if (chartRef.current) {
      resizeObserver.observe(chartRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 调整图表容器为正方形
  useEffect(() => {
    if (!chartRef.current || !containerSize.width) return;

    // 以容器宽度作为基准，保持正方形比例
    const squareSize = containerSize.width;

    // 设置容器高度为宽度相同，保持正方形比例
    chartRef.current.style.height = `${squareSize}px`;

    // 如果已经创建了图表实例，需要重新调整大小
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  }, [containerSize]);

  // 初始化和更新图表
  useEffect(() => {
    if (!chartRef.current) return;

    // 创建或获取已有的图表实例
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    // 设置图表选项
    const option = createChartOption();
    chartInstance.current.setOption(option);

    // 处理窗口大小变化时的图表自适应
    const handleResize = () => {
      chartInstance.current?.resize();
    };
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      // 在组件卸载时销毁图表实例
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [locationHistory, title, trackMode, containerSize, theme]);

  return (
    <div
      ref={chartRef}
      className="buoy-location-chart w-full"
      style={{
        width: '100%',
        height: 'auto',
        minHeight: '240px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    />
  );
};

/**
 * 关于地图底图的支持分析：
 *
 * ECharts 目前不直接支持在散点/线图底部添加卫星地图底图的功能，但有以下几种可能的解决方案：
 *
 * 1. 使用 ECharts 的 geo 组件：
 *    - ECharts 支持在 geo 组件上添加地图，但通常是矢量地图而非卫星图
 *    - 需要注册自定义地图数据（GeoJSON格式）
 *    - 优点：与ECharts集成度高
 *    - 缺点：无法直接使用卫星图像
 *
 * 2. 使用底层的 CSS 与定位技术：
 *    - 将地图图片作为容器的背景图
 *    - 需要计算坐标与像素的映射关系
 *    - 优点：实现简单
 *    - 缺点：不支持地图交互，如缩放、平移
 *
 * 3. 将 ECharts 叠加在地图库上：
 *    - 使用如 Leaflet 或 OpenLayers 作为底图
 *    - ECharts 图表绘制在底图之上的透明层
 *    - 需要同步两个组件的缩放和平移操作
 *    - 优点：可以使用成熟的地图库功能
 *    - 缺点：实现复杂，需要额外的协调工作
 *
 * 4. 使用 ECharts GL：
 *    - ECharts GL 扩展支持 3D 地球和地图
 *    - 可以加载卫星贴图
 *    - 优点：视觉效果好，支持3D
 *    - 缺点：需要额外加载 ECharts GL 库
 *
 * 5. 结合使用 Cesium：
 *    - 由于项目已经使用了 Cesium，可以考虑复用 Cesium 的地图能力
 *    - 将轨迹数据直接在 Cesium 中展示，而不是在 ECharts 中
 *    - 优点：充分利用 Cesium 的地图和 3D 能力
 *    - 缺点：需要放弃 ECharts 的一些可视化优势
 *
 * 建议的解决方案：
 * 短期：使用当前的 ECharts 图表，不添加底图
 * 中期：尝试方案3，将 ECharts 叠加在地图库上
 * 长期：开发专用的 Cesium 轨迹可视化组件，完全整合到 3D 地球中
 */

export default BuoyLocationChart;