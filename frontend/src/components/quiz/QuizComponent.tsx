import React, { useState, useEffect } from 'react';
import { getQuizQuestions, submitQuizAnswer } from '../../api/quizService';
import { QuizQuestion } from '../../types/quiz.types';

const QuizComponent: React.FC = () => {
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQuestions = async () => {
      try {
        setLoading(true);
        const response = await getQuizQuestions(5); // 获取5个问题
        setQuestions(response.data.questions);
        setLoading(false);
      } catch {
        setError('无法加载问题，请稍后再试。');
        setLoading(false);
      }
    };

    fetchQuestions();
  }, []);

  const handleOptionSelect = (optionId: number) => {
    setSelectedOption(optionId.toString());
  };

  const handleSubmit = async () => {
    if (!selectedOption || !questions[currentQuestionIndex]) return;

    try {
      const response = await submitQuizAnswer(questions[currentQuestionIndex].id.toString(), selectedOption);
      console.log('Submit Answer Response:', response.data);
      // 提交成功后，显示评分信息
      if (response.data.isCorrect) {
        alert('答案正确！');
      } else {
        const correctOption = currentQuestion.options.find(opt => opt.id === response.data.correctAnswerId);
        alert(`答案错误。正确答案是：${correctOption ? correctOption.text : '未知'}`);
      }
      // 清除选择并跳转到下一个问题
      setSelectedOption(null);
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
      } else {
        // 如果是最后一个问题，可以显示完成信息或重置
        setCurrentQuestionIndex(0);
        setQuestions([]);
        // 重新获取新的问题
        const newResponse = await getQuizQuestions(5);
        setQuestions(newResponse.data.questions);
      }
    } catch {
      setError('提交答案时出错，请重试。');
    }
  };

  if (loading) {
    return <div>加载中...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  if (questions.length === 0) {
    return <div>暂无问题</div>;
  }

  const currentQuestion = questions[currentQuestionIndex];
  console.log('Questions array:', questions);
  console.log('Current question index:', currentQuestionIndex);
  console.log('Current question:', currentQuestion);
  console.log('Options array:', currentQuestion.options);

  return (
    <div className="quiz-container p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">知识问答</h2>
      <div className="question mb-4">
        <p className="text-base font-medium">{currentQuestion.content}</p>
      </div>
      <div className="options mb-4">
        {currentQuestion.options.map((option, index) => (
          <button
            key={option.id}
            className={`block w-full text-left p-2 rounded-md mb-2 ${
              selectedOption === option.id.toString() ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => handleOptionSelect(option.id)}
            disabled={false}
          >
            {`${String.fromCharCode(65 + index)}: ${option.text}`}
          </button>
        ))}
      </div>
      <button
        className="submit-button bg-green-500 text-white px-4 py-2 rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed"
        onClick={handleSubmit}
        disabled={!selectedOption}
      >
        提交答案
      </button>
    </div>
  );
};

export default QuizComponent;