import React, { useState } from 'react';
import { useBuoyContext } from '../../contexts/BuoyContext';
import { useTheme } from '../../contexts/ThemeContext';
import { BuoyDataChart, BuoyLocationChart } from '../index';
import { DataLoadingIndicator } from '../ui';
import { formatUTCToLocal } from '../../utils/dateUtils';
import { AnalysisRequestItem } from '../../api/analysisService';
import { useToast } from '../ui/Toast';
import { useAnalysisReport } from '../../hooks/useAnalysisReport';
import AnalysisReportPanel from '../analysis/AnalysisReportPanel';

// 分隔面板图标组件
const SplitPanelIcon = ({ className = '' }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <rect width="18" height="18" x="3" y="3" rx="2"></rect>
    <path d="M15 3v18"></path>
  </svg>
);

interface BuoySidebarProps {
  className?: string;
  collapsed: boolean;
  toggleCollapsed: () => void;
}

const BuoyInfoSidebar: React.FC<BuoySidebarProps> = ({ className = '', collapsed, toggleCollapsed }) => {
  const {
    selectedBuoyId,
    sensorData,
    locationHistory,
    buoys,
    loadingSensorData,
    loadingLocationHistory
  } = useBuoyContext();
  const { theme } = useTheme();

  // 智能分析报告 hook
  const { loading: reportLoading, error: reportError, data: reportData, fetchReport } = useAnalysisReport();

  // 查找选中的浮标对象
  const selectedBuoy = buoys.find(buoy => buoy.id === selectedBuoyId);

  // 渲染折叠状态下的侧边栏
  if (collapsed) {
    return (
      <div className={`flex flex-col items-center ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-md h-full ${className}`}>
        <button
          onClick={toggleCollapsed}
          className={`p-2 my-2 rounded-md ${theme === 'dark' ? 'bg-gray-700 text-gray-100 hover:bg-gray-600' : 'bg-blue-50 text-blue-600 hover:bg-blue-100'}`}
          title="展开浮标信息"
        >
          <SplitPanelIcon className="text-xl" />
        </button>
        {selectedBuoyId && (
          <div
            className={`p-2 my-2 rounded-full ${selectedBuoy?.status === 'active' ? 'bg-green-400' : 'bg-yellow-400'}`}
            title={selectedBuoy?.name || '浮标详情'}
          >
          </div>
        )}
      </div>
    );
  }

  // 智能分析回调
  const handleAnalysis = (items: AnalysisRequestItem[]) => {
    fetchReport({ items });
  };

  return (
    <div className={`p-4 overflow-y-auto flex flex-col space-y-4 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} ${className} relative`}>
      <button
        onClick={toggleCollapsed}
        className={`absolute top-2 right-2 p-2 rounded-md ${theme === 'dark' ? 'bg-gray-700 text-gray-100 hover:bg-gray-600' : 'bg-blue-50 text-blue-600 hover:bg-blue-100'}`}
        title="收起浮标信息"
      >
        <SplitPanelIcon className="text-xl" />
      </button>

      {selectedBuoyId ? (
        <>
          <div className={`rounded-lg shadow p-4 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} mt-8`}>
            <h2 className={`text-xl font-bold mb-4 flex items-center ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
              <span className={`mr-2 flex-shrink-0 w-3 h-3 rounded-full ${selectedBuoy?.status === 'active' ? 'bg-green-400' : 'bg-yellow-400'}`}></span>
              传感器数据
            </h2>
            <DataLoadingIndicator
              isLoading={loadingSensorData}
              label="加载传感器数据..."
              overlay={sensorData.length > 0}
            >
              {sensorData.length > 0 ? (
                <>
                  {/* 智能分析多选与按钮 */}
                  <SensorAnalysisSelector
                    sensorData={sensorData}
                    onAnalysis={handleAnalysis}
                    analysisLoading={reportLoading}
                  />
                  <AnalysisReportPanel
                    report={reportData}
                    loading={reportLoading}
                    error={reportError}
                  />
                  <BuoyDataChart
                    sensorData={sensorData}
                    chartType="sensor"
                    height="250px"
                    title={`${selectedBuoy?.name || ''}`}
                  />
                </>
              ) : (
                <p className={`${theme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}>没有找到该浮标的传感器数据。</p>
              )}
            </DataLoadingIndicator>
          </div>
          <div className={`rounded-lg shadow p-4 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
            <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>轨迹数据</h2>
            <DataLoadingIndicator
              isLoading={loadingLocationHistory}
              label="加载轨迹数据..."
              overlay={locationHistory.length > 0}
            >
              {locationHistory.length > 0 ? (
                <BuoyLocationChart
                  locationHistory={locationHistory}
                  title={`${selectedBuoy?.name || ''}`}
                />
              ) : (
                <p className={`${theme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}>没有找到该浮标的轨迹数据。</p>
              )}
            </DataLoadingIndicator>
          </div>
          <div className={`rounded-lg shadow p-4 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
            <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>浮标信息</h2>
            <div className="space-y-2">
              <div className={`flex justify-between p-2 rounded ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <span className={`${theme === 'dark' ? 'text-gray-100' : 'text-gray-600'}`}>ID</span>
                <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{selectedBuoy?.id}</span>
              </div>
              <div className={`flex justify-between p-2 rounded ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <span className={`${theme === 'dark' ? 'text-gray-100' : 'text-gray-600'}`}>名称</span>
                <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{selectedBuoy?.name}</span>
              </div>
              <div className={`flex justify-between p-2 rounded ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <span className={`${theme === 'dark' ? 'text-gray-100' : 'text-gray-600'}`}>状态</span>
                <span className={`font-medium ${selectedBuoy?.status === 'active' ? 'text-green-500' : 'text-yellow-500'}`}>
                  {selectedBuoy?.status === 'active' ? '活跃' : '不活跃'}
                </span>
              </div>
              <div className={`flex justify-between p-2 rounded ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <span className={`${theme === 'dark' ? 'text-gray-100' : 'text-gray-600'}`}>最后更新</span>
                <span className={`font-medium text-right ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{formatUTCToLocal(selectedBuoy?.last_heartbeat)}</span>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="flex items-center mt-12 justify-center h-full">
          <p className={`text-lg ${theme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}>请在地图上选择一个浮标以查看数据。</p>
        </div>
      )}
    </div>
  );
};

/**
 * 智能分析多选与按钮子组件
 */
interface SensorAnalysisSelectorProps {
  sensorData: import('../../types/buoy.types').SensorData[];
  onAnalysis: (items: AnalysisRequestItem[]) => void;
  analysisLoading?: boolean;
}
const SensorAnalysisSelector: React.FC<SensorAnalysisSelectorProps> = ({ sensorData, onAnalysis, analysisLoading }) => {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const toast = useToast();

  // 选择切换
  const handleSelect = (id: string) => {
    setSelectedIds(prev =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };

  // 组装API请求体
  const buildRequestItems = (): AnalysisRequestItem[] => {
    return sensorData
      .filter(d => selectedIds.includes(d.id))
      .map(d => ({
        buoy_id: d.buoyId,
        sensor_type: d.data_type,
        data: {
          timestamp: d.timestamp,
          value: typeof d.value === 'number' ? d.value : 0,
          unit: d.unit,
        },
      }));
  };

  // 智能分析主流程
  const handleAnalysis = () => {
    if (selectedIds.length === 0) {
      toast.showToast('请至少选择一条传感器数据', 'warning');
      return;
    }
    const items = buildRequestItems();
    onAnalysis(items);
    toast.showToast('正在分析，请稍候...', 'info');
  };

  // 渲染多选列表
  return (
    <div className="mb-4">
      <div className="flex flex-wrap gap-2 mb-2">
        {sensorData.map(d => (
          <label key={d.id} className="flex items-center space-x-1 text-xs px-2 py-1 rounded border cursor-pointer"
            style={{ borderColor: selectedIds.includes(d.id) ? '#3b82f6' : '#d1d5db', background: selectedIds.includes(d.id) ? '#e0e7ff' : 'transparent' }}>
            <input
              type="checkbox"
              checked={selectedIds.includes(d.id)}
              onChange={() => handleSelect(d.id)}
              disabled={analysisLoading}
              className="mr-1"
            />
            <span>
              {d.data_type}{' '}
              {typeof d.value === 'number' ? d.value : '[对象]'}
              {d.unit}{' '}
              <span className="text-gray-400">({formatUTCToLocal(d.timestamp)})</span>
            </span>
          </label>
        ))}
      </div>
      <button
        className={`px-4 py-1 rounded bg-blue-500 text-white text-sm font-medium hover:bg-blue-600 transition disabled:opacity-50`}
        disabled={selectedIds.length === 0 || analysisLoading}
        onClick={handleAnalysis}
      >
        {analysisLoading ? '分析中...' : '智能分析'}
      </button>
    </div>
  );
};

export default BuoyInfoSidebar;