import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useToast, Toast } from '../../components/ui/Toast';
import { FiBell, FiUser, FiSettings, FiLogOut, FiArrowDown, FiCheck, FiSun, FiMoon } from 'react-icons/fi';
import { WebSocketStatusIndicator } from '../../components/websocket';

interface TopNavProps {
  className?: string;
}

const TopNav: React.FC<TopNavProps> = ({ className = '' }) => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout, isAuthenticated } = useAuth();
  const { history: notifications, markAsRead, markAllAsRead } = useToast();
  const navigate = useNavigate();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // 处理登出
  const handleLogout = async () => {
    try {
      // 执行登出
      logout();
      // 关闭菜单
      setShowUserMenu(false);
      // 重定向到登录页
      navigate('/auth');
    } catch (error) {
      console.error('登出失败', error);
    }
  };

  // 关闭下拉菜单的点击外部事件处理
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target as Node)
      ) {
        setShowNotifications(false);
      }

      if (
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target as Node)
      ) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 未读通知计数
  const unreadCount = notifications.filter(n => !n.read).length;

  // 处理单个通知的已读标记
  const handleMarkAsRead = (id: string) => {
    markAsRead(id);
  };

  // 处理全部标记为已读
  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  // 只修改通知显示部分
  const getNotificationInfo = (notification: Toast) => {
    // 根据通知类型返回友好的来源名称
    const getSource = () => {
      switch(notification.type) {
        case 'success': return 'System';
        case 'error': return 'Error';
        case 'warning': return 'Warning';
        case 'info': return 'Info';
        default: return notification.from || 'System';
      }
    };

    // 获取格式化的时间
    const getTime = () => {
      // 如果有time属性就使用，否则尝试从id中提取时间戳
      if (notification.time) return notification.time;

      // 从ID中提取时间戳部分(id格式通常是时间戳-随机字符串)
      const timestampStr = notification.id.split('-')[0];
      if (timestampStr && !isNaN(Number(timestampStr))) {
        const date = new Date(Number(timestampStr));

        // 如果是今天的通知，只显示时间
        const now = new Date();
        if (date.toDateString() === now.toDateString()) {
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }

        // 否则显示日期和时间
        return date.toLocaleString([], {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }

      return '未知时间';
    };

    return {
      source: getSource(),
      time: getTime()
    };
  };

  return (
    <div className={`flex items-center justify-between px-4 py-2 border-b ${theme === 'dark' ? 'bg-gray-900 border-gray-700 text-white' : 'bg-white border-gray-200 text-gray-800'} ${className}`}>
      {/* 左侧品牌Logo */}
      <div className="flex items-center">
        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-2">
          X
        </div>
        <span className="font-bold text-lg hidden md:inline">浮标系统</span>
      </div>

      {/* 右侧功能区 */}
      <div className="flex items-center space-x-2">
        {/* WebSocket状态指示器 */}
        {isAuthenticated && (
          <div className="flex items-center">
            <WebSocketStatusIndicator showLabel={false} pulseEffect={true} showToasts={false} />
          </div>
        )}

        {/* 主题切换按钮 */}
        <button
          onClick={toggleTheme}
          className={`
            p-2 rounded-full
            ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}
          `}
          aria-label={theme === 'light' ? '切换到深色模式' : '切换到浅色模式'}
        >
          {theme === 'light' ? (
            <FiMoon className="w-5 h-5" />
          ) : (
            <FiSun className="w-5 h-5" />
          )}
        </button>
{/* 分享到微博按钮 */}
        <button
          onClick={() => {
            const title = document.title;
            const url = window.location.href;
            const shareUrl = `https://service.weibo.com/share/share.php?title=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`;
            window.open(shareUrl, '_blank', 'width=600,height=500');
          }}
          className="p-2 rounded-full hover:bg-red-100 text-[#e6162d]"
          aria-label="分享到微博"
          title="分享到微博"
        >
          {/* 微博 SVG 图标 */}
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50.497 40.648" width="20" height="20" fill="currentColor">
            <path fill="#E99315" d="M50.448 12.132c.217 2.814-.259 6.186-2.117 6.351-3.033.271-1.451-3.07-1.411-5.081.111-5.829-4.865-9.879-9.739-9.879-1.381 0-4.588.936-4.094-1.976.222-1.284 1.31-1.266 2.399-1.411 8.197-1.093 14.386 4.546 14.962 11.996z"/>
            <path fill="#D52A2C" d="M37.04 18.907c3.524 1.928 7.758 2.888 7.056 8.61-.168 1.371-.998 3.203-1.834 4.373-5.957 8.339-23.924 11.844-35.144 5.506C3.355 35.269-.539 32.159.062 25.962c.517-5.333 4.103-9.464 7.622-12.983 3.357-3.359 6.897-5.987 11.714-7.198 5.226-1.314 6.771 3.043 5.363 7.339 3.027-.203 9.442-3.582 12.279-.282 1.25 1.454.771 4.058 0 6.069zm-3.811 13.548c1.129-1.28 2.264-3.231 2.257-5.503-.015-7.014-8.851-9.605-15.806-9.033-3.804.312-6.363 1.115-9.033 2.682-2.179 1.279-4.729 3.36-5.363 6.491-1.427 7.041 6.231 10.35 11.855 10.726 6.498.437 13.002-1.857 16.09-5.363z"/>
            <path fill="#E99315" d="M43.531 12.132c.296 2.149-.319 4.011-1.552 4.093-2.056.137-1.287-1.408-1.412-3.246-.078-1.132-1.016-2.439-1.835-2.823-1.606-.752-4.093.548-4.093-1.693 0-1.664 1.443-1.491 2.259-1.553 3.574-.272 6.216 2.191 6.633 5.222z"/>
            <path d="M27.019 26.246c3.007 9.088-12.66 13.314-15.525 5.504-1.917-5.223 2.686-9.377 7.48-9.879 4.093-.429 7.144 1.658 8.045 4.375zm-7.198 1.553c.638 1.104 2.105.311 1.976-.564-.154-1.013-1.989-.863-1.976.564zm-2.541 4.799c2.634-.627 2.988-5.588-.988-4.658-3.34.78-2.694 5.533.988 4.658z"/>
          </svg>
        </button>

        {isAuthenticated && (
          <>
            {/* 通知按钮和下拉菜单 */}
            <div className="relative" ref={notificationRef}>
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className={`
                  relative p-2 rounded-full
                  ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}
                `}
                aria-label="通知"
              >
                <FiBell className="w-5 h-5" />
                {unreadCount > 0 && (
                  <span className="absolute top-1 right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {unreadCount}
                  </span>
                )}
              </button>

              {/* 通知下拉面板 */}
              {showNotifications && (
                <div className={`
                  absolute right-0 mt-2 w-80 rounded-lg shadow-lg z-[2000] overflow-hidden
                  ${theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'}
                  border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}
                `}>
                  <div className="p-3 border-b flex justify-between items-center font-medium">
                    <span>通知</span>
                    {unreadCount > 0 && (
                      <button
                        className="text-xs text-blue-500 hover:text-blue-600 flex items-center"
                        onClick={handleMarkAllAsRead}
                      >
                        <FiCheck className="w-3 h-3 mr-1" /> 标记全部已读
                      </button>
                    )}
                  </div>

                  <div className="max-h-96 overflow-y-auto">
                    {notifications.length > 0 ? (
                      notifications.map(notification => {
                        const { source, time } = getNotificationInfo(notification);
                        return (
                          <div
                            key={notification.id}
                            className={`
                              p-4 border-b ${theme === 'dark' ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-100 hover:bg-gray-50'}
                              ${!notification.read ? (theme === 'dark' ? 'bg-gray-700/30' : 'bg-blue-50/30') : ''}
                              cursor-pointer
                            `}
                            onClick={() => handleMarkAsRead(notification.id)}
                          >
                            <div className="flex justify-between mb-1">
                              <span className="font-medium text-sm">{source}</span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">{time}</span>
                            </div>
                            <p className="text-sm">{notification.message}</p>
                          </div>
                        );
                      })
                    ) : (
                      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                        没有通知
                      </div>
                    )}
                  </div>

                  {notifications.length > 0 && (
                    <div className="p-2 text-center">
                      <button
                        className="w-full py-2 text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                        onClick={() => setShowNotifications(false)}
                      >
                        关闭
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 用户头像和下拉菜单 */}
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center"
                aria-label="用户菜单"
              >
                <div className="flex items-center space-x-2 p-1 rounded-full overflow-hidden group">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                    <FiUser className="w-5 h-5" />
                  </div>
                  <span className="hidden md:inline text-sm font-medium">{user?.username || '用户'}</span>
                  <FiArrowDown className={`w-4 h-4 transition-transform ${showUserMenu ? 'rotate-180' : ''} hidden md:inline`} />
                </div>
              </button>

              {/* 用户菜单下拉面板 */}
              {showUserMenu && (
                <div className={`
                  absolute right-0 mt-2 w-48 rounded-lg shadow-lg z-[2000] overflow-hidden
                  ${theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'}
                  border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}
                `}>
                  <div className="p-3 border-b flex items-center">
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 mr-3">
                      <FiUser className="w-6 h-6" />
                    </div>
                    <div>
                      <div className="font-medium text-sm">{user?.username || '用户'}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{user?.email || ''}</div>
                    </div>
                  </div>

                  <div className="py-1">
                    {user?.role === 'admin' && (
                      <Link
                        to="/admin"
                        className={`
                          flex items-center px-4 py-2 text-sm
                          ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}
                        `}
                      >
                        <FiSettings className="w-4 h-4 mr-3" />
                        <span>管理系统</span>
                      </Link>
                    )}

                    <Link
                      to="/account"
                      className={`
                        flex items-center px-4 py-2 text-sm
                        ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}
                      `}
                    >
                      <FiUser className="w-4 h-4 mr-3" />
                      <span>我的账户</span>
                    </Link>

                    <div className={`my-1 h-px ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'}`} />

                    <button
                      onClick={handleLogout}
                      className={`
                        w-full flex items-center px-4 py-2 text-sm text-red-500
                        ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}
                      `}
                    >
                      <FiLogOut className="w-4 h-4 mr-3" />
                      <span>退出登录</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {!isAuthenticated && (
          <Link
            to="/auth"
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm"
          >
            登录
          </Link>
        )}
      </div>
    </div>
  );
};

export default TopNav;