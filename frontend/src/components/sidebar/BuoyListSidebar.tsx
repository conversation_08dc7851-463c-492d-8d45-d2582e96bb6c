// 浮标列表侧边栏组件
import React, { useState, useEffect } from 'react';
import { useBuoyContext } from '../../contexts/BuoyContext';
import { useTheme } from '../../contexts/ThemeContext';
import AnalysisTab from '../analysis/AnalysisTab';
import { BuoyStatusMonitor } from '../websocket';

// 分隔面板图标（与 BuoyInfoSidebar 保持一致）
const SplitPanelIcon = ({ className = '' }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <rect width="18" height="18" x="3" y="3" rx="2"></rect>
    <path d="M15 3v18"></path>
  </svg>
);

interface BuoyListSidebarProps {
  className?: string;
  collapsed: boolean;
  toggleCollapsed: () => void;
}

/**
 * 浮标列表侧边栏组件
 * - 展示所有浮标
 * - 高亮当前选中浮标
 * - 支持点击切换选中浮标
 */
const BuoyListSidebar: React.FC<BuoyListSidebarProps> = ({
  className = '',
  collapsed,
  toggleCollapsed,
}) => {
  // 获取主题色
  const { theme, colors } = useTheme();
  const {
    buoys,
    selectedBuoyId,
    setSelectedBuoyId,
    loadingBuoys,
    sensorData,
  } = useBuoyContext();

  // Tab 状态：0-浮标列表，1-智能分析，2-阈值配置，3-状态监控
  const [activeTab, setActiveTab] = useState<number>(0);

  // 阈值本地存储（支持上限/下限可选）
  const [thresholds, setThresholds] = useState<{ [key: string]: { upper?: number | string; lower?: number | string } }>({});

  // 页面加载时读取 localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sensorThresholds');
    if (saved) {
      try {
        setThresholds(JSON.parse(saved));
      } catch {
        setThresholds({});
      }
    }
  }, []);

  // 阈值变更时写入 localStorage
  useEffect(() => {
    localStorage.setItem('sensorThresholds', JSON.stringify(thresholds));
  }, [thresholds]);

  // 处理传感器类型与数值
  const sensorTypeMap: { [type: string]: { values: number[]; unit: string } } = {};
  sensorData.forEach((d) => {
    if (typeof d.value === 'number') {
      if (!sensorTypeMap[d.data_type]) {
        sensorTypeMap[d.data_type] = { values: [], unit: d.unit };
      }
      if (!sensorTypeMap[d.data_type].values.includes(d.value)) {
        sensorTypeMap[d.data_type].values.push(d.value);
      }
    }
  });
  // 排序
  Object.values(sensorTypeMap).forEach(obj => obj.values.sort((a, b) => a - b));

  // 折叠状态下的侧边栏
  if (collapsed) {
    return (
      <div
        className={`flex flex-col items-center ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-md h-full ${className}`}
      >
        <button
          onClick={toggleCollapsed}
          className={`p-2 my-2 rounded-md ${theme === 'dark'
            ? 'bg-gray-700 text-gray-100 hover:bg-gray-600'
            : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
            }`}
          title="展开浮标列表"
        >
          <SplitPanelIcon className="text-xl" />
        </button>
        {selectedBuoyId && (
          <div
            className={`p-2 my-2 rounded-full ${buoys.find(b => b.id === selectedBuoyId)?.status === 'active'
              ? 'bg-green-400'
              : 'bg-yellow-400'
              }`}
            title={buoys.find(b => b.id === selectedBuoyId)?.name || '浮标'}
          />
        )}
      </div>
    );
  }

  return (
    <div
      className={`p-2 sm:p-4 overflow-y-auto flex flex-col ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-md h-full ${className} relative`}
    >
      {/* 折叠按钮 */}
      <button
        onClick={toggleCollapsed}
        className={`absolute top-1 right-1 sm:top-2 sm:right-2 p-1 sm:p-2 rounded-md z-10 ${theme === 'dark'
          ? 'bg-gray-700 text-gray-100 hover:bg-gray-600'
          : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
          } transition-colors duration-200`}
        title="收起浮标列表"
      >
        <SplitPanelIcon className="text-lg sm:text-xl" />
      </button>

      {/* Tab 切换 - 修复布局问题，确保在所有缩放等级下都可见 */}
      <div className="flex flex-wrap gap-1 mt-8 mb-2 min-h-[2.5rem]">
        <button
          className={`flex-1 min-w-[3rem] px-1 py-1 rounded-t text-xs sm:text-sm whitespace-nowrap ${activeTab === 0
            ? theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-blue-100 text-blue-800'
            : theme === 'dark' ? 'bg-gray-900 text-gray-400 hover:bg-gray-800' : 'bg-gray-200 text-gray-500 hover:bg-gray-300'
            } transition-colors duration-200`}
          onClick={() => setActiveTab(0)}
          title="浮标列表"
        >
          浮标列表
        </button>
        <button
          className={`flex-1 min-w-[3rem] px-1 py-1 rounded-t text-xs sm:text-sm whitespace-nowrap ${activeTab === 1
            ? theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-blue-100 text-blue-800'
            : theme === 'dark' ? 'bg-gray-900 text-gray-400 hover:bg-gray-800' : 'bg-gray-200 text-gray-500 hover:bg-gray-300'
            } transition-colors duration-200`}
          onClick={() => setActiveTab(1)}
          title="智能分析"
        >
          智能分析
        </button>
        <button
          className={`flex-1 min-w-[3rem] px-1 py-1 rounded-t text-xs sm:text-sm whitespace-nowrap ${activeTab === 2
            ? theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-blue-100 text-blue-800'
            : theme === 'dark' ? 'bg-gray-900 text-gray-400 hover:bg-gray-800' : 'bg-gray-200 text-gray-500 hover:bg-gray-300'
            } transition-colors duration-200`}
          onClick={() => setActiveTab(2)}
          title="阈值配置"
        >
          阈值配置
        </button>
        <button
          className={`flex-1 min-w-[3rem] px-1 py-1 rounded-t text-xs sm:text-sm whitespace-nowrap ${activeTab === 3
            ? theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-blue-100 text-blue-800'
            : theme === 'dark' ? 'bg-gray-900 text-gray-400 hover:bg-gray-800' : 'bg-gray-200 text-gray-500 hover:bg-gray-300'
            } transition-colors duration-200`}
          onClick={() => setActiveTab(3)}
          title="状态监控"
        >
          状态监控
        </button>
      </div>

      {/* Tab 内容 */}
      {activeTab === 0 ? (
        <>
          <h2 className={`text-xl font-bold mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>浮标列表</h2>
          {/* 加载中 */}
          {loadingBuoys ? (
            <div className="flex items-center justify-center h-32">
              <span className={`text-base ${theme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}>正在加载浮标列表...</span>
            </div>
          ) : buoys.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <span className={`text-base ${theme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}>暂无浮标数据</span>
            </div>
          ) : (
            <ul className="space-y-2">
              {buoys.map((buoy) => {
                const isSelected = buoy.id === selectedBuoyId;
                return (
                  <li
                    key={buoy.id}
                    className={`flex items-center p-3 rounded-lg cursor-pointer shadow transition
                      ${isSelected
                        ? theme === 'dark'
                          ? 'bg-blue-900 border border-blue-400'
                          : 'bg-blue-100 border border-blue-400'
                        : theme === 'dark'
                          ? 'hover:bg-gray-700'
                          : 'hover:bg-gray-50'
                      }
                    `}
                    onClick={() => setSelectedBuoyId(buoy.id)}
                    title={buoy.name}
                  >
                    {/* 状态圆点 */}
                    <span
                      className={`w-3 h-3 rounded-full mr-3 flex-shrink-0
                        ${buoy.status === 'active'
                          ? 'bg-green-400'
                          : buoy.status === 'inactive'
                            ? 'bg-yellow-400'
                            : 'bg-red-400'
                        }`}
                      title={buoy.status === 'active' ? '活跃' : buoy.status === 'inactive' ? '不活跃' : '异常'}
                    />
                    <div className="flex-1 min-w-0">
                      <div className={`font-medium truncate ${isSelected
                        ? theme === 'dark' ? 'text-blue-200' : 'text-blue-800'
                        : theme === 'dark' ? 'text-white' : 'text-gray-800'
                        }`}>
                        {buoy.name}
                      </div>
                      <div className={`text-xs truncate ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        ID: {buoy.id}
                      </div>
                      <div className={`text-xs truncate ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        最后心跳: {buoy.last_heartbeat ? new Date(buoy.last_heartbeat).toLocaleString() : '无'}
                      </div>
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        </>
      ) : activeTab === 1 ? (
        // 智能分析 Tab
        <AnalysisTab sensorData={sensorData} />
      ) : activeTab === 2 ? (
        // 阈值配置 Tab
        <div>
          <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>阈值配置</h2>
          {Object.keys(sensorTypeMap).length === 0 ? (
            <div className="text-gray-400 text-center">暂无传感器数据</div>
          ) : (
            <div className="space-y-6">
              {Object.entries(sensorTypeMap).map(([type, { unit }]) => (
                <div key={type} className="border-b pb-4" style={{ color: colors.text.primary }}>
                  <div className="font-semibold mb-2 ">{type}（{unit}）</div>
                  {/* 响应式阈值配置布局 */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                    <div className="flex items-center gap-2 flex-1">
                      <span className="text-xs sm:text-sm whitespace-nowrap" style={{ color: colors.text.primary }}>下限：</span>
                      <input
                        type="number"
                        className="border rounded px-2 py-1 w-16 sm:w-20 text-xs sm:text-sm"
                        style={{ color: colors.text.primary, background: colors.background.main }}
                        value={thresholds[type]?.lower ?? ''}
                        onChange={e => {
                          const val = e.target.value;
                          setThresholds(prev => ({
                            ...prev,
                            [type]: {
                              ...prev[type],
                              lower: val
                            }
                          }));
                        }}
                        placeholder="可选"
                      />
                    </div>
                    <div className="flex items-center gap-2 flex-1">
                      <span className="text-xs sm:text-sm whitespace-nowrap" style={{ color: colors.text.primary }}>上限：</span>
                      <input
                        type="number"
                        className="border rounded px-2 py-1 w-16 sm:w-20 text-xs sm:text-sm"
                        style={{ color: colors.text.primary, background: colors.background.main }}
                        value={thresholds[type]?.upper ?? ''}
                        onChange={e => {
                          const val = e.target.value;
                          setThresholds(prev => ({
                            ...prev,
                            [type]: {
                              ...prev[type],
                              upper: val
                            }
                          }));
                        }}
                        placeholder="可选"
                      />
                    </div>
                    {unit && (
                      <span
                        className="text-xs whitespace-nowrap"
                        style={{ color: colors.text.secondary }}
                      >
                        ({unit})
                      </span>
                    )}
                    <style>
                      {`
                        input::placeholder {
                          color: ${colors.text.secondary};
                          opacity: 1;
                        }
                      `}
                    </style>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        // 状态监控 Tab
        <div>
          <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>状态监控</h2>
          <BuoyStatusMonitor className="w-full" maxEvents={30} />
        </div>
      )}
    </div>
  );
};



export default BuoyListSidebar;