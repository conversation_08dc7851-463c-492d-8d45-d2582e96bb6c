import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type ThemeType = 'light' | 'dark';

interface ThemeColors {
  primary: string;
  secondary: string;
  background: {
    main: string;
    secondary: string;
  };
  text: {
    primary: string;
    secondary: string;
  };
  border: string;
  buoy: {
    active: string;
    inactive: string;
    error: string;
    default: string;
  };
}

interface ThemeContextType {
  theme: ThemeType;
  colors: ThemeColors;
  toggleTheme: () => void;
  setTheme: (theme: ThemeType) => void;
}

// 定义主题颜色
const themes: Record<ThemeType, ThemeColors> = {
  light: {
    primary: '#3B82F6', // blue-500
    secondary: '#10B981', // emerald-500
    background: {
      main: '#FFFFFF',
      secondary: '#F9FAFB',
    },
    text: {
      primary: '#111827', // gray-900
      secondary: '#6B7280', // gray-500
    },
    border: '#E5E7EB', // gray-200
    buoy: {
      active: '#FBBF24', // yellow
      inactive: '#9CA3AF', // gray
      error: '#EF4444', // red
      default: '#3B82F6', // blue
    },
  },
  dark: {
    primary: '#60A5FA', // blue-400
    secondary: '#34D399', // emerald-400
    background: {
      main: '#1F2937', // gray-800
      secondary: '#111827', // gray-900
    },
    text: {
      primary: '#F9FAFB', // gray-50
      secondary: '#F3F4F6', // 修改为更亮的颜色 gray-100
    },
    border: '#374151', // gray-700
    buoy: {
      active: '#FBBF24', // yellow
      inactive: '#6B7280', // gray
      error: '#F87171', // red-400
      default: '#60A5FA', // blue-400
    },
  },
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  // 从localStorage读取主题设置，默认为light
  const [currentTheme, setCurrentTheme] = useState<ThemeType>(() => {
    const savedTheme = localStorage.getItem('theme');
    return (savedTheme as ThemeType) || 'light';
  });

  // 切换主题
  const toggleTheme = () => {
    setCurrentTheme(prevTheme => {
      const newTheme = prevTheme === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', newTheme);
      return newTheme;
    });
  };

  // 设置特定主题
  const setTheme = (theme: ThemeType) => {
    localStorage.setItem('theme', theme);
    setCurrentTheme(theme);
  };

  // 当主题变化时更新CSS变量
  useEffect(() => {
    const root = document.documentElement;
    const currentColors = themes[currentTheme];
    
    // 设置CSS变量
    root.style.setProperty('--color-primary', currentColors.primary);
    root.style.setProperty('--color-secondary', currentColors.secondary);
    root.style.setProperty('--color-bg-main', currentColors.background.main);
    root.style.setProperty('--color-bg-secondary', currentColors.background.secondary);
    root.style.setProperty('--color-text-primary', currentColors.text.primary);
    root.style.setProperty('--color-text-secondary', currentColors.text.secondary);
    root.style.setProperty('--color-border', currentColors.border);
    
    // 浮标颜色
    root.style.setProperty('--color-buoy-active', currentColors.buoy.active);
    root.style.setProperty('--color-buoy-inactive', currentColors.buoy.inactive);
    root.style.setProperty('--color-buoy-error', currentColors.buoy.error);
    root.style.setProperty('--color-buoy-default', currentColors.buoy.default);
    
    // 设置data-theme属性用于CSS选择器
    document.body.setAttribute('data-theme', currentTheme);
  }, [currentTheme]);

  return (
    <ThemeContext.Provider value={{ 
      theme: currentTheme, 
      colors: themes[currentTheme], 
      toggleTheme,
      setTheme
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

// 自定义Hook方便使用主题
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme必须在ThemeProvider内使用');
  }
  return context;
}; 