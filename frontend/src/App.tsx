import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ProtectedRoute } from './components';
import { ToastProvider } from './components/ui';
import { useToast } from './components/ui/Toast';
import AuthPage from './pages/AuthPage';
import UserAccountPage from './pages/UserAccountPage';
import HomePage from './pages/HomePage';
import QuizPage from './pages/QuizPage';
import AdminPage from './pages/AdminPage';
import { useEffect } from 'react';
import { setAuthExpiredHandler } from './api/apiClient';

// 创建一个内部组件来处理认证和导航
const AppContent = () => {
  const { handleAuthExpired } = useAuth();
  const { showToast } = useToast();

  // 设置API客户端的认证过期处理函数
  useEffect(() => {
    // 创建一个包装函数，添加Toast通知
    const handleAuthExpiredWithToast = (returnPath?: string) => {
      // 显示会话过期提示
      showToast('会话已过期，请重新登录', 'warning');
      // 调用AuthContext的处理函数
      handleAuthExpired(returnPath);
    };

    // 设置API客户端的处理函数
    setAuthExpiredHandler(handleAuthExpiredWithToast);
  }, [handleAuthExpired, showToast]);

  return (
    <>
      <Routes>
        <Route path="/auth" element={<AuthPage />} />
        <Route
          path="/home"
          element={
            <ProtectedRoute>
              <HomePage />
            </ProtectedRoute>
          }
        />
      <Route
        path="/account"
        element={
          <ProtectedRoute>
            <UserAccountPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/*"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/quiz"
        element={
          <ProtectedRoute>
            <QuizPage />
          </ProtectedRoute>
        }
      />
      <Route path="/" element={<Navigate to="/auth" replace />} />
    </Routes>
    </>
  );
};

function App() {
  return (
    <ThemeProvider>
      <ToastProvider>
        <Router>
          <AuthProvider>
            <AppContent />
          </AuthProvider>
        </Router>
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;
