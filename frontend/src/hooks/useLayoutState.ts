import { useState, useEffect } from 'react';

// 定义布局状态接口
export interface LayoutState {
  isMobile: boolean;
  leftSidebarOpen: boolean;
  rightSidebarOpen: boolean;
  navCollapsed: boolean;
  buoyInfoCollapsed: boolean;
}

// 定义布局操作接口
export interface LayoutActions {
  toggleLeftSidebar: () => void;
  toggleRightSidebar: () => void;
  toggleNavCollapsed: () => void;
  toggleBuoyInfoCollapsed: () => void;
}

/**
 * 自定义Hook，用于管理应用布局状态
 * @param mobileBreakpoint 移动设备断点宽度（像素）
 * @returns 布局状态和操作方法
 */
export const useLayoutState = (mobileBreakpoint = 768): [LayoutState, LayoutActions] => {
  // 初始化布局状态
  const [state, setState] = useState<LayoutState>({
    isMobile: window.innerWidth < mobileBreakpoint,
    leftSidebarOpen: window.innerWidth >= mobileBreakpoint,
    rightSidebarOpen: window.innerWidth >= mobileBreakpoint,
    navCollapsed: window.innerWidth < mobileBreakpoint,
    buoyInfoCollapsed: window.innerWidth < mobileBreakpoint
  });

  // 检测屏幕尺寸变化
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < mobileBreakpoint;
      
      setState(prevState => ({
        ...prevState,
        isMobile: isMobileView,
        // 在桌面端默认打开侧边栏，在移动端默认关闭
        leftSidebarOpen: isMobileView ? false : true,
        rightSidebarOpen: isMobileView ? false : true,
        // navCollapsed: isMobileView ? true : false,
        navCollapsed: true,
        buoyInfoCollapsed: isMobileView ? true : false
      }));
    };

    // 初始检测
    checkMobile();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', checkMobile);
    
    // 清理函数
    return () => window.removeEventListener('resize', checkMobile);
  }, [mobileBreakpoint]);

  // 定义操作方法
  const actions: LayoutActions = {
    toggleLeftSidebar: () => setState(prev => ({ ...prev, leftSidebarOpen: !prev.leftSidebarOpen })),
    toggleRightSidebar: () => setState(prev => ({ ...prev, rightSidebarOpen: !prev.rightSidebarOpen })),
    toggleNavCollapsed: () => setState(prev => ({ ...prev, navCollapsed: !prev.navCollapsed })),
    toggleBuoyInfoCollapsed: () => setState(prev => ({ ...prev, buoyInfoCollapsed: !prev.buoyInfoCollapsed }))
  };

  return [state, actions];
};
