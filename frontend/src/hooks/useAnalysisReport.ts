import { useState, useCallback } from 'react';
import { postAnalysisReport, AnalysisRequestBody } from '../api/analysisService';

interface AnalysisReport {
  analysis_text: string;
}

interface UseAnalysisReportResult {
  loading: boolean;
  error: string | null;
  data: AnalysisReport | null;
  fetchReport: (body: AnalysisRequestBody) => Promise<void>;
}

export function useAnalysisReport(): UseAnalysisReportResult {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<AnalysisReport | null>(null);

  const fetchReport = useCallback(async (body: AnalysisRequestBody) => {
    setLoading(true);
    setError(null);
    setData(null);
    try {
      console.log('发送分析请求:', body);
      const res = await postAnalysisReport(body);
      console.log('收到分析响应:', res);
      if (res.analysis_text) {
        console.log('设置分析数据:', res);
        setData(res);
      } else {
        console.warn('分析响应异常: 缺少 analysis_text 字段');
        setError('分析报告格式不正确');
      }
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : '分析报告获取异常';
      console.error('分析请求异常:', e);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  return { loading, error, data, fetchReport };
}