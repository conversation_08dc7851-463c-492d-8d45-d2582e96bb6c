/**
 * 浮标WebSocket控制服务
 * 提供通过WebSocket发送浮标控制命令的功能
 */

import { ControlCommand, ControlResponse } from '../types';
import webSocketClient from '../utils/websocket';

/**
 * 通过WebSocket发送浮标控制命令
 * @param buoyId 浮标ID
 * @param command 控制命令
 * @returns Promise，成功时解析为true，失败时拒绝
 */
export const sendControlCommand = async (buoyId: string, command: ControlCommand): Promise<boolean> => {
  try {
    // 验证WebSocket连接状态
    if (!webSocketClient.isConnected()) {
      console.error('WebSocket未连接，无法发送控制命令');
      throw new Error('WebSocket未连接，请先连接WebSocket');
    }

    // 验证命令格式
    if (!command.command) {
      throw new Error('控制命令缺少command字段');
    }

    // 验证亮度范围
    if (command.brightness !== undefined && (command.brightness < 0 || command.brightness > 100)) {
      throw new Error('亮度必须在0-100范围内');
    }

    // 验证颜色格式
    if (command.color !== undefined && (!command.color.startsWith('#') || command.color.length !== 7)) {
      throw new Error('颜色必须是#开头的7位十六进制格式');
    }

    // 构造控制主题
    const topic = `/topic/buoys/${buoyId}/control`;

    // 发送控制命令
    webSocketClient.send(topic, command);
    // console.log(`已通过WebSocket发送控制命令: ${topic}`, command);

    return true;
  } catch (error) {
    console.error('发送WebSocket控制命令失败:', error);
    throw error;
  }
};

/**
 * 订阅浮标控制响应
 * @param buoyId 浮标ID
 * @param callback 响应回调函数
 * @returns 订阅对象，用于取消订阅
 */
export const subscribeControlResponse = (buoyId: string, callback: (response: ControlResponse) => void) => {
  try {
    // 构造响应主题
    const topic = `/topic/buoys/${buoyId}/control_response`;

    // 订阅响应主题
    const subscription = webSocketClient.subscribe(topic, (message) => {
      try {
        // 解析响应消息
        let response: ControlResponse;
        if (typeof message === 'string') {
          response = JSON.parse(message);
        } else {
          response = message;
        }

        // 调用回调函数
        callback(response);
      } catch (error) {
        console.error('处理控制响应消息失败:', error);
      }
    });

    console.log(`已订阅浮标控制响应: ${topic}`);
    return subscription;
  } catch (error) {
    console.error('订阅浮标控制响应失败:', error);
    throw error;
  }
};

export default {
  sendControlCommand,
  subscribeControlResponse
};
