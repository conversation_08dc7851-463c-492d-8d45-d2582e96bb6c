import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Login, Register, Layout } from '../components';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
/* import { getThemeClasses } from '../utils/themeUtils'; */

const AuthPage = () => {
  const [isLoginView, setIsLoginView] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { theme } = useTheme();

  // 获取重定向来源路径
  const from = location.state?.from || '/home';

  useEffect(() => {
    if (isAuthenticated) {
      // 如果已经认证，重定向到之前的页面或首页
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  const handleLoginSuccess = () => {
    // 登录成功后，重定向到之前的页面或首页
    navigate(from, { replace: true });
  };

  const handleRegisterSuccess = () => {
    setIsLoginView(true);
  };

  // 根据主题获取适当的样式
  const leftBgClass = theme === 'light' ? 'bg-main' : 'bg-main';
  const textPrimaryClass = 'text-primary';
  const textSecondaryClass = 'text-secondary';
  const accentBgClass = theme === 'light' ? 'bg-primary' : 'bg-primary';
  const accentTextClass = theme === 'light' ? 'text-primary' : 'text-primary';
  const accentHoverClass = theme === 'light' ? 'hover:text-blue-500' : 'hover:text-blue-400';

  // 右侧背景区域的样式
  const rightBgClass = theme === 'light'
    ? 'bg-gradient-to-br from-blue-400 via-blue-300 to-pink-200'
    : 'bg-gradient-to-br from-blue-800 via-blue-700 to-indigo-900';

  return (
    <Layout hideHeader={false} hideFooter>
      <div className="min-h-screen flex">
        {/* 左侧内容区 */}
        <div className={`w-1/2 ${leftBgClass} flex flex-col justify-center px-12`}>
          <div className="mb-12">
            <div className="flex items-center mb-8">
              <div className={`w-10 h-10 ${accentBgClass} rounded mr-3 flex items-center justify-center`}>
                <div className="w-5 h-5 bg-white transform rotate-45"></div>
              </div>
              <h1 className={`text-xl font-bold ${textPrimaryClass}`}>HANJIE</h1>
            </div>

            <div className="mb-8">
              <p className={`${textSecondaryClass} text-sm mb-2`}>开启探索之旅</p>
              <h2 className={`text-3xl font-bold ${textPrimaryClass}`}>{isLoginView ? '登录' : '注册'}</h2>
            </div>

            {isLoginView ? (
              <Login onLoginSuccess={handleLoginSuccess} />
            ) : (
              <Register
                onRegisterSuccess={handleRegisterSuccess}
                onGoToLogin={() => setIsLoginView(true)}
              />
            )}

            <div className="mt-8 text-center">
              {isLoginView ? (
                <p className={textSecondaryClass}>
                  没有账号？
                  <button
                    onClick={() => setIsLoginView(false)}
                    className={`ml-1 font-medium ${accentTextClass} ${accentHoverClass} focus:outline-none`}
                  >
                    注册
                  </button>
                </p>
              ) : null}
            </div>
          </div>
        </div>

        {/* 右侧背景区 */}
        <div className={`w-1/2 ${rightBgClass}`}>
          <div className="h-full w-full" style={{
            backgroundImage: "url('data:image/svg+xml;charset=utf8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 1440 320%22%3E%3Cpath fill=%22%23ffffff18%22 fill-opacity=%220.1%22 d=%22M0,192L48,165.3C96,139,192,85,288,74.7C384,64,480,96,576,144C672,192,768,256,864,277.3C960,299,1056,277,1152,229.3C1248,181,1344,107,1392,69.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z%22%3E%3C/path%3E%3C/svg%3E')",
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}></div>
        </div>
      </div>
    </Layout>
  );
};

export default AuthPage;