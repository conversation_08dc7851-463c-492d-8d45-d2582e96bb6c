import React from 'react';
import QuizComponent from '../components/quiz/QuizComponent';
import { useTheme } from '../contexts/ThemeContext';

const QuizPage: React.FC = () => {
  const { theme } = useTheme();

  return (
    <div className={`flex flex-col h-screen ${theme}`}>
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="max-w-2xl w-full">
          <h1 className="text-2xl font-bold mb-6 text-center">互动体验 - 知识问答</h1>
          <QuizComponent />
        </div>
      </div>
    </div>
  );
};

export default QuizPage;