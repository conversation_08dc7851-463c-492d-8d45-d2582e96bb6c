import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { UserProfile, Layout } from '../components';
import { MyFeedbackList } from '../components/feedback';
import { logout as apiLogout } from '../api';

const UserAccountPage = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'feedback'>('profile');

  const handleLogout = async () => {
    try {
      // 先调用后端API登出
      await apiLogout();
    } catch (error) {
      console.error('登出API调用失败', error);
    } finally {
      // 无论API是否成功，都进行前端登出
      logout();
      window.location.href = '/auth';
    }
  };

  // 用户信息和退出按钮
  const userActions = (
    <>
      <span className="hidden sm:inline-block mr-4 text-secondary">
        {user?.username} ({user?.role === 'admin' ? '管理员' : '用户'})
      </span>
      <button
        onClick={handleLogout}
        className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium"
      >
        退出登录
      </button>
    </>
  );

  return (
    <Layout
      withContainer
      containerClassName="max-w-5xl"
      title="我的账户"
      actions={userActions}
    >
      {/* 标签页导航 */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <nav className="flex -mb-px">
          <button
            onClick={() => setActiveTab('profile')}
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              activeTab === 'profile'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            个人资料
          </button>
          <button
            onClick={() => setActiveTab('feedback')}
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              activeTab === 'feedback'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            我的反馈
          </button>
        </nav>
      </div>

      {/* 内容区域 */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        {activeTab === 'profile' && <UserProfile />}
        {activeTab === 'feedback' && <MyFeedbackList />}
      </div>
    </Layout>
  );
};

export default UserAccountPage;