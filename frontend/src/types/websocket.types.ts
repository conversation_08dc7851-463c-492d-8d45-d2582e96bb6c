/**
 * WebSocket相关类型定义
 */

// WebSocket连接状态枚举
export enum WebSocketConnectionState {
  CONNECTING = 'CONNECTING',
  OPEN = 'OPEN',
  CLOSING = 'CLOSING',
  CLOSED = 'CLOSED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

// STOMP命令枚举
export enum StompCommand {
  CONNECT = 'CONNECT',
  CONNECTED = 'CONNECTED',
  SEND = 'SEND',
  SUBSCRIBE = 'SUBSCRIBE',
  UNSUBSCRIBE = 'UNSUBSCRIBE',
  MESSAGE = 'MESSAGE',
  ERROR = 'ERROR',
  DISCONNECT = 'DISCONNECT'
}

// STOMP帧接口
export interface StompFrame {
  command: StompCommand;
  headers: Record<string, string>;
  body?: string;
}

// 消息处理回调函数类型
export type MessageCallback = (message: any) => void;

// 连接状态变化回调函数类型
export type ConnectionStateCallback = (state: WebSocketConnectionState) => void;

// 错误处理回调函数类型
export type ErrorCallback = (error: Error) => void;

// 订阅选项接口
export interface SubscriptionOptions {
  // 订阅ID，如果不提供则自动生成
  id?: string;
  // 订阅头部信息
  headers?: Record<string, string>;
}

// 订阅对象接口
export interface Subscription {
  // 订阅ID
  id: string;
  // 订阅的主题
  topic: string;
  // 消息处理回调
  callback: MessageCallback;
  // 取消订阅方法
  unsubscribe: () => void;
}

// WebSocket客户端配置接口
export interface WebSocketClientConfig {
  // WebSocket URL，如果不提供则使用相对路径
  url?: string;
  // WebSocket路径，默认为/ws
  path?: string;
  // 重连配置
  reconnect?: {
    // 是否启用自动重连，默认为true
    enabled?: boolean;
    // 最大重连尝试次数，默认为10
    maxAttempts?: number;
    // 重连延迟（毫秒），默认为5000
    delay?: number;
    // 重连延迟增长因子，默认为1.5
    backoffFactor?: number;
  };
  // 调试模式，默认为false
  debug?: boolean;
  // STOMP协议版本，默认为1.2
  stompVersion?: string;
  // 心跳间隔（毫秒），默认为0（禁用）
  heartbeat?: number;
  // 连接超时（毫秒），默认为10000
  connectTimeout?: number;
}

// WebSocket客户端接口
export interface WebSocketClient {
  // 连接WebSocket
  connect(): Promise<void>;
  // 断开WebSocket连接
  disconnect(): Promise<void>;
  // 订阅主题
  subscribe(topic: string, callback: MessageCallback, options?: SubscriptionOptions): Subscription;
  // 取消订阅主题
  unsubscribe(subscription: Subscription | string): void;
  // 发送消息到指定主题
  send(topic: string, message: any, headers?: Record<string, string>): void;
  // 获取当前连接状态
  getConnectionState(): WebSocketConnectionState;
  // 检查是否已连接
  isConnected(): boolean;
  // 添加连接状态变化监听器
  onConnectionStateChange(callback: ConnectionStateCallback): () => void;
  // 添加错误监听器
  onError(callback: ErrorCallback): () => void;
  // 设置认证令牌
  setAuthToken(token: string | null): void;
  // 销毁WebSocket客户端实例，清理所有资源
  destroy(): void;
}
