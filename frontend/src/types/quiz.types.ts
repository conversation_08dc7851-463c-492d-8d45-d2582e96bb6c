// 知识问答相关类型定义

// 知识问答题目接口
export interface QuizQuestion {
  id: number;
  content: string;
  options: Array<{ id: number; text: string }>;
  correct_answer_id: number | null;
  category: string;
  difficulty: string;
  explanation?: string;
}
// 知识问答问题列表响应接口
export interface QuizQuestionsResponse {
  questions: QuizQuestion[];
  total: number;
  limit: number;
  offset: number;
  random: boolean;
}

// 答题记录接口
export interface QuizAttempt {
  id: string;
  userId: string;
  questionId: string;
  selectedOption: 'A' | 'B' | 'C' | 'D';
  isCorrect: boolean;
  timestamp: string;
}
