// 用户相关类型定义

// 用户信息接口
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  bio?: string;
}

// 用户资料更新接口
export interface UserProfileUpdate {
  username?: string;
  email?: string;
  avatar?: string;
  bio?: string;
}

// 通知接口
export interface Notification {
  id: string;
  userId?: string;
  buoyId?: string;
  type: string;
  message: string;
  readStatus: boolean;
  timestamp: string;
}
