/**
 * 测试工具函数
 */

import { controlBuoy } from '../api/buoyService';
import { ControlCommand } from '../types';

/**
 * 测试浮标控制API
 * 
 * 这个函数用于测试浮标控制API是否正常工作
 * 
 * @param buoyId 浮标ID
 * @returns 测试结果
 */
export async function testBuoyControl(buoyId: string): Promise<{success: boolean; message: string}> {
  try {
    // 构造测试控制指令
    const testCommand: ControlCommand = {
      command: 'set_light',
      brightness: 50,
      color: '#00FF00'
    };
    
    // 调用控制API
    const response = await controlBuoy(buoyId, testCommand);
    
    return {
      success: true,
      message: `测试成功: ${response.message}`
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : '测试失败，未知错误'
    };
  }
}

/**
 * 验证控制指令参数
 * 
 * @param brightness 亮度值
 * @param color 颜色值
 * @returns 验证结果
 */
export function validateControlParams(brightness?: number, color?: string): {valid: boolean; message?: string} {
  // 验证亮度
  if (brightness !== undefined) {
    if (isNaN(brightness)) {
      return { valid: false, message: '亮度必须是数字' };
    }
    
    if (brightness < 0 || brightness > 100) {
      return { valid: false, message: '亮度必须在0-100范围内' };
    }
  }
  
  // 验证颜色
  if (color !== undefined) {
    // 检查是否是有效的十六进制颜色格式
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (!hexColorRegex.test(color)) {
      return { valid: false, message: '颜色必须是有效的十六进制格式（如 #FF8C00）' };
    }
  }
  
  return { valid: true };
}
