import { ThemeType } from '../contexts/ThemeContext';

// 根据当前主题返回适当的样式类名
export const getThemeClasses = (theme: ThemeType, options: {
  light: string; 
  dark: string;
}): string => {
  return theme === 'light' ? options.light : options.dark;
};

// 根据主题获取按钮样式
export const getButtonClasses = (theme: ThemeType, variant: 'primary' | 'secondary' | 'outline' = 'primary'): string => {
  const baseClasses = 'px-4 py-2 rounded-md transition-colors focus:outline-none';
  
  switch (variant) {
    case 'primary':
      return `${baseClasses} ${theme === 'light' 
        ? 'bg-primary text-white hover:bg-blue-600' 
        : 'bg-primary text-white hover:bg-blue-400'}`;
    case 'secondary':
      return `${baseClasses} ${theme === 'light' 
        ? 'bg-secondary text-white hover:bg-green-600' 
        : 'bg-secondary text-white hover:bg-green-400'}`;
    case 'outline':
      return `${baseClasses} border ${theme === 'light' 
        ? 'border-primary text-primary hover:bg-blue-50' 
        : 'border-primary text-primary hover:bg-gray-700'}`;
    default:
      return baseClasses;
  }
};

// 根据主题获取卡片/容器样式
export const getCardClasses = (theme: ThemeType): string => {
  return `bg-${theme === 'light' ? 'white' : 'gray-800'} 
    shadow-md rounded-lg p-4 
    border ${theme === 'light' ? 'border-gray-100' : 'border-gray-700'}`;
};

// 根据主题获取输入框样式
export const getInputClasses = (theme: ThemeType): string => {
  return `block w-full rounded-md border 
    ${theme === 'light' ? 'border-gray-300 bg-white' : 'border-gray-600 bg-gray-700'} 
    px-3 py-2 focus:outline-none 
    ${theme === 'light' ? 'focus:border-blue-500 focus:ring-blue-500' : 'focus:border-blue-400 focus:ring-blue-400'} 
    focus:ring-1 transition-colors`;
};

// 获取浮标状态对应的颜色类
export const getBuoyStatusClasses = (status: string, theme: ThemeType): string => {
  switch (status) {
    case 'active':
      return theme === 'light' ? 'text-green-600' : 'text-green-400';
    case 'inactive':
      return theme === 'light' ? 'text-gray-500' : 'text-gray-400';
    case 'error':
      return theme === 'light' ? 'text-red-600' : 'text-red-400';
    default:
      return theme === 'light' ? 'text-blue-600' : 'text-blue-400';
  }
}; 