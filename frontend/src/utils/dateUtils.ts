/**
 * 日期时间处理工具函数
 */

/**
 * 将UTC时间戳转换为本地时间并格式化显示
 * @param dateString UTC时间戳字符串
 * @param options 格式化选项
 * @returns 格式化后的本地时间字符串
 */
export const formatUTCToLocal = (
  dateString: string | undefined | null,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }
): string => {
  if (!dateString) return '未知';
  
  try {
    // 创建Date对象 - JavaScript的Date会自动将UTC时间转换为本地时间
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的日期格式:', dateString);
      return '未知';
    }
    
    // 使用toLocaleString将日期格式化为本地时间字符串
    return date.toLocaleString('zh-CN', options);
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString || '未知';
  }
};

/**
 * 获取日期的时间部分
 * @param dateString 日期字符串
 * @returns 格式化的时间字符串
 */
export const formatTimeOnly = (dateString: string | undefined | null): string => {
  if (!dateString) return '未知';
  
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      return '未知';
    }
    
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (error) {
    console.error('时间格式化错误:', error);
    return '未知';
  }
};

/**
 * 格式化相对时间（如：5分钟前，1小时前等）
 * @param dateString 日期字符串
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (dateString: string | undefined | null): string => {
  if (!dateString) return '未知';
  
  try {
    const date = new Date(dateString);
    const now = new Date();
    
    if (isNaN(date.getTime())) {
      return '未知';
    }
    
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffSec < 60) {
      return '刚刚';
    } else if (diffMin < 60) {
      return `${diffMin}分钟前`;
    } else if (diffHour < 24) {
      return `${diffHour}小时前`;
    } else if (diffDay < 30) {
      return `${diffDay}天前`;
    } else {
      // 超过30天则显示完整日期
      return formatUTCToLocal(dateString, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
  } catch (error) {
    console.error('相对时间格式化错误:', error);
    return '未知';
  }
};

/**
 * 检查日期是否为今天
 * @param dateString 日期字符串
 * @returns 是否为今天
 */
export const isToday = (dateString: string | undefined | null): boolean => {
  if (!dateString) return false;
  
  try {
    const date = new Date(dateString);
    const today = new Date();
    
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  } catch (error) {
    console.error('日期检查错误:', error);
    return false;
  }
};

/**
 * 将本地时间转换为UTC时间字符串
 * @param date 本地日期对象或日期字符串
 * @returns UTC格式的时间字符串
 */
export const formatLocalToUTC = (date: Date | string): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      throw new Error('无效的日期');
    }
    
    return dateObj.toISOString();
  } catch (error) {
    console.error('UTC时间格式化错误:', error);
    return '';
  }
};
