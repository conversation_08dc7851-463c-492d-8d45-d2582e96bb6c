/**
 * STOMP协议实现
 * 用于WebSocket通信中的STOMP协议处理
 */

import { StompCommand, StompFrame } from '../types';

/**
 * 解析STOMP帧
 * @param data STOMP帧字符串
 * @returns 解析后的STOMP帧对象
 */
export function parseStompFrame(data: string): StompFrame | null {
  try {
    // 移除末尾的NULL字符
    if (data.endsWith('\0')) {
      data = data.slice(0, -1);
    }

    const lines = data.split('\n');
    if (!lines.length) {
      return null;
    }

    // 获取命令
    const command = lines[0] as StompCommand;
    if (!command) {
      return null;
    }

    // 解析头部和正文
    const headers: Record<string, string> = {};
    let bodyStartIndex = -1;

    // 查找空行，分割headers和body
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) {
        bodyStartIndex = i;
        break;
      }

      // 解析头部
      const colonIndex = lines[i].indexOf(':');
      if (colonIndex > 0) {
        const key = lines[i].substring(0, colonIndex);
        const value = lines[i].substring(colonIndex + 1);
        headers[key] = value;
      }
    }

    // 解析正文
    let body: string | undefined;
    if (bodyStartIndex > 0 && bodyStartIndex < lines.length - 1) {
      body = lines.slice(bodyStartIndex + 1).join('\n');
    }

    return {
      command,
      headers,
      body
    };
  } catch (error) {
    console.error('解析STOMP帧出错:', error);
    return null;
  }
}

/**
 * 构建STOMP帧
 * @param command STOMP命令
 * @param headers 头部信息
 * @param body 消息正文
 * @returns STOMP帧字符串
 */
export function buildStompFrame(
  command: StompCommand,
  headers: Record<string, string> = {},
  body?: string
): string {
  try {
    // 构建命令行
    let frame = `${command}\n`;

    // 添加头部
    for (const [key, value] of Object.entries(headers)) {
      frame += `${key}:${value}\n`;
    }

    // 如果有正文，添加内容长度头部
    if (body) {
      frame += `content-length:${body.length}\n`;
    }

    // 添加空行分隔头部和正文
    frame += '\n';

    // 添加正文
    if (body) {
      frame += body;
    }

    // 添加NULL字符结束帧
    frame += '\0';

    return frame;
  } catch (error) {
    console.error('构建STOMP帧出错:', error);
    throw error;
  }
}

/**
 * 构建CONNECT帧
 * @param headers 连接头部信息
 * @returns STOMP CONNECT帧字符串
 */
export function buildConnectFrame(headers: Record<string, string> = {}): string {
  const connectHeaders = {
    'accept-version': '1.2',
    'heart-beat': '0,0',
    ...headers
  };

  return buildStompFrame(StompCommand.CONNECT, connectHeaders);
}

/**
 * 构建SUBSCRIBE帧
 * @param destination 订阅目标主题
 * @param id 订阅ID
 * @param headers 额外的头部信息
 * @returns STOMP SUBSCRIBE帧字符串
 */
export function buildSubscribeFrame(
  destination: string,
  id: string,
  headers: Record<string, string> = {}
): string {
  const subscribeHeaders = {
    destination,
    id,
    ...headers
  };

  return buildStompFrame(StompCommand.SUBSCRIBE, subscribeHeaders);
}

/**
 * 构建UNSUBSCRIBE帧
 * @param id 订阅ID
 * @param destination 订阅目标主题（可选，但推荐提供）
 * @param headers 额外的头部信息
 * @returns STOMP UNSUBSCRIBE帧字符串
 */
export function buildUnsubscribeFrame(
  id: string,
  destination?: string,
  headers: Record<string, string> = {}
): string {
  const unsubscribeHeaders: Record<string, string> = {
    id,
    ...headers
  };

  // 如果提供了destination，添加到头部
  if (destination) {
    unsubscribeHeaders.destination = destination;
  }

  return buildStompFrame(StompCommand.UNSUBSCRIBE, unsubscribeHeaders);
}

/**
 * 构建SEND帧
 * @param destination 发送目标主题
 * @param body 消息正文
 * @param headers 额外的头部信息
 * @returns STOMP SEND帧字符串
 */
export function buildSendFrame(
  destination: string,
  body: string,
  headers: Record<string, string> = {}
): string {
  const sendHeaders = {
    destination,
    'content-type': 'application/json',
    ...headers
  };

  return buildStompFrame(StompCommand.SEND, sendHeaders, body);
}

/**
 * 构建DISCONNECT帧
 * @param receipt 回执ID
 * @returns STOMP DISCONNECT帧字符串
 */
export function buildDisconnectFrame(receipt?: string): string {
  const headers: Record<string, string> = {};
  if (receipt) {
    headers['receipt'] = receipt;
  }

  return buildStompFrame(StompCommand.DISCONNECT, headers);
}

/**
 * 解析STOMP消息正文
 * @param frame STOMP帧对象
 * @returns 解析后的消息对象
 */
export function parseMessageBody(frame: StompFrame): any {
  if (!frame.body) {
    return null;
  }

  const contentType = frame.headers['content-type'];

  try {
    if (contentType && contentType.includes('application/json')) {
      return JSON.parse(frame.body);
    }
    return frame.body;
  } catch (error) {
    console.error('解析消息正文出错:', error);
    return frame.body;
  }
}
