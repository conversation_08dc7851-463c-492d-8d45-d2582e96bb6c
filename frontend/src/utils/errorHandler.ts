import axios, { AxiosError } from 'axios';

// 错误类型定义
export interface ApiError {
  status: number;
  message: string;
  details?: string;
}

// API响应数据类型
export interface ApiErrorResponse {
  detail?: string;
  message?: string;
  error?: string;
  [key: string]: unknown;
}

/**
 * 解析API错误响应
 */
export function parseApiError(error: unknown): ApiError {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;
    
    if (axiosError.response) {
      const status = axiosError.response.status;
      
      // 尝试从响应中获取详细错误信息
      const responseData = axiosError.response.data as ApiErrorResponse;
      
      // 检查常见的错误消息格式
      const errorMessage = 
        responseData?.detail || 
        responseData?.message || 
        responseData?.error || 
        axiosError.message;
      
      // 检查错误详情
      const errorDetails = 
        typeof responseData === 'object' && responseData !== null 
          ? JSON.stringify(responseData) 
          : undefined;
      
      return {
        status,
        message: getErrorMessageByStatus(status, errorMessage),
        details: errorDetails
      };
    }
    
    // 网络错误
    if (axiosError.code === 'ECONNABORTED') {
      return {
        status: 0,
        message: '请求超时，请检查您的网络连接'
      };
    }
    
    if (axiosError.code === 'ERR_NETWORK') {
      return {
        status: 0,
        message: '网络错误，无法连接到服务器'
      };
    }
  }
  
  // 非Axios错误
  return {
    status: 500,
    message: error instanceof Error ? error.message : '发生未知错误'
  };
}

/**
 * 根据HTTP状态码获取友好的错误消息
 */
export function getErrorMessageByStatus(status: number, defaultMessage?: string): string {
  switch (status) {
    case 400:
      return defaultMessage || '请求参数有误，请检查您的输入';
    case 401:
      return '未授权，请登录后重试';
    case 403:
      return '您没有权限执行此操作';
    case 404:
      return '请求的资源不存在';
    case 409:
      return '请求冲突，可能存在重复数据';
    case 422:
      return defaultMessage || '提交的数据无效';
    case 429:
      return '请求过于频繁，请稍后重试';
    case 500:
      return '服务器内部错误，请稍后重试';
    case 502:
    case 503:
    case 504:
      return '服务暂时不可用，请稍后重试';
    default:
      return defaultMessage || '发生错误，请稍后重试';
  }
}

/**
 * 映射WebSocket错误
 */
export function getWebSocketErrorMessage(error: Error | string | unknown): string {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error instanceof Error) {
    // 常见的WebSocket错误
    if (error.message.includes('Connection refused')) {
      return 'WebSocket连接被拒绝，服务器可能未运行';
    }
    
    if (error.message.includes('timeout')) {
      return 'WebSocket连接超时，请检查网络状态';
    }
    
    return error.message;
  }
  
  return '发生WebSocket错误';
}

/**
 * 根据错误类型分发不同的处理逻辑
 */
export function handleApplicationError(error: unknown): ApiError {
  return parseApiError(error);
} 