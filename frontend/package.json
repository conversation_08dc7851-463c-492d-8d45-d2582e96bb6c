{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:unit": "vitest run --dir tests/unit", "test:components": "vitest run --dir tests/components", "test:integration": "vitest run --dir tests/integration", "test:coverage": "vitest run --coverage", "test:websocket": "vite --open /websocket-test"}, "dependencies": {"@stomp/stompjs": "^7.1.0", "@tailwindcss/postcss": "^4.0.17", "@tailwindcss/vite": "^4.1.0", "@types/cesium": "^1.67.14", "axios": "^1.8.4", "cesium": "^1.127.0", "echarts": "^5.6.0", "html2canvas": "^1.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-rnd": "^10.5.2", "react-router-dom": "^7.4.1", "resium": "^1.19.0-beta.1", "vite-plugin-cesium": "^1.2.23"}, "devDependencies": {"@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vitest": "^1.4.0"}}