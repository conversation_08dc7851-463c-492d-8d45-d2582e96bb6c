# 智能浮标管理平台 - 生产环境部署指南

## 概述

本项目使用 Docker Compose 实现一键部署，包含以下服务：
- **Caddy**: 反向代理、静态文件托管、自动 HTTPS 证书管理
- **Frontend**: React/Vite 前端应用（静态文件）
- **Backend**: FastAPI 后端服务
- **Database**: PostGIS/PostgreSQL 数据库
- **MQTT**: Eclipse Mosquitto 消息代理

## 部署前准备

### 1. 服务器要求
- Linux 服务器（推荐 Ubuntu 20.04+）
- Docker 和 Docker Compose 已安装
- 至少 2GB RAM，10GB 存储空间
- 开放端口：80 (HTTP)、443 (HTTPS)、8883 (MQTTS)

### 2. 域名配置
- 确保域名已解析到服务器 IP
- 修改 `caddy/Caddyfile` 中的域名设置

### 3. 环境变量配置
复制并编辑环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键变量：
```bash
# 数据库配置
POSTGRES_SERVER=db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=buoydb
POSTGRES_PORT=5432

# MQTT 配置
MQTT_BROKER_HOST=mqtt
MQTT_BROKER_PORT=8883
MQTT_USERNAME=your_mqtt_username
MQTT_PASSWORD=your_mqtt_password
MQTT_USE_TLS=true

# 安全配置
SECRET_KEY=your_secret_key_here_at_least_32_characters

# 环境标识
ENV=production
LOG_LEVEL=INFO
TZ=Asia/Shanghai
```

**重要**: 请务必修改默认密码和密钥！

## 部署步骤

### 快速部署（推荐）

使用提供的部署脚本：

```bash
# 1. 克隆项目
git clone <repository-url>
cd 浮标网站

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，修改密码和密钥

# 3. 配置域名（生产环境）
# 编辑 caddy/Caddyfile，替换 your-domain.com

# 4. 一键部署
./deploy.sh prod  # 生产环境
# 或
./deploy.sh dev   # 开发环境
```

### 手动部署步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd 浮标网站
```

#### 2. 配置域名
编辑 `caddy/Caddyfile`，将 `your-domain.com` 替换为实际域名：
```
your-domain.com {
    # ... 其他配置保持不变
}
```

#### 3. 构建并启动服务
```bash
# 构建前端静态文件
cd frontend
npm install
npm run build
cd ..

# 启动所有服务
docker-compose up -d
```

#### 4. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f caddy
docker-compose logs -f backend
```

## 服务架构

### 网络架构
```
Internet → Caddy (80/443) → Frontend (静态文件)
                         → Backend (8000) → Database (5432)
                                         → MQTT (8883)
```

### 数据持久化
- `caddy_data`: Caddy 配置和证书
- `postgres_data`: 数据库数据
- `backend_logs`: 后端日志
- `./mosquitto/data`: MQTT 数据

## 维护操作

### 更新应用
```bash
# 拉取最新代码
git pull

# 重新构建前端
cd frontend && npm run build && cd ..

# 重启服务
docker-compose restart frontend backend
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f caddy
docker-compose logs -f backend
```

### 备份数据
```bash
# 备份数据库
docker-compose exec db pg_dump -U $POSTGRES_USER $POSTGRES_DB > backup.sql

# 备份 MQTT 数据
tar -czf mosquitto_backup.tar.gz mosquitto/data/
```

### 恢复数据
```bash
# 恢复数据库
docker-compose exec -T db psql -U $POSTGRES_USER $POSTGRES_DB < backup.sql

# 恢复 MQTT 数据
tar -xzf mosquitto_backup.tar.gz
```

## 安全建议

### 1. 防火墙配置
```bash
# 只开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8883/tcp
ufw enable
```

### 2. 定期更新
- 定期更新 Docker 镜像
- 监控安全漏洞
- 定期备份数据

### 3. 监控
- 使用 `docker-compose logs` 监控应用日志
- 监控服务器资源使用情况
- 设置告警机制

## 故障排除

### 常见问题

1. **Caddy 无法获取证书**
   - 检查域名解析是否正确
   - 确保端口 80/443 未被占用
   - 查看 Caddy 日志：`docker-compose logs caddy`

2. **前端页面无法访问**
   - 确认前端构建成功：检查 `frontend/dist` 目录
   - 检查 Caddy 配置和日志

3. **后端 API 无法访问**
   - 检查后端服务状态：`docker-compose ps backend`
   - 查看后端日志：`docker-compose logs backend`

4. **数据库连接失败**
   - 检查环境变量配置
   - 确认数据库服务运行正常

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart caddy
docker-compose restart backend
```

## 性能优化

1. **启用 Gzip 压缩**（已在 Caddyfile 中配置）
2. **设置静态资源缓存**（已在 Caddyfile 中配置）
3. **数据库优化**：根据实际使用情况调整 PostgreSQL 配置
4. **监控资源使用**：使用 `docker stats` 监控容器资源使用

## 联系支持

如遇到部署问题，请提供以下信息：
- 服务器环境信息
- 错误日志
- 配置文件内容（敏感信息请脱敏）
