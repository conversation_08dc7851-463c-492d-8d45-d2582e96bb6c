# 智能浮标功能需求

**版本:** 1.0
**日期:** 2025-3-27

## 1. 功能概述

智能浮标需要具备以下核心功能，以支持智能浮标管理与互动平台：

* **环境数据采集:**  采集多种环境参数，例如水质（pH值、溶解氧等）、水位、流速、温度等。
* **图像数据采集:**  拍摄水面或特定方向的图像。
* **数据传输:**  将采集的环境数据和图像数据可靠地传输到云平台。
* **指令接收与执行:**  接收并执行来自云平台的控制指令，例如调节LED灯的亮度、颜色等。
* **状态反馈:**  可选地，向云平台报告自身状态，例如指令执行结果、设备运行状态等。
* **安全通信:**  支持安全的通信协议，保障数据传输的机密性和完整性。

## 2. 详细功能需求

### 2.1. 数据采集与传输

* **2.1.1. 环境数据采集**
    * 浮标应配备多种传感器，用于采集以下环境数据（具体传感器类型和精度可根据实际应用场景调整）：
        * 水质参数：例如 pH 值、溶解氧、电导率、浊度、叶绿素浓度等。
        * 气象参数：例如气温、气压、湿度、风速、风向等（如果需要）。
        * 水文参数：例如水位、流速、水温等。
    * 数据采集频率可配置，默认建议为 1 分钟/次，可根据指令动态调整。
    * 采集的数据需进行初步处理，例如单位转换、数据校验等。
    * 数据格式化为 JSON 格式，例如：
    ```json
    {
      "buoy_id": "浮标ID",
      "timestamp": "时间戳 (ISO 8601 格式)",
      "data_type": "sensor_data",
      "sensors": [
        {"type": "ph", "value": 7.2, "unit": "pH"},
        {"type": "dissolved_oxygen", "value": 8.5, "unit": "mg/L"},
        {"type": "water_temperature", "value": 25.3, "unit": "°C"}
        // ... 其他传感器数据
      ]
    }
    ```

* **2.1.2. 图像数据采集**
    * 浮标应配备摄像头，用于拍摄图像。
    * 图像分辨率建议为 1920x1080 或更高。
    * 图像采集频率可配置，默认建议为 5 分钟/次，可根据指令动态调整或触发。
    * 图像格式为 JPEG 或 PNG。

* **2.1.3. 数据传输协议**
    * **主要协议:** MQTT (MQTTS)
        * 浮标作为 MQTT 客户端，连接到指定的 MQTT Broker。
        * 发布主题：`buoy/{buoy_id}/data`  (用于发布传感器数据和状态信息)
    * **图像上传协议:** HTTP/HTTPS (RESTful API)
        * 浮标作为 HTTP 客户端，通过 POST 请求上传图像到指定的后端 API 接口。

* **2.1.4. 数据传输安全性**
    * **MQTT:**  必须使用 MQTTS (MQTT over TLS) 加密传输，保障数据传输安全。支持用户名/密码认证，建议考虑 TLS 客户端证书认证以增强安全性。
    * **HTTP/HTTPS:** 图像上传必须使用 HTTPS 加密传输。建议使用 API Key 认证，通过 HTTP Header (`X-API-Key`) 传递 API Key。

### 2.2. 指令接收与执行

* **2.2.1. 指令接收协议**
    * **MQTT:** 浮标订阅控制指令主题：`buoy/{buoy_id}/command`

* **2.2.2. 支持的控制指令**
    * **设置 LED 灯:**
        ```json
        {
          "command": "set_light",
          "brightness": 0-100, // 亮度百分比
          "color": "#RRGGBB"   // 十六进制颜色码
        }
        ```
    * **调整数据采集频率:** (可选)
        ```json
        {
          "command": "set_data_frequency",
          "frequency": 60 // 单位：秒
        }
        ```
    * **调整图像采集频率:** (可选)
        ```json
        {
          "command": "set_image_frequency",
          "frequency": 300 // 单位：秒
        }
        ```
    * **立即拍摄图像:** (可选)
        ```json
        {
          "command": "capture_image_now"
        }
        ```
    * **重启设备:** (可选，谨慎使用)
        ```json
        {
          "command": "reboot"
        }
        ```
    * 指令格式为 JSON。
    * 浮标接收到指令后，需解析并执行相应的操作。

* **2.2.3. 指令执行反馈** (可选)
    * 浮标执行指令后，可以发布状态消息到 MQTT 数据主题，告知指令执行结果或当前状态。例如：
    ```json
    {
      "buoy_id": "浮标ID",
      "timestamp": "时间戳 (ISO 8601 格式)",
      "data_type": "status_report",
      "status": "command_executed",
      "command": "set_light",
      "result": "success" // 或 "failure", "error_message": "..."
    }
    ```

### 2.3. 图像上传接口

* **2.3.1. API 接口**
    * **URL:**  `/api/buoys/{buoy_id}/images` (具体 URL 由后端提供)
    * **Method:** POST
    * **Headers:**
        * `Content-Type: multipart/form-data`
        * `X-API-Key: <浮标API Key>` (由平台分配)
    * **Body:**
        * `image_file`:  图像文件
        * 可选的元数据字段，例如 `timestamp`, `description` 等。

* **2.3.2. 响应**
    * **成功 (200 OK):** 返回 JSON 响应，包含图像元数据或成功信息。
    * **失败 (4xx/5xx):** 返回错误信息，例如认证失败、参数错误、服务器错误等。

### 2.4. 安全性要求

* **通信安全:**  如 2.1.4 节所述，MQTT 和 HTTP/HTTPS 通信必须加密。
* **设备认证:**  浮标需要进行身份认证才能接入系统，例如 MQTT 用户名/密码、API Key、TLS 客户端证书等。
* **固件安全:**  考虑固件更新的安全机制，防止恶意固件注入。
* **物理安全:**  浮标设备本身应具备一定的物理安全防护能力，例如防拆卸、防破坏等（根据实际应用场景考虑）。

### 2.5. 开发与部署环境考虑

* **开发环境:**
    * 浮标应易于在开发环境中进行调试和测试。
    * 建议提供串口或网络调试接口，方便查看日志和状态信息。
    * 可以配置连接到本地 MQTT Broker 和开发环境的后端 API。
* **部署环境:**
    * 浮标应能够在实际部署环境中稳定可靠运行。
    * 考虑部署环境的网络条件（例如蜂窝网络、卫星网络等），选择合适的通信方案。
    * 部署环境通常需要连接到生产环境的 MQTT Broker 和后端 API。
    * 考虑功耗管理，优化数据传输策略，以延长电池寿命或降低能源消耗。
    * 考虑环境适应性，例如防水、防腐蚀、耐高温低温等。

### 2.6. 其他要求

* **设备ID:**  每个浮标需要有唯一的设备 ID (`buoy_id`)，用于身份识别和数据关联。
* **时间同步:**  浮标需要具备时间同步机制，保证数据时间戳的准确性（例如 NTP 协议）。
* **低功耗设计:**  在满足功能需求的前提下，尽可能降低功耗，延长设备续航时间。
* **可靠性与稳定性:**  浮标应具备高可靠性和稳定性，能够在恶劣的环境条件下长时间稳定运行。
* **可维护性:**  浮标应易于维护和升级，例如支持远程固件升级、模块化设计方便更换部件等。
* **环境适应性:**  根据实际应用场景，浮标需要具备相应的环境适应性，例如防水等级、工作温度范围、抗风浪能力等。

---

**请供应商注意：**

* 本文档描述的是智能浮标的基本功能需求，具体实现细节和技术方案由供应商根据自身技术能力和经验进行设计。
* 供应商在开发过程中如有任何疑问，请及时与需求方沟通确认。
* 最终交付的浮标产品需要经过充分的测试，确保满足本需求文档中的各项功能和性能指标，并能顺利集成到智能浮标管理与互动平台系统中。 