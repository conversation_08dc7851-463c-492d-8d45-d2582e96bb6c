# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-05-16 10:35:51 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

* 构建一个智能浮标管理与互动平台，用于监控和管理智能浮标设备，支持环境数据采集、图像数据采集、数据传输、指令接收与执行、状态反馈和安全通信。平台旨在提供实时数据可视化、用户管理和浮标控制功能。

## Key Features

* 环境数据采集：采集水质、气象、水文等多种环境参数。
* 图像数据采集：拍摄水面或特定方向的图像。
* 数据传输：通过MQTT和HTTP/HTTPS协议将数据传输到云平台。
* 指令接收与执行：通过MQTT接收并执行控制指令，如调节LED灯亮度和颜色。
* 状态反馈：向云平台报告设备状态和指令执行结果。
* 安全通信：支持MQTTS和HTTPS加密传输，确保数据安全。
* 实时数据流：通过WebSocket实现实时数据推送和图表更新。
* 地图可视化：使用CesiumJS实现浮标位置和历史轨迹的实时显示。
* 用户认证与管理：支持用户注册、登录和信息管理。
* 浮标控制界面：提供亮度调节和颜色选择控件。
* 数据分析预测：集成DeepSeek API进行环境状况评估和趋势预测。
* 互动体验与社交功能：包括知识问答和社交分享功能。
* 通知系统：实现数据异常检测和实时提醒。

## Overall Architecture

* 前端：使用React框架，集成CesiumJS进行地图可视化，通过WebSocket实现实时数据更新。
* 后端：基于FastAPI框架，处理API请求，支持用户认证、浮标管理和数据处理。
* 数据库：使用PostgreSQL和PostGIS扩展，存储用户数据、浮标信息和传感器数据。
* 通信协议：MQTT用于实时数据传输和指令控制，HTTP/HTTPS用于图像上传。
* 浮标模拟器：模拟浮标设备行为和数据生成，用于开发和测试。