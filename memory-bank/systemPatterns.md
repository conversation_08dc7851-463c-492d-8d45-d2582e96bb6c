# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-05-16 10:36:35 - Log of updates made.

*

* [2025-05-16 11:18:27] - 后端使用 FastAPI 框架，采用 async def 处理 I/O 操作，使用 Pydantic V2 模型进行请求/响应验证。
* [2025-05-16 11:18:27] - 后端数据库操作使用 SQLAlchemy 2.0 的异步风格，分离 ORM 模型和 API 模式。
* [2025-05-16 11:18:27] - 后端 ORM 模型中的时间字段使用 UTC 时区处理数据库中的时间信息。
* [2025-05-16 11:18:27] - 前端使用 React 框架，采用函数式组件和 TypeScript 类型定义，使用 Tailwind CSS 4 进行样式设计。
* [2025-05-16 11:18:27] - 前端使用 CesiumJS 进行地图可视化，将 CesiumJS 功能封装到模块或自定义钩子中，优化场景更新性能。
* [2025-05-16 11:18:27] - 项目使用 MQTT 进行实时通信，实现重连逻辑和适当的 QoS 级别。
* [2025-05-16 11:18:27] - 项目使用 Docker Compose 管理服务，通过服务名进行通信，使用命名卷进行数据持久化。

## Architectural Patterns
* [2025-05-16 11:18:27] - 后端 WebSocket 连接实现 token 认证机制以确保安全性。
* [2025-05-16 11:18:27] - 前端 WebSocket 功能集成到 BuoyContext 中，只有登录用户才能订阅浮标信息。
* [2025-05-16 11:18:27] - 系统在 /home 页面自动创建 WebSocket 连接，并根据选择的浮标 ID 订阅相应主题。
* [2025-05-16 11:18:27] - WebSocket 接收到的新数据消息用于更新 BuoyContext 中的历史位置数据和传感器数据。

## Testing Patterns
* [2025-05-16 11:18:27] - 前端项目设计系统性的测试系统，按照合理的文件结构组织测试文件。
## Coding Patterns

*   

## Architectural Patterns

*   

## Testing Patterns

*