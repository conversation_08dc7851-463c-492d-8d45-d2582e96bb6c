import json
import time
import logging
import signal
import sys
import threading
from datetime import datetime, timezone
from typing import Optional
import tkinter as tk
from tkinter import ttk, messagebox
import paho.mqtt.client as mqtt
from config import *

# 配置日志
logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'INFO'),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('buoy_controller_gui')

class BuoyControllerGUI:
    def __init__(self):
        # 浮标ID（可配置）
        self.buoy_id = 1
        self.client_id = f"buoy-controller-gui-{self.buoy_id}"
        self.client = mqtt.Client(client_id=self.client_id, callback_api_version=mqtt.CallbackAPIVersion.VERSION1)
        
        # 设置认证信息
        if MQTT_USERNAME:
            self.client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
        
        self.connected = False
        self.running = False
        self.auto_mode = True  # 自动模式标志
        
        # 设置LWT（遗嘱消息）
        self.status_topic = f"buoy/{self.buoy_id}/status"
        self.client.will_set(self.status_topic, payload="offline", qos=1, retain=False)
        
        # 设置回调
        self.client.on_connect = self.on_connect
        self.client.on_disconnect = self.on_disconnect
        self.client.on_message = self.on_message
        
        # 订阅控制主题（接收来自服务器的控制命令）
        self.control_topic = f"buoy/command/{self.buoy_id}"
        
        # 浮标状态
        self.status = "active"
        self.light_brightness = 0
        self.light_color = "#FFFFFF"
        self.light_enabled = False
        
        # 传感器数据（模拟浮标上的传感器）
        self.sensor_data = {
            "temperature": {"value": 20.0, "unit": "°C"},
            "ph": {"value": 7.0, "unit": "pH"},
            "dissolved_oxygen": {"value": 8.0, "unit": "mg/L"},
            "water_level": {"value": 1.5, "unit": "m"},
            "flow_rate": {"value": 1.5, "unit": "m/s"}
        }
        
        # 位置数据（模拟浮标位置）
        self.location_data = {
            "longitude": BASE_LONGITUDE,
            "latitude": BASE_LATITUDE
        }
        
        # 创建GUI
        self.root = tk.Tk()
        self.setup_gui()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.data_thread = None  # 保存数据线程对象
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root.title("浮标模拟器")
        self.root.geometry("700x900")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 浮标配置
        self.setup_buoy_config(main_frame)
        
        # 连接控制
        self.setup_connection_control(main_frame)
        
        # 连接状态
        self.setup_connection_status(main_frame)
        
        # 数据模式控制
        self.setup_data_mode_control(main_frame)
        
        # 传感器数据显示
        self.setup_sensor_data_display(main_frame)
        
        # 位置数据显示
        self.setup_location_data_display(main_frame)
        
        # 灯光状态显示
        self.setup_light_status_display(main_frame)
        
        # 控制按钮
        self.setup_control_buttons(main_frame)
        
        # 日志显示
        self.setup_log_display(main_frame)
    
    def setup_buoy_config(self, parent):
        """设置浮标配置"""
        config_frame = ttk.LabelFrame(parent, text="浮标配置", padding="5")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # 浮标ID设置
        ttk.Label(config_frame, text="浮标ID:").grid(row=0, column=0, sticky=tk.W)
        self.buoy_id_var = tk.StringVar(value=str(self.buoy_id))
        self.buoy_id_entry = ttk.Entry(config_frame, textvariable=self.buoy_id_var, width=10)
        self.buoy_id_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 配置说明
        ttk.Label(config_frame, text="注意：连接时会自动应用当前浮标ID配置",
                 foreground="gray", font=("Arial", 8)).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
    
    def setup_connection_control(self, parent):
        """设置连接控制"""
        control_frame = ttk.LabelFrame(parent, text="连接控制", padding="5")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(1, weight=1)
        
        # 连接按钮
        self.connect_btn = ttk.Button(control_frame, text="连接MQTT", command=self.connect_mqtt)
        self.connect_btn.grid(row=0, column=0, padx=(0, 5))
        
        # 断开连接按钮
        self.disconnect_btn = ttk.Button(control_frame, text="断开连接", command=self.disconnect_mqtt, state=tk.DISABLED)
        self.disconnect_btn.grid(row=0, column=1, padx=(0, 5))
        
        # 连接状态指示
        self.connection_status_label = ttk.Label(control_frame, text="未连接", foreground="red")
        self.connection_status_label.grid(row=0, column=2, padx=(10, 0))
    
    def setup_connection_status(self, parent):
        """设置连接状态显示"""
        # 连接状态框架
        status_frame = ttk.LabelFrame(parent, text="系统状态", padding="5")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="MQTT状态:").grid(row=0, column=0, sticky=tk.W)
        self.connection_label = ttk.Label(status_frame, text="未连接", foreground="red")
        self.connection_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="浮标状态:").grid(row=1, column=0, sticky=tk.W)
        self.buoy_status_label = ttk.Label(status_frame, text="活跃", foreground="green")
        self.buoy_status_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="数据模式:").grid(row=2, column=0, sticky=tk.W)
        self.data_mode_label = ttk.Label(status_frame, text="自动模式", foreground="blue")
        self.data_mode_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
    
    def setup_data_mode_control(self, parent):
        """设置数据模式控制"""
        mode_frame = ttk.LabelFrame(parent, text="数据模式控制", padding="5")
        mode_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        mode_frame.columnconfigure(1, weight=1)
        
        # 模式选择
        self.mode_var = tk.StringVar(value="auto")
        auto_radio = ttk.Radiobutton(mode_frame, text="自动模式", variable=self.mode_var, 
                                   value="auto", command=self.on_mode_change)
        auto_radio.grid(row=0, column=0, sticky=tk.W)
        
        manual_radio = ttk.Radiobutton(mode_frame, text="手动模式", variable=self.mode_var, 
                                     value="manual", command=self.on_mode_change)
        manual_radio.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        # 模式说明
        self.mode_description = ttk.Label(mode_frame, text="自动模式：系统自动生成随机数据并定期发送", 
                                        foreground="gray", font=("Arial", 8))
        self.mode_description.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
    
    def setup_sensor_data_display(self, parent):
        """设置传感器数据显示"""
        sensor_frame = ttk.LabelFrame(parent, text="传感器数据", padding="5")
        sensor_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        sensor_frame.columnconfigure(1, weight=1)
        
        # 温度
        ttk.Label(sensor_frame, text="温度:").grid(row=0, column=0, sticky=tk.W)
        self.temperature_var = tk.StringVar(value="20.0")
        self.temperature_entry = ttk.Entry(sensor_frame, textvariable=self.temperature_var, width=10)
        self.temperature_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(sensor_frame, text="°C").grid(row=0, column=2, sticky=tk.W)
        
        # pH值
        ttk.Label(sensor_frame, text="pH值:").grid(row=1, column=0, sticky=tk.W)
        self.ph_var = tk.StringVar(value="7.0")
        self.ph_entry = ttk.Entry(sensor_frame, textvariable=self.ph_var, width=10)
        self.ph_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(sensor_frame, text="pH").grid(row=1, column=2, sticky=tk.W)
        
        # 溶解氧
        ttk.Label(sensor_frame, text="溶解氧:").grid(row=2, column=0, sticky=tk.W)
        self.dissolved_oxygen_var = tk.StringVar(value="8.0")
        self.dissolved_oxygen_entry = ttk.Entry(sensor_frame, textvariable=self.dissolved_oxygen_var, width=10)
        self.dissolved_oxygen_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(sensor_frame, text="mg/L").grid(row=2, column=2, sticky=tk.W)
        
        # 水位
        ttk.Label(sensor_frame, text="水位:").grid(row=3, column=0, sticky=tk.W)
        self.water_level_var = tk.StringVar(value="1.5")
        self.water_level_entry = ttk.Entry(sensor_frame, textvariable=self.water_level_var, width=10)
        self.water_level_entry.grid(row=3, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(sensor_frame, text="m").grid(row=3, column=2, sticky=tk.W)

        # 流速
        ttk.Label(sensor_frame, text="流速:").grid(row=4, column=0, sticky=tk.W)
        self.flow_rate_var = tk.StringVar(value="1.5")
        self.flow_rate_entry = ttk.Entry(sensor_frame, textvariable=self.flow_rate_var, width=10)
        self.flow_rate_entry.grid(row=4, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(sensor_frame, text="m/s").grid(row=4, column=2, sticky=tk.W)

        # 更新传感器数据按钮
        self.update_sensor_btn = ttk.Button(sensor_frame, text="更新传感器数据", command=self.update_sensor_data)
        self.update_sensor_btn.grid(row=5, column=0, columnspan=3, pady=(10, 0))
    
    def setup_location_data_display(self, parent):
        """设置位置数据显示"""
        location_frame = ttk.LabelFrame(parent, text="位置数据", padding="5")
        location_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        location_frame.columnconfigure(1, weight=1)
        
        # 经度
        ttk.Label(location_frame, text="经度:").grid(row=0, column=0, sticky=tk.W)
        self.longitude_var = tk.StringVar(value=str(BASE_LONGITUDE))
        self.longitude_entry = ttk.Entry(location_frame, textvariable=self.longitude_var, width=15)
        self.longitude_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 纬度
        ttk.Label(location_frame, text="纬度:").grid(row=1, column=0, sticky=tk.W)
        self.latitude_var = tk.StringVar(value=str(BASE_LATITUDE))
        self.latitude_entry = ttk.Entry(location_frame, textvariable=self.latitude_var, width=15)
        self.latitude_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 更新位置数据按钮
        self.update_location_btn = ttk.Button(location_frame, text="更新位置数据", command=self.update_location_data)
        self.update_location_btn.grid(row=2, column=0, columnspan=2, pady=(10, 0))
    
    def setup_light_status_display(self, parent):
        """设置灯光状态显示"""
        light_frame = ttk.LabelFrame(parent, text="灯光状态", padding="5")
        light_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        light_frame.columnconfigure(1, weight=1)
        
        # 灯光状态
        ttk.Label(light_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        self.light_status_label = ttk.Label(light_frame, text="关闭", foreground="red")
        self.light_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 亮度显示
        ttk.Label(light_frame, text="亮度:").grid(row=1, column=0, sticky=tk.W)
        self.brightness_label = ttk.Label(light_frame, text="0%")
        self.brightness_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 颜色显示
        ttk.Label(light_frame, text="颜色:").grid(row=2, column=0, sticky=tk.W)
        self.color_label = ttk.Label(light_frame, text="#FFFFFF")
        self.color_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 颜色预览
        self.color_preview = tk.Canvas(light_frame, width=30, height=20, bg="#FFFFFF", relief=tk.SUNKEN)
        self.color_preview.grid(row=2, column=2, padx=(5, 0))
        
        # 模拟灯光效果
        self.light_effect = tk.Canvas(light_frame, width=100, height=50, bg="black", relief=tk.SUNKEN)
        self.light_effect.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        # 更新灯光显示
        self.update_light_display()
    
    def setup_control_buttons(self, parent):
        """设置控制按钮"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=7, column=0, columnspan=2, pady=(0, 10))
        
        # 发送心跳按钮
        self.send_heartbeat_btn = ttk.Button(control_frame, text="发送心跳", command=self.send_heartbeat)
        self.send_heartbeat_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 发送所有数据按钮
        self.send_all_btn = ttk.Button(control_frame, text="发送所有数据", command=self.send_all_data)
        self.send_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清空日志按钮
        self.clear_log_btn = ttk.Button(control_frame, text="清空日志", command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT)
    
    def setup_log_display(self, parent):
        """设置日志显示"""
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding="5")
        log_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        parent.rowconfigure(8, weight=1)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
    
    def _apply_current_config(self):
        """应用当前的浮标配置（内部方法）"""
        try:
            new_buoy_id = int(self.buoy_id_var.get())
            if new_buoy_id <= 0:
                raise ValueError("浮标ID必须为正整数")

            # 更新配置
            old_buoy_id = self.buoy_id
            self.buoy_id = new_buoy_id
            self.client_id = f"buoy-controller-gui-{self.buoy_id}"
            self.control_topic = f"buoy/command/{self.buoy_id}"
            self.status_topic = f"buoy/{self.buoy_id}/status"

            if old_buoy_id != new_buoy_id:
                self.log_message(f"浮标ID已更新为: {self.buoy_id}")

        except ValueError as e:
            # 如果配置无效，保持原有配置
            self.buoy_id_var.set(str(self.buoy_id))
            raise ValueError(f"浮标ID配置无效: {str(e)}")
    
    def on_mode_change(self):
        """模式切换处理"""
        self.auto_mode = self.mode_var.get() == "auto"
        
        if self.auto_mode:
            self.data_mode_label.config(text="自动模式", foreground="blue")
            self.mode_description.config(text="自动模式：系统自动生成随机数据并定期发送")
            # 启用自动数据线程
            if self.connected and not self.running:
                self.start_data_thread()
        else:
            self.data_mode_label.config(text="手动模式", foreground="orange")
            self.mode_description.config(text="手动模式：用户手动设置数据并发送")
            # 停止自动数据线程
            self.running = False
        
        self.log_message(f"已切换到{'自动' if self.auto_mode else '手动'}模式")
    
    def connect_mqtt(self):
        """连接MQTT服务器"""
        try:
            # 首先应用当前的浮标ID配置
            try:
                self._apply_current_config()
            except ValueError as e:
                messagebox.showerror("配置错误", str(e))
                return False

            # 重新创建MQTT客户端以避免TLS配置冲突
            self.client = mqtt.Client(client_id=self.client_id, callback_api_version=mqtt.CallbackAPIVersion.VERSION1)

            # 设置认证信息
            if MQTT_USERNAME:
                self.client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)

            # 设置LWT（遗嘱消息）
            self.client.will_set(self.status_topic, payload="offline", qos=1, retain=False)

            # 设置回调
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_message = self.on_message

            # 如果启用了TLS，配置TLS连接
            if MQTT_USE_TLS:
                import ssl
                ssl_context = ssl.create_default_context()

                if MQTT_CA_CERT_PATH:
                    try:
                        ssl_context.load_verify_locations(cafile=MQTT_CA_CERT_PATH)
                        self.log_message(f"已加载MQTT CA证书: {MQTT_CA_CERT_PATH}")
                    except Exception as cert_err:
                        self.log_message(f"加载MQTT CA证书失败: {str(cert_err)}")
                        ssl_context.check_hostname = False
                        ssl_context.verify_mode = ssl.CERT_NONE
                        self.log_message("已禁用MQTT服务器证书验证")

                self.client.tls_set_context(ssl_context)
                self.log_message("已启用MQTT TLS连接")

            # 连接到MQTT服务器
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            self.log_message(f"已连接到MQTT服务器: {MQTT_BROKER}:{MQTT_PORT}")

            # 更新按钮状态
            self.connect_btn.config(state=tk.DISABLED)
            self.disconnect_btn.config(state=tk.NORMAL)
            self.connection_status_label.config(text="连接中...", foreground="orange")

            return True
        except Exception as e:
            self.log_message(f"连接MQTT服务器时出错: {e}")
            messagebox.showerror("连接错误", f"连接MQTT服务器失败: {e}")
            return False
    
    def disconnect_mqtt(self):
        """断开MQTT连接"""
        if not self.connected:
            return

        # 立即更新UI状态，提供即时反馈
        self.connection_label.config(text="断开中...", foreground="orange")
        self.connection_status_label.config(text="断开中...", foreground="orange")
        self.disconnect_btn.config(state=tk.DISABLED)

        try:
            self.running = False  # 停止自动数据线程

            # 异步处理断开连接，避免阻塞UI
            def _disconnect_async():
                try:
                    # 等待数据线程结束，但时间更短
                    if self.data_thread and self.data_thread.is_alive():
                        self.data_thread.join(timeout=0.5)  # 减少到0.5秒

                    # 发布offline消息，但不等待
                    if hasattr(self, 'client') and self.client:
                        self.client.publish(self.status_topic, "offline", qos=0, retain=False)  # 使用QoS 0
                        self.client.disconnect()
                        self.client.loop_stop(force=True)

                    # 更新UI状态
                    self.root.after(0, self._update_disconnect_ui)
                    self.root.after(0, lambda: self.log_message("已断开MQTT连接"))

                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"断开MQTT连接时出错: {e}"))
                    self.root.after(0, self._update_disconnect_ui)

            # 在后台线程中执行断开操作
            threading.Thread(target=_disconnect_async, daemon=True).start()

        except Exception as e:
            self.log_message(f"断开MQTT连接时出错: {e}")
            self._update_disconnect_ui()

    def _update_disconnect_ui(self):
        """更新断开连接后的UI状态"""
        self.connected = False
        self.connection_label.config(text="未连接", foreground="red")
        self.connection_status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)
    
    def on_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            self.connected = True
            self.client.subscribe(self.control_topic)
            self.log_message(f"已订阅控制主题: {self.control_topic}")
            self.connection_label.config(text="已连接", foreground="green")
            self.connection_status_label.config(text="已连接", foreground="green")
            # 主动发布online
            self.client.publish(self.status_topic, "online", qos=1, retain=False)
            
            # 如果是自动模式，启动数据线程
            if self.auto_mode:
                self.start_data_thread()
        else:
            self.connected = False
            self.connection_label.config(text="连接失败", foreground="red")
            self.connection_status_label.config(text="连接失败", foreground="red")
            self.log_message(f"连接失败，返回码: {rc}")
            
            # 恢复按钮状态
            self.connect_btn.config(state=tk.NORMAL)
            self.disconnect_btn.config(state=tk.DISABLED)
    
    def on_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        self.connected = False
        self.connection_label.config(text="已断开", foreground="red")
        self.connection_status_label.config(text="已断开", foreground="red")
        self.log_message("已断开MQTT连接")
        
        # 恢复按钮状态
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)
    
    def on_message(self, client, userdata, msg):
        """MQTT消息回调 - 处理来自服务器的控制命令"""
        try:
            payload = json.loads(msg.payload.decode())
            self.log_message(f"收到控制命令: {payload}")
            
            if msg.topic == self.control_topic:
                self.handle_control_command(payload)
        except Exception as e:
            self.log_message(f"处理消息时出错: {e}")
    
    def handle_control_command(self, command):
        """处理来自服务器的控制命令"""
        if "command" not in command:
            return
        
        if command["command"] == "set_light":
            # 更新灯光设置
            if "brightness" in command:
                self.light_brightness = command["brightness"]
            if "color" in command:
                self.light_color = command["color"]
            
            # 根据亮度判断是否启用灯光
            self.light_enabled = self.light_brightness > 0
            
            # 更新GUI显示
            self.update_light_display()
            
            # 发送确认响应
            self.send_control_response(command, "success")
            
            self.log_message(f"灯光已更新: 状态={'开启' if self.light_enabled else '关闭'}, 亮度={self.light_brightness}%, 颜色={self.light_color}")
    
    def send_control_response(self, command, status):
        """发送控制响应"""
        if not self.connected:
            return
        
        try:
            response_topic = f"buoy/command/response/{self.buoy_id}"
            response = {
                "buoy_id": self.buoy_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": status,
                "command": command["command"],
                "current_settings": {
                    "brightness": self.light_brightness,
                    "color": self.light_color,
                    "enabled": self.light_enabled
                }
            }
            
            self.client.publish(response_topic, json.dumps(response))
            self.log_message(f"已发送控制响应: {status}")
            
        except Exception as e:
            self.log_message(f"发送控制响应时出错: {e}")
    
    def update_sensor_data(self):
        """更新传感器数据"""
        try:
            # 从输入框获取数据
            self.sensor_data["temperature"]["value"] = float(self.temperature_var.get())
            self.sensor_data["ph"]["value"] = float(self.ph_var.get())
            self.sensor_data["dissolved_oxygen"]["value"] = float(self.dissolved_oxygen_var.get())
            self.sensor_data["water_level"]["value"] = float(self.water_level_var.get())
            self.sensor_data["flow_rate"]["value"] = float(self.flow_rate_var.get())

            self.log_message("传感器数据已更新")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数值")
    
    def update_location_data(self):
        """更新位置数据"""
        try:
            # 从输入框获取数据
            self.location_data["longitude"] = float(self.longitude_var.get())
            self.location_data["latitude"] = float(self.latitude_var.get())
            
            self.log_message("位置数据已更新")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的坐标值")
    
    def send_sensor_data(self):
        """发送传感器数据"""
        if not self.connected:
            messagebox.showerror("错误", "未连接到MQTT服务器")
            return
        
        try:
            # 发送每种传感器数据
            for data_type, data in self.sensor_data.items():
                topic = f"{TOPIC_PREFIX}{self.buoy_id}/{data_type}"
                payload = {
                    "buoy_id": self.buoy_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "data_type": data_type,
                    "value": data["value"],
                    "unit": data["unit"]
                }
                self.client.publish(topic, json.dumps(payload))
                self.log_message(f"已发送{data_type}数据: {data['value']} {data['unit']}")
            
        except Exception as e:
            self.log_message(f"发送传感器数据时出错: {e}")
    
    def send_location_data(self):
        """发送位置数据"""
        if not self.connected:
            messagebox.showerror("错误", "未连接到MQTT服务器")
            return
        
        try:
            topic = f"{TOPIC_PREFIX}{self.buoy_id}/location"
            payload = {
                "buoy_id": self.buoy_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data_type": "location",
                "value": self.location_data,
                "unit": "WGS84"
            }
            
            self.client.publish(topic, json.dumps(payload))
            self.log_message(f"已发送位置数据: 经度={self.location_data['longitude']:.6f}, 纬度={self.location_data['latitude']:.6f}")
            
        except Exception as e:
            self.log_message(f"发送位置数据时出错: {e}")
    
    def send_heartbeat(self):
        """发送心跳"""
        if not self.connected:
            messagebox.showerror("错误", "未连接到MQTT服务器")
            return
        
        try:
            topic = f"buoy/heartbeat/{self.buoy_id}"
            payload = {
                "buoy_id": self.buoy_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": self.status
            }
            
            self.client.publish(topic, json.dumps(payload))
            self.log_message("已发送心跳")
            
        except Exception as e:
            self.log_message(f"发送心跳时出错: {e}")
    
    def send_all_data(self):
        """发送所有数据"""
        self.send_sensor_data()
        self.send_location_data()
        self.send_heartbeat()
        self.log_message("已发送所有数据")
    
    def start_data_thread(self):
        """启动数据发送线程"""
        if self.running:
            return
        self.running = True
        self.data_thread = threading.Thread(target=self._data_thread, daemon=True)
        self.data_thread.start()
        self.log_message("自动数据发送线程已启动")
    
    def _data_thread(self):
        """数据发送线程"""
        import random
        
        while self.running and self.auto_mode:
            try:
                if not self.connected:
                    time.sleep(5)
                    continue
                
                # 随机更新传感器数据
                self.sensor_data["temperature"]["value"] = round(random.uniform(15, 30), 1)
                self.sensor_data["ph"]["value"] = round(random.uniform(6.5, 8.5), 1)
                self.sensor_data["dissolved_oxygen"]["value"] = round(random.uniform(5, 12), 1)
                self.sensor_data["water_level"]["value"] = round(random.uniform(0.5, 2.5), 1)
                self.sensor_data["flow_rate"]["value"] = round(random.uniform(0.1, 3.0), 2)
                
                # 随机更新位置数据
                self.location_data["longitude"] = BASE_LONGITUDE + random.uniform(-0.01, 0.01)
                self.location_data["latitude"] = BASE_LATITUDE + random.uniform(-0.01, 0.01)
                
                # 更新GUI显示
                self.root.after(0, self.update_gui_from_data)
                
                # 发送数据
                self.send_sensor_data()
                self.send_location_data()
                self.send_heartbeat()
                
                # 等待一段时间
                wait_time = random.uniform(DATA_INTERVAL_MIN, DATA_INTERVAL_MAX)
                time.sleep(wait_time)
                
            except Exception as e:
                self.log_message(f"数据线程出错: {e}")
                time.sleep(5)
    
    def update_gui_from_data(self):
        """从数据更新GUI显示"""
        # 更新传感器数据显示
        self.temperature_var.set(str(self.sensor_data["temperature"]["value"]))
        self.ph_var.set(str(self.sensor_data["ph"]["value"]))
        self.dissolved_oxygen_var.set(str(self.sensor_data["dissolved_oxygen"]["value"]))
        self.water_level_var.set(str(self.sensor_data["water_level"]["value"]))
        self.flow_rate_var.set(str(self.sensor_data["flow_rate"]["value"]))

        # 更新位置数据显示
        self.longitude_var.set(f"{self.location_data['longitude']:.6f}")
        self.latitude_var.set(f"{self.location_data['latitude']:.6f}")
    
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        logger.info(message)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def update_light_display(self):
        """更新灯光显示"""
        # 更新状态标签
        if self.light_enabled:
            self.light_status_label.config(text="开启", foreground="green")
        else:
            self.light_status_label.config(text="关闭", foreground="red")
        
        # 更新亮度标签
        self.brightness_label.config(text=f"{self.light_brightness}%")
        
        # 更新颜色标签和预览
        self.color_label.config(text=self.light_color)
        self.color_preview.config(bg=self.light_color)
        
        # 更新灯光效果
        self.light_effect.delete("all")
        if self.light_enabled:
            # 计算颜色强度
            intensity = self.light_brightness / 100.0
            # 解析颜色
            color = self.light_color.lstrip('#')
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
            # 应用亮度
            r = int(r * intensity)
            g = int(g * intensity)
            b = int(b * intensity)
            # 绘制灯光效果
            light_color = f"#{r:02x}{g:02x}{b:02x}"
            self.light_effect.create_oval(10, 10, 90, 40, fill=light_color, outline="white")
            self.light_effect.create_text(50, 25, text="💡", font=("Arial", 16))
        else:
            # 显示关闭状态
            self.light_effect.create_oval(10, 10, 90, 40, fill="gray", outline="white")
            self.light_effect.create_text(50, 25, text="⚫", font=("Arial", 16))
    
    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出浮标模拟器吗？"):
            self.running = False
            if self.data_thread and self.data_thread.is_alive():
                self.data_thread.join(timeout=2)
            if self.connected:
                self.disconnect_mqtt()
            self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.log_message(f"浮标模拟器已启动，浮标ID: {self.buoy_id}")
        self.root.mainloop()


def main():
    """主函数"""
    logger.info("启动浮标模拟器GUI")
    
    # 注册信号处理程序
    def signal_handler(sig, frame):
        logger.info(f"接收到信号 {sig}，准备关闭...")
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # 创建并运行GUI模拟器
        simulator = BuoyControllerGUI()
        simulator.run()
    except KeyboardInterrupt:
        logger.info("接收到键盘中断，准备关闭...")
    except Exception as e:
        logger.error(f"运行模拟器时出错: {e}")
    finally:
        logger.info("浮标模拟器GUI已关闭")


if __name__ == "__main__":
    main() 