# 浮标设备模拟器

这个模拟器用于模拟多个浮标设备，向MQTT服务器发送传感器数据和位置信息，并响应控制命令。

## 功能

- 模拟多个浮标设备
- 定期发送模拟的传感器数据（温度、pH值、溶解氧、水位等）
- 发送位置数据
- 接收并响应控制命令
- 发送心跳包以维持连接状态

## 配置

可以通过环境变量或`.env`文件配置模拟器：

| 环境变量 | 说明 | 默认值 |
|---------|------|-------|
| MQTT_BROKER_HOST | MQTT服务器地址 | mqtt |
| MQTT_BROKER_PORT | MQTT服务器端口 | 1883 |
| MQTT_USERNAME | MQTT用户名 | (空) |
| MQTT_PASSWORD | MQTT密码 | (空) |
| SIM_BUOY_COUNT | 模拟的浮标数量 | 1 |
| SIM_DATA_INTERVAL_MIN | 数据发送最小间隔(秒) | 2 |
| SIM_DATA_INTERVAL_MAX | 数据发送最大间隔(秒) | 5 |
| SIM_TOPIC_PREFIX | 主题前缀 | buoy/data/ |
| SIM_BASE_LATITUDE | 基准纬度 | 31.2304 |
| SIM_BASE_LONGITUDE | 基准经度 | 121.4737 |

## 使用方法

### 使用Docker Compose

推荐使用docker-compose启动整个系统，包括模拟器：

```bash
docker-compose up -d
```

### 单独启动模拟器

如果需要单独启动模拟器：

```bash
cd simulator
docker build -t buoy-simulator .
docker run -d --name buoy-simulator --network=host buoy-simulator
```

## 控制命令

可以向浮标发送控制命令，格式为：

```json
{
  "command": "set_light",
  "brightness": 75,
  "color": "#FF8C00"
}
```

发送到主题：`buoy/control/{buoy_id}` 