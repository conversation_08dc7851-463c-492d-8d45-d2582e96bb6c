import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# MQTT配置
MQTT_BROKER = os.getenv("MQTT_BROKER_HOST", "mqtt")
MQTT_PORT = int(os.getenv("MQTT_BROKER_PORT", 1883))
MQTT_USERNAME = os.getenv("MQTT_USERNAME", "")
MQTT_PASSWORD = os.getenv("MQTT_PASSWORD", "")
MQTT_USE_TLS = os.getenv("MQTT_USE_TLS", "False").lower() == "true"
MQTT_CA_CERT_PATH = os.getenv("MQTT_CA_CERT_PATH", "/app/certs/mqtt-ca.crt")

# 模拟器配置
BUOY_COUNT = int(os.getenv("SIM_BUOY_COUNT", 1))
DATA_INTERVAL_MIN = float(os.getenv("SIM_DATA_INTERVAL_MIN", 2))
DATA_INTERVAL_MAX = float(os.getenv("SIM_DATA_INTERVAL_MAX", 5))
TOPIC_PREFIX = os.getenv("SIM_TOPIC_PREFIX", "buoy/data/")

# 基准位置（上海某地）
BASE_LATITUDE = float(os.getenv("SIM_BASE_LATITUDE", 31.2304))
BASE_LONGITUDE = float(os.getenv("SIM_BASE_LONGITUDE", 121.4737))