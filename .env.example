# 数据库配置
POSTGRES_SERVER=db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=buoydb
POSTGRES_PORT=5432

# MQTT配置
MQTT_BROKER_HOST=mqtt
MQTT_BROKER_PORT=8883  # 使用MQTTS端口
MQTT_CLIENT_ID=backend-client
MQTT_USERNAME=
MQTT_PASSWORD=
MQTT_USE_TLS=true  # 启用TLS
MQTT_CA_CERT_PATH=/app/certs/mqtt-ca.crt  # 证书路径

# API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_ENDPOINT=https://api.deepseek.com/v1

# 安全配置
SECRET_KEY=your_secret_key_here

# 日志配置
LOG_LEVEL=INFO
ENV=production