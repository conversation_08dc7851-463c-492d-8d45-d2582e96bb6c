技术栈: Python/FastAPI, SQLAlchemy, PostgreSQL/PostGIS, TypeScript/React, CesiumJS, WebSocket/MQTT, docker-compose.

1. 通用原则
   代码: 简洁、准确、可读、DRY。
   风格: 函数式/声明式优先，避免非必要类。
   命名: 描述性 (`is_`, `has_`), `lowercase_underscore` (文件/目录), 命名导出, `handle` (React事件)。
   接口: RORO模式 (Receive Object, Return Object)。
   完整性: 功能完整，无Bug，无TODO。
   类型提示: 强制 (Python/TS)。
   流程: 理解需求 -> 计划 -> 编码 -> 评审 -> 测试。

2. 后端 (Python/FastAPI)
   异步: `async def` 强制用于 I/O (DB, API调用)。
   FastAPI:
       路由: `APIRouter`。
       验证/序列化: 强制 Pydantic V2 模型 (No raw dicts)。
       依赖注入 (DI)。
       生命周期: `lifespan` 优先。
       中间件: 用于通用逻辑 (日志, 错误, 认证)。
   数据库 (SQLAlchemy):
       使用 SQLAlchemy 2.0 `async` 风格。
       区分 Model (ORM) 与 Schema (API)。
       索引优化。
   代码结构: 标准化 (e.g., `models/`, `schemas/`, `routers/`)。
   逻辑: 卫语句 (Guard Clauses), 早期返回 (Early Returns)。

3. 前端 (TypeScript/React)
   组件: `const` 函数式组件 + 类型定义。
   代码流: 早期返回, Happy Path 最后。
   样式: 强制 Tailwind CSS (No CSS/`<style>`), `class:` 优先。
   状态: 合理选择状态管理方案。
   事件: `handle` 命名。
   可访问性 (A11y): `tabIndex`, `aria-`, 键盘导航 (`onClick`+`onKeyDown`)。
   CesiumJS: 封装 (模块/Hooks)。

4. 错误处理 & 验证
   入口验证: 卫语句。
   后端: `HTTPException` (预期), 中间件 (意外+日志)。
   前端: 优雅处理, 用户反馈, Error Boundaries。
   日志: 结构化日志。

5. 性能
   后端: 异步 I/O。
   缓存: 静态/频繁访问数据。
   加载: Lazy Loading (FE), 分页 (BE)。
   序列化: Pydantic 高效处理。
   监控: API 性能指标。

6. 协作
   清晰沟通, 遵循规范, 积极 Code Review。

7. 容器开发 (Docker)
   工具: Docker, docker-compose
   配置: docker-compose.yml 统一定义服务
   服务通信: 容器间通过服务名通信 (db, mqtt)，宿主机访问映射端口。
   持久化: 使用命名卷 (postgres_data, backend_images) 持久化数据。
   