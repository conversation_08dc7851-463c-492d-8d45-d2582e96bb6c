---
description: Best practices and guidelines for FastAPI
globs: 
alwaysApply: false
---
- Use `async def` for all I/O-bound operations (database, API calls).
- Organize routes using `APIRouter`.
- Enforce Pydantic V2 models for request/response validation and serialization.
- Implement dependency injection for better code organization.
- Utilize `lifespan` for application lifecycle management.
- Use middleware for common logic like logging, error handling, and authentication.
