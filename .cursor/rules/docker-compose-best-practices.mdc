---
description: Best practices and guidelines for Docker Compose
globs: 
alwaysApply: false
---
- Use `docker-compose.yml` to define and manage services.
- Implement service communication using service names (e.g., `db`, `mqtt`).

- Use named volumes for data persistence (e.g., `postgres_data`, `backend_images`).
- Map host ports for external access to services.
- Use environment variables for configuration management.