---
description: Best practices and guidelines for React
globs: 
alwaysApply: false
---
- Use functional components with `const` and TypeScript type definitions.
- Implement early returns and guard clauses for cleaner code flow.
- Use Tailwind CSS for styling, avoiding raw CSS or `<style>` tags.
- Choose appropriate state management solutions based on app complexity.
- Use `handle` prefix for event handlers.
- Ensure accessibility with `tabIndex`, `aria-*` attributes, and keyboard navigation.