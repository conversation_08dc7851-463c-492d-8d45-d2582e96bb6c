---
description: 
globs: 
alwaysApply: true
---
```tree
.
├── backend                             # 后端服务目录
│   ├── Dockerfile                      # 后端Docker配置文件
│   ├── fastapi_app                     # FastAPI应用目录
│   │   ├── api/                        # API路由定义
│   │   │   └── api_v1/                 # API版本1
│   │   │       ├── api.py              # API路由注册和配置
│   │   │       └── endpoints/          # API端点定义
│   │   │           ├── auth.py         # 认证相关API
│   │   │           ├── buoys.py        # 浮标相关API
│   │   │           ├── data.py         # 数据相关API
│   │   │           ├── settings.py     # 设置相关API
│   │   │           └── users.py        # 用户相关API
│   │   ├── core/                       # 核心配置和工具
│   │   │   └── config.py               # 应用配置
│   │   ├── db/                         # 数据库连接和操作
│   │   │   ├── base.py                 # 数据库基础配置
│   │   │   └── init_db.py              # 数据库初始化
│   │   ├── models/                     # 数据模型定义
│   │   │   ├── __init__.py             # 模型初始化
│   │   │   ├── additional.py           # 附加数据模型
│   │   │   ├── buoy.py                 # 浮标数据模型
│   │   │   ├── sensor_data.py          # 传感器数据模型
│   │   │   └── user.py                 # 用户数据模型
│   │   ├── schemas/                    # 数据验证模式
│   │   │   ├── buoy.py                 # 浮标数据验证模式
│   │   │   ├── settings.py             # 设置数据验证模式
│   │   │   └── user.py                 # 用户数据验证模式
│   │   ├── services/                   # 业务逻辑服务
│   │   │   ├── auth_service.py         # 认证服务
│   │   │   ├── buoy_service.py         # 浮标管理服务
│   │   │   ├── data_service.py         # 数据处理服务
│   │   │   ├── geo_service.py          # 地理数据处理服务
│   │   │   ├── mqtt_client.py          # MQTT客户端服务
│   │   │   └── security.py             # 安全相关服务
│   │   └── main.py                     # 应用入口文件
│   ├── images                          # 后端相关图片
│   ├── requirements.txt                # Python依赖列表
│   └── venv                            # Python虚拟环境
├── docker-compose.yaml                 # Docker Compose配置
├── frontend                            # 前端应用目录
│   ├── dist                            # 构建输出目录
│   ├── Dockerfile.dev                  # 前端开发Docker配置
│   ├── .dockerignore                   # Docker忽略文件配置
│   ├── .eslintrc.json                  # ESLint配置文件
│   ├── eslint.config.js                # ESLint扩展配置
│   ├── index.html                      # HTML入口文件
│   ├── node_modules                    # Node.js依赖
│   ├── package.json                    # NPM包管理文件
│   ├── package-lock.json               # NPM依赖锁定文件
│   ├── postcss.config.js               # PostCSS配置
│   ├── public                          # 公共资源目录
│   ├── README.md                       # 前端说明文档
│   ├── src                             # 源代码目录
│   │   ├── api/                        # API请求函数
│   │   │   └── index.ts                # API函数定义
│   │   ├── assets/                     # 静态资源
│   │   ├── components/                 # 可复用组件
│   │   │   ├── auth/                   # 认证相关组件
│   │   │   │   ├── index.ts            # 认证组件导出
│   │   │   │   ├── Login.tsx           # 登录组件
│   │   │   │   ├── ProtectedRoute.tsx  # 路由保护组件
│   │   │   │   ├── Register.tsx        # 注册组件
│   │   │   │   └── UserProfile.tsx     # 用户资料组件
│   │   │   ├── layout/                 # 布局相关组件
│   │   │   │   ├── index.ts            # 布局组件导出
│   │   │   │   ├── Footer.tsx          # 页脚组件
│   │   │   │   ├── Header.tsx          # 页头组件
│   │   │   │   └── Layout.tsx          # 布局容器组件
│   │   │   ├── map/                    # 地图相关组件
│   │   │   │   ├── CustomInfoBox.tsx   # 自定义信息框组件
│   │   │   │   ├── GlobeView.tsx       # 全球视图组件
│   │   │   │   └── SyncedViews.tsx     # 同步视图组件
│   │   │   ├── visualizations/         # 可视化组件
│   │   │   │   └── BuoyDataChart.tsx   # 浮标数据图表
│   │   │   └── index.ts                # 组件导出索引
│   │   ├── contexts/                   # React上下文
│   │   │   └── AuthContext.tsx         # 认证上下文
│   │   ├── pages/                      # 页面组件
│   │   │   ├── AuthPage.tsx            # 认证页面
│   │   │   ├── HomePage.tsx            # 首页
│   │   │   └── UserAccountPage.tsx     # 用户账户页面
│   │   ├── types/                      # TypeScript类型定义
│   │   │   └── index.ts                # 类型声明
│   │   ├── utils/                      # 工具函数
│   │   │   ├── cesium.ts               # Cesium相关工具
│   │   │   └── websocket.ts            # WebSocket相关工具
│   │   ├── App.tsx                     # 应用根组件
│   │   ├── index.css                   # 全局样式
│   │   ├── main.tsx                    # 应用入口
│   │   └── vite-env.d.ts               # Vite环境类型声明
│   ├── tailwind.config.js              # Tailwind CSS配置
│   ├── tsconfig.app.json               # TypeScript应用配置
│   ├── tsconfig.json                   # TypeScript主配置
│   ├── tsconfig.node.json              # Node.js TypeScript配置
│   └── vite.config.ts                  # Vite构建工具配置
├── mosquitto                           # MQTT代理服务器目录
│   ├── config                          # Mosquitto配置
│   ├── data                            # Mosquitto数据
│   └── log                             # Mosquitto日志
├── simulator                           # 浮标模拟器目录
│   ├── buoy_simulator.py               # 浮标模拟器主程序
│   ├── config.py                       # 模拟器配置文件
│   ├── Dockerfile                      # 模拟器Docker配置
│   ├── README.md                       # 模拟器说明文档
│   └── requirements.txt                # 模拟器Python依赖
├── 方案                                # 解决方案文档目录
├── 数据                                # 数据文件目录
├── 需求                                # 需求文档目录
├── 测试                                # 测试相关文件目录
├── README.md                           # 项目总体说明文档
├── roadmap.md                          # 项目路线图文档
├── TODOs.md                            # 待办事项列表
├── .cursorrules                        # Cursor编辑器规则
├── .cursorignore                       # Cursor忽略规则
├── .gitignore                          # Git忽略规则
└── PROJECT_STRUCTURE.md                # 本项目结构文档
```