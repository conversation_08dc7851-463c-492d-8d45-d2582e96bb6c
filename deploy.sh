#!/bin/bash

# 智能浮标管理平台部署脚本
# 使用方法: ./deploy.sh [dev|prod]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f .env ]; then
        print_warning ".env 文件不存在，请创建并配置环境变量"
        print_info "参考 DEPLOYMENT.md 中的环境变量配置说明"
        exit 1
    fi
    print_success "环境变量文件检查通过"
}

# 构建前端
build_frontend() {
    print_info "构建前端静态文件..."
    
    if [ ! -d "frontend/node_modules" ]; then
        print_info "安装前端依赖..."
        cd frontend
        npm install
        cd ..
    fi
    
    cd frontend
    npm run build
    cd ..
    
    if [ ! -d "frontend/dist" ]; then
        print_error "前端构建失败，dist 目录不存在"
        exit 1
    fi
    
    print_success "前端构建完成"
}

# 部署开发环境
deploy_dev() {
    print_info "部署开发环境..."
    docker-compose -f docker-compose.dev.yaml up -d
    print_success "开发环境部署完成"
    print_info "前端访问地址: http://localhost:3000"
    print_info "后端 API 地址: http://localhost:8000"
    print_info "数据库端口: 5432"
}

# 部署生产环境
deploy_prod() {
    print_info "部署生产环境..."
    
    # 检查域名配置
    if grep -q "your-domain.com" caddy/Caddyfile; then
        print_warning "请先在 caddy/Caddyfile 中配置您的域名"
        print_info "将 'your-domain.com' 替换为您的实际域名"
        exit 1
    fi
    
    # 构建前端
    build_frontend
    
    # 启动服务
    docker-compose up -d
    
    print_success "生产环境部署完成"
    print_info "请等待 Caddy 自动申请 SSL 证书..."
    print_info "查看日志: docker-compose logs -f caddy"
}

# 显示帮助信息
show_help() {
    echo "智能浮标管理平台部署脚本"
    echo ""
    echo "使用方法:"
    echo "  ./deploy.sh dev   - 部署开发环境"
    echo "  ./deploy.sh prod  - 部署生产环境"
    echo "  ./deploy.sh help  - 显示帮助信息"
    echo ""
    echo "部署前请确保:"
    echo "  1. 已安装 Docker 和 Docker Compose"
    echo "  2. 已配置 .env 环境变量文件"
    echo "  3. 生产环境需配置域名（修改 caddy/Caddyfile）"
}

# 主函数
main() {
    case "${1:-help}" in
        "dev")
            check_dependencies
            check_env_file
            deploy_dev
            ;;
        "prod")
            check_dependencies
            check_env_file
            deploy_prod
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"
