name: buoy-project-dev

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app:z
      - /app/node_modules
    env_file:
      - .env
      - ./frontend/.env
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app:z
      - ./backend/images:/app/images:z
      - ./backend/certs:/app/certs:z
    env_file:
      - .env
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - ENV=${ENV:-development}
    depends_on:
      - db
      - mqtt
    command: uvicorn fastapi_app.main:app --host 0.0.0.0 --port 8000 --reload

  db:
    image: postgis/postgis:15-3.3
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data:z
    env_file:
      - .env
    environment:
      - TZ=${TZ:-Asia/Shanghai}

  mqtt:
    image: eclipse-mosquitto:2
    user: "1000:1000"
    ports:
      - "1883:1883"
      - "8883:8883"
      - "9001:9001"
    volumes:
      - ./mosquitto/config:/mosquitto/config:Z
      - ./mosquitto/data:/mosquitto/data:Z
      - ./mosquitto/log:/mosquitto/log:Z
      - ./mosquitto/certs:/mosquitto/certs:Z
    environment:
      - TZ=${TZ:-Asia/Shanghai}

  simulator:
    build: ./simulator
    container_name: buoy-simulator
    profiles: ["sim"]
    restart: unless-stopped
    depends_on:
      - mqtt
    volumes:
      - ./simulator:/app:z
      - ./simulator/certs:/app/certs:z
    env_file:
      - .env
      - ./simulator/.env
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}

volumes:
  postgres_data_dev:
