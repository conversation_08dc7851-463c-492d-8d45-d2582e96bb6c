# 智能浮标管理与互动平台项目计划

## 项目现状分析
- **后端**：已使用 FastAPI 框架，实现了用户认证、浮标管理和数据处理功能。已实现 WebSocket 连接的 token 认证机制、时间字段的 UTC 处理、日志级别的区分以及 MQTTS 加密通信。
- **前端**：已使用 React 框架和 CesiumJS 进行地图可视化，实现了 WebSocket 实时数据更新，并已将 WebSocket 功能整合到 BuoyContext 中，实现了自动连接和数据更新。需要优化组件依赖关系，并考虑用户对系统性测试系统的暂时不考虑。
- **实时通信**：已使用 MQTT 进行数据传输和指令控制，并已升级为 MQTTS 以增强安全性。需要确保可靠的重连机制。
- **基础设施**：已使用 Docker Compose 管理服务。需要确保所有服务在 Docker 容器中运行，并使用命名卷进行数据持久化。

## 实施方案设计
将项目计划分解为以下几个关键步骤，每个步骤都可独立测试和验证：

### 步骤 1：后端功能完善
- **目标**：确保 FastAPI 后端功能完全符合用户需求。
- **任务**：
  - 确保所有 I/O 操作使用 async def 处理，使用 Pydantic V2 模型进行请求/响应验证。
  - 确保 SQLAlchemy 2.0 的异步风格用于数据库操作，分离 ORM 模型和 API 模式。
  - 为 admin 角色用户提供数据库管理功能。
  - 通过 WebSocket 向前端反馈命令执行情况。
- **测试**：编写单元测试和集成测试，验证每个功能的正确性。

### 步骤 2：前端功能优化
- **目标**：优化 React 前端功能，提升用户体验和性能。
- **任务**：
  - 使用函数式组件和 TypeScript 类型定义，从 `@frontend/src/types/index.ts` 导入和使用类型定义。
  - 使用 Tailwind CSS 进行样式设计，确保系统颜色主题的一致性，修改 AuthPage、Header 和 Footer 组件以正确应用主题颜色系统。
  - 将 CesiumJS 功能封装到模块或自定义钩子中，优化场景更新性能。
  - 确保 WebSocket 实时订阅功能仅在 `/home` 页面中实现，而不是在其他页面。
  - 通过 BuoyProvider 管理 WebSocket 连接，避免手动管理组件间的包裹关系。
- **测试**：根据用户反馈，暂时不考虑实现系统性的测试系统。

### 步骤 3：实时通信安全升级
- **目标**：确保可靠的通信机制。
- **任务**：
  - 实现 MQTT 重连逻辑和适当的 QoS 级别。
  - 确保 WebSocket 订阅机制正常工作，通过 WebSocket 主题正确广播 MQTT 消息。
  - 将证书文件添加到 `.gitignore` 中以排除在版本控制之外。
- **测试**：进行安全测试和压力测试，验证通信的可靠性和安全性。

### 步骤 4：基础设施优化
- **目标**：优化 Docker Compose 配置，确保服务在容器中运行。
- **任务**：
  - 确保后端和前端在 Docker 容器中运行，避免直接在 shell 中使用 python 或 npm 命令。
  - 使用命名卷进行数据持久化。
- **测试**：进行容器化部署测试，验证服务的可用性和数据持久性。

### 步骤 5：开发流程规范化
- **目标**：规范开发流程，提升开发效率和代码质量。
- **任务**：
  - 将项目计划分解为多个可测试的步骤。
  - 在进行代码修改前先分析现状并设计实施方案。
  - 将实现方案和步骤写入 `roadmap` 目录下的 markdown 文档中。
- **测试**：定期审查开发流程，确保符合用户偏好。

## Mermaid 图表
以下是更新后项目计划的简化流程图，以帮助更清晰地展示步骤和依赖关系：

```mermaid
graph TD
    A[项目启动] --> B[后端功能完善]
    A --> C[前端功能优化]
    A --> D[实时通信安全升级]
    A --> E[基础设施优化]
    A --> F[开发流程规范化]
    B --> G[测试与验证]
    C --> G
    D --> G
    E --> G
    F --> G
    G --> H[项目阶段性完成]