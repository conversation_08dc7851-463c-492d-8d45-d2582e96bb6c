# 管理员页面设计方案

## 1. 功能概述

管理员页面将提供以下核心功能：

1. **用户管理**：
   - 查看所有用户列表
   - 创建新用户（包括设置管理员权限）
   - 编辑用户信息
   - 删除用户

2. **浮标管理**：
   - 查看所有浮标列表
   - 创建新浮标
   - 编辑浮标信息
   - 删除浮标以及对应的传感器数据

3. **数据管理**：
   - 数据库统计信息查看
   - 历史数据清理（传感器数据、通知、控制日志、图像数据）
   - 数据导出功能

4. **系统监控**：
   - 系统运行状态
   - 数据库使用情况
   - 活跃用户统计

## 2. 页面布局设计

管理员页面将采用现代化的布局设计，包括：

1. **顶部导航栏**：显示当前页面标题和用户信息
2. **侧边栏**：提供功能导航菜单
3. **主内容区**：根据选择的功能显示相应的内容
4. **标签页**：在主内容区内使用标签页组织不同的管理功能

## 3. UI组件设计

页面将使用以下UI组件：

1. **数据表格**：使用响应式表格展示用户和浮标列表
2. **表单**：用于创建和编辑用户/浮标信息
3. **卡片**：展示统计信息和系统状态
4. **图表**：可视化数据统计和系统监控信息
5. **模态框**：用于确认删除操作和显示详细信息
6. **通知组件**：显示操作结果反馈

## 4. 技术实现方案

1. **前端**：
   - 使用React函数式组件和TypeScript
   - 使用Tailwind CSS进行样式设计
   - 使用React Router进行子路由管理
   - 使用Context API进行状态管理

2. **API交互**：
   - 使用Axios进行API请求
   - 实现API请求的错误处理和加载状态管理
   - 使用React Query进行数据缓存和自动刷新

3. **权限控制**：
   - 使用现有的ProtectedRoute组件确保只有管理员可以访问
   - 在API请求中验证管理员权限

## 5. 数据流设计

1. **用户管理**：
   - 获取用户列表：`GET /api/v1/admin/users`
   - 创建用户：`POST /api/v1/admin/users`
   - 更新用户：`PUT /api/v1/admin/users/{user_id}`
   - 删除用户：`DELETE /api/v1/admin/users/{user_id}`

2. **浮标管理**：
   - 获取浮标列表：`GET /api/v1/buoys`
   - 创建浮标：`POST /api/v1/buoys`
   - 更新浮标：`PUT /api/v1/buoys/{buoy_id}`
   - 删除浮标：`DELETE /api/v1/buoys/{buoy_id}`

3. **数据管理**：
   - 获取数据库统计：`GET /api/v1/admin/stats`
   - 清理传感器数据：`DELETE /api/v1/admin/cleanup/sensor-data`
   - 清理通知：`DELETE /api/v1/admin/cleanup/notifications`
   - 清理控制日志：`DELETE /api/v1/admin/cleanup/control-logs`
   - 清理图像数据：`DELETE /api/v1/admin/cleanup/images`

# 实现计划

## 阶段一：基础结构搭建

1. **创建管理员页面组件**：
   - 创建AdminPage.tsx作为主页面组件
   - 实现基本布局（侧边栏、主内容区）
   - 设置子路由结构

2. **创建管理功能组件**：
   - 创建UserManagement.tsx用户管理组件
   - 创建BuoyManagement.tsx浮标管理组件
   - 创建DataManagement.tsx数据管理组件
   - 创建SystemMonitor.tsx系统监控组件

## 阶段二：用户管理功能实现

1. **用户列表展示**：
   - 实现用户数据获取和表格展示
   - 添加分页、排序和搜索功能

2. **用户创建和编辑**：
   - 实现用户创建表单
   - 实现用户编辑表单
   - 添加表单验证

3. **用户删除功能**：
   - 实现删除确认对话框
   - 实现删除API调用和状态更新

## 阶段三：浮标管理功能实现

1. **浮标列表展示**：
   - 实现浮标数据获取和表格展示
   - 添加分页、排序和搜索功能

2. **浮标创建和编辑**：
   - 实现浮标创建表单
   - 实现浮标编辑表单
   - 添加表单验证

3. **浮标删除功能**：
   - 实现删除确认对话框
   - 实现删除API调用和状态更新

## 阶段四：数据管理功能实现

1. **数据库统计展示**：
   - 实现统计数据获取和卡片展示
   - 添加数据可视化图表

2. **数据清理功能**：
   - 实现传感器数据清理功能
   - 实现通知清理功能
   - 实现控制日志清理功能
   - 添加确认对话框和结果反馈

## 阶段五：系统监控功能实现

1. **系统状态展示**：
   - 实现系统状态数据获取和展示
   - 添加状态指标可视化

2. **用户活跃度统计**：
   - 实现用户活跃度数据获取和展示
   - 添加活跃度图表

## 阶段六：优化和测试

1. **UI/UX优化**：
   - 优化页面布局和响应式设计
   - 添加加载状态和错误处理
   - 优化表单交互体验

2. **功能测试**：
   - 测试所有CRUD操作
   - 测试权限控制
   - 测试边界情况和错误处理

3. **性能优化**：
   - 优化数据加载和缓存策略
   - 优化组件渲染性能

## 文件结构

```
frontend/
└── src/
    ├── pages/
    │   └── AdminPage.tsx                # 管理员主页面
    ├── components/
    │   └── admin/                       # 管理员组件目录
    │       ├── index.ts                 # 导出所有管理员组件
    │       ├── UserManagement.tsx       # 用户管理组件
    │       ├── BuoyManagement.tsx       # 浮标管理组件
    │       ├── DataManagement.tsx       # 数据管理组件
    │       ├── SystemMonitor.tsx        # 系统监控组件
    │       ├── UserForm.tsx             # 用户创建/编辑表单
    │       ├── BuoyForm.tsx             # 浮标创建/编辑表单
    │       ├── ConfirmDialog.tsx        # 确认对话框
    │       └── StatsCard.tsx            # 统计信息卡片
    └── api/
        └── adminService.ts              # 管理员API服务
```
