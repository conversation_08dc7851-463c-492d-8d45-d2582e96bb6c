# 环境变量管理指南

本文档详细说明了项目中环境变量的管理方法，旨在统一各个服务的环境变量配置方式，提高项目的可维护性和安全性。

## 1. 当前环境变量管理现状

目前项目中环境变量管理存在以下情况：

1. **多种配置方式并存**：
   - 部分服务使用`.env`文件（如根目录、frontend、simulator）
   - 部分配置直接在`docker-compose.yaml`中硬编码

2. **现有环境变量文件**：
   - 根目录：`.env`（数据库、MQTT、API密钥等全局配置）
   - 前端：`frontend/.env`（API路径、WebSocket配置等）
   - 模拟器：`simulator/.env`（模拟器特定配置）

3. **示例文件**：
   - 根目录：`.env.example`
   - 模拟器：`simulator/.env.example`

## 2. 统一环境变量管理方案

### 2.1 文件结构

采用分层环境变量管理方式，文件结构如下：

```
项目根目录/
├── .env                  # 全局环境变量（生产环境）
├── .env.development      # 开发环境全局变量
├── .env.example          # 全局环境变量示例和文档
├── frontend/
│   ├── .env              # 前端特定环境变量
│   └── .env.example      # 前端环境变量示例和文档
├── backend/
│   ├── .env              # 后端特定环境变量（可选）
│   └── .env.example      # 后端环境变量示例和文档（可选）
└── simulator/
    ├── .env              # 模拟器特定环境变量
    └── .env.example      # 模拟器环境变量示例和文档
```

### 2.2 环境变量优先级

环境变量加载优先级从高到低：

1. 服务特定的`.env`文件（如`frontend/.env`）
2. 根目录下的环境特定文件（如`.env.development`）
3. 根目录下的默认`.env`文件
4. Docker Compose中定义的默认值

### 2.3 Docker Compose配置

在`docker-compose.yaml`中统一使用以下方式配置环境变量：

```yaml
services:
  service-name:
    # 加载环境变量文件
    env_file:
      - .env                  # 全局环境变量
      - ./service-dir/.env    # 服务特定环境变量
    
    # 提供默认值或覆盖特定变量
    environment:
      - VARIABLE=${VARIABLE:-default_value}
```

## 3. 环境变量命名规范

### 3.1 命名前缀

- **全局共享变量**：使用通用名称（如`POSTGRES_USER`）
- **前端变量**：使用`VITE_`前缀（如`VITE_API_BASE_URL`）
- **模拟器变量**：使用`SIM_`前缀（如`SIM_BUOY_COUNT`）
- **后端变量**：可使用`API_`前缀（如`API_RATE_LIMIT`）

### 3.2 变量分类

- **数据库配置**：`POSTGRES_*`
- **MQTT配置**：`MQTT_*`
- **API配置**：`API_*`、`VITE_API_*`
- **安全配置**：`SECRET_*`、`*_KEY`
- **日志配置**：`LOG_*`
- **时区配置**：`TZ`

## 4. 环境变量文件内容

### 4.1 全局环境变量（`.env`）

包含所有服务共享的配置：

```
# 环境类型
ENV=production  # development, production, testing

# 数据库配置
POSTGRES_SERVER=db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=buoydb
POSTGRES_PORT=5432

# MQTT配置
MQTT_BROKER_HOST=mqtt
MQTT_BROKER_PORT=1883
MQTT_CLIENT_ID=backend-client
MQTT_USERNAME=
MQTT_PASSWORD=
MQTT_USE_TLS=false

# API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_ENDPOINT=https://api.deepseek.com/v1

# 安全配置
SECRET_KEY=your_secret_key_here

# 日志配置
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# 时区配置
TZ=Asia/Shanghai
```

### 4.2 前端环境变量（`frontend/.env`）

包含前端特定配置：

```
# API配置
VITE_API_BASE_URL=/api/v1
VITE_BACKEND_URL=http://backend:8000
VITE_WEBSOCKET_URL=ws://backend:8000
VITE_WEBSOCKET_PATH=/ws

# 其他前端特定配置
# VITE_FEATURE_FLAG_ENABLE_NEW_UI=false
# VITE_MAPBOX_TOKEN=your_mapbox_token_here
```

### 4.3 模拟器环境变量（`simulator/.env`）

包含模拟器特定配置：

```
# MQTT配置
MQTT_BROKER_HOST=mqtt
MQTT_BROKER_PORT=1883
MQTT_USERNAME=
MQTT_PASSWORD=

# 模拟器配置
SIM_BUOY_COUNT=3
SIM_DATA_INTERVAL_MIN=60
SIM_DATA_INTERVAL_MAX=120
SIM_TOPIC_PREFIX=buoy/data/

# 基准位置（上海某地）
SIM_BASE_LATITUDE=31.2304
SIM_BASE_LONGITUDE=121.4737 

# 配置日志
LOG_LEVEL=INFO

# 配置时区
TZ=Asia/Shanghai
```

## 5. 环境变量安全管理

### 5.1 敏感信息处理

- 敏感信息（如API密钥、密码）不应提交到版本控制系统
- 在`.gitignore`中添加所有`.env`文件（除了`.env.example`）
- 使用`.env.example`作为模板，提供默认的非敏感值

### 5.2 生产环境配置

- 生产环境的敏感配置应通过安全的方式管理（如密钥管理服务）
- 部署时通过CI/CD流程注入环境变量，避免将敏感信息存储在代码仓库中

## 6. 环境变量使用指南

### 6.1 开发环境设置

1. 复制示例文件创建本地环境变量文件：
   ```bash
   cp .env.example .env
   cp frontend/.env.example frontend/.env
   cp simulator/.env.example simulator/.env
   ```

2. 根据需要修改环境变量值

3. 启动服务：
   ```bash
   docker-compose up -d
   ```

### 6.2 添加新环境变量流程

1. 确定环境变量的作用域（全局或服务特定）
2. 选择合适的命名前缀
3. 更新相应的`.env.example`文件
4. 在项目文档中说明新变量的用途和可能的值
5. 通知团队成员更新他们的本地`.env`文件

### 6.3 环境变量访问方式

- **后端（Python）**：
  ```python
  import os
  
  database_url = os.getenv("POSTGRES_SERVER")
  ```

- **前端（JavaScript/TypeScript）**：
  ```javascript
  // Vite项目中
  const apiUrl = import.meta.env.VITE_API_BASE_URL;
  ```

- **Docker Compose**：
  ```yaml
  environment:
    - VARIABLE=${VARIABLE:-default_value}
  ```

## 7. 常见问题解决

### 7.1 环境变量未加载

- 检查文件名是否正确（`.env`而不是`env`）
- 确认文件位置正确（相对于docker-compose.yaml的路径）
- 使用`docker-compose config`命令查看解析后的配置

### 7.2 前端环境变量不可访问

- 确保使用了正确的前缀（Vite项目使用`VITE_`前缀）
- 重新构建前端项目以加载新的环境变量

### 7.3 服务启动失败

- 检查是否缺少必要的环境变量
- 查看服务日志以获取详细错误信息

## 8. 最佳实践

1. **保持简单**：只添加必要的环境变量
2. **提供默认值**：为非敏感配置提供合理的默认值
3. **文档化**：记录所有环境变量的用途和可能的值
4. **分离敏感信息**：将敏感信息与非敏感配置分开管理
5. **定期审查**：定期清理不再使用的环境变量
6. **环境区分**：使用不同的环境变量文件区分开发、测试和生产环境

## 9. 实施计划

1. **准备阶段**：
   - 创建或更新所有`.env.example`文件
   - 编写环境变量管理文档

2. **迁移阶段**：
   - 从docker-compose.yaml中提取硬编码的环境变量到相应的`.env`文件
   - 更新docker-compose.yaml使用`env_file`和变量引用

3. **验证阶段**：
   - 测试所有服务是否能正确加载环境变量
   - 确认敏感信息不会被意外提交到版本控制系统

4. **培训阶段**：
   - 向团队成员介绍新的环境变量管理方式
   - 提供环境变量更新的最佳实践
