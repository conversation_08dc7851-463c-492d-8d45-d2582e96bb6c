# 环境变量管理指南

本项目采用分层环境变量管理方式，确保配置的一致性和灵活性。

## 环境变量文件结构

```
项目根目录/
├── .env                  # 全局环境变量（生产环境）
├── .env.development      # 开发环境全局变量
├── .env.example          # 全局环境变量示例和文档
├── frontend/
│   ├── .env              # 前端特定环境变量
│   └── .env.example      # 前端环境变量示例和文档
└── simulator/
    ├── .env              # 模拟器特定环境变量
    └── .env.example      # 模拟器环境变量示例和文档
```

## 环境变量优先级

1. 服务特定的`.env`文件（如`frontend/.env`）
2. 根目录下的环境特定文件（如`.env.development`）
3. 根目录下的默认`.env`文件
4. Docker Compose中定义的默认值

## 环境变量使用规范

### 命名规范

- 全局共享变量：使用通用名称（如`POSTGRES_USER`）
- 服务特定变量：使用前缀区分（如`VITE_`前缀用于前端，`SIM_`前缀用于模拟器）

### 敏感信息处理

- 敏感信息（如API密钥、密码）不应提交到版本控制系统
- 在`.gitignore`中添加所有`.env`文件（除了`.env.example`）
- 使用`.env.example`作为模板，提供默认的非敏感值

### 环境区分

- 使用`ENV`变量区分环境：`development`、`production`、`testing`
- 根据环境使用不同的配置文件：`.env.development`、`.env.production`等

## Docker Compose集成

- 在`docker-compose.yaml`中使用`env_file`指定环境变量文件
- 使用`${VARIABLE:-default}`语法提供默认值
- 避免在Docker Compose文件中硬编码环境变量值

## 环境变量更新流程

1. 如需添加新的环境变量，先更新相应的`.env.example`文件
2. 在项目文档中说明新变量的用途和可能的值
3. 通知团队成员更新他们的本地`.env`文件

## 常见问题解决

- 如果服务无法启动，检查是否缺少必要的环境变量
- 使用`docker-compose config`命令查看解析后的配置，确认环境变量是否正确加载
- 对于前端环境变量，确保使用正确的前缀（如Vite项目使用`VITE_`前缀）
