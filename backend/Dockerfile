FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖 (包括 PostgreSQL 和 PostGIS 的客户端库)
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt /app/

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . /app/

# 创建图像存储目录
RUN mkdir -p /app/images

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["uvicorn", "fastapi_app.main:app", "--host", "0.0.0.0", "--port", "8000"] 