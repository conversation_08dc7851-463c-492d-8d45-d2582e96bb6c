FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖 (包括 PostgreSQL 和 PostGIS 的客户端库)
# 完全替换为阿里云 Debian 源，取消官方源
RUN rm -rf /etc/apt/sources.list.d/* && cat > /etc/apt/sources.list <<EOF
deb http://mirrors.aliyun.com/debian/ bookworm main contrib non-free
deb http://mirrors.aliyun.com/debian/ bookworm-updates main contrib non-free
deb http://mirrors.aliyun.com/debian/ bookworm-backports main contrib non-free
deb http://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free
EOF

RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt /app/

# 安装 Python 依赖
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ -r requirements.txt

# 复制应用代码
COPY . /app/

# 创建图像存储目录
RUN mkdir -p /app/images

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["uvicorn", "fastapi_app.main:app", "--host", "0.0.0.0", "--port", "8000"]