# 浮标监测系统后端

## API接口文档

### 浮标位置历史数据

获取浮标位置历史数据，包含经纬度和时间戳信息。

**URL**: `/api/v1/buoys/{buoy_id}/locations`

**方法**: `GET`

**URL参数**:
- `buoy_id`: 浮标ID (必需)

**查询参数**:
- `limit`: 最大返回记录数，默认100，范围1-1000 (可选)
- `start_time`: 开始时间，ISO格式 (可选)
- `end_time`: 结束时间，ISO格式 (可选)

**成功响应示例**:

```json
[
  {
    "longitude": 120.123456,
    "latitude": 30.654321,
    "timestamp": "2023-01-01T12:00:00"
  },
  {
    "longitude": 120.123789,
    "latitude": 30.654789,
    "timestamp": "2023-01-01T12:10:00"
  }
]
```

**错误响应**:
- 404 Not Found: 浮标不存在
- 401 Unauthorized: 未授权访问 