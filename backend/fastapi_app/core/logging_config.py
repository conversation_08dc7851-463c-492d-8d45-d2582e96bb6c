import logging
import os
import sys
from logging.config import dictConfig
from typing import Dict, Any

# 获取环境变量中的日志级别，默认为INFO
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO").upper()
# 获取环境变量中的环境类型，默认为production
ENV = os.environ.get("ENV", "production").lower()

# 判断是否为调试环境
IS_DEBUG = LOG_LEVEL == "DEBUG" or ENV == "development"

# 日志配置
def configure_logging():
    """配置应用的日志系统"""
    # 基本格式 - 简洁明了
    default_format = "%(asctime)s - %(levelname)s - %(message)s"
    # 详细格式 - 仅在调试模式下使用，包含更多信息
    detailed_format = "%(asctime)s - %(levelname)s - [%(name)s] - %(message)s"

    # 根据环境选择格式
    log_format = detailed_format if IS_DEBUG else default_format

    # 设置SQLAlchemy日志级别
    sqlalchemy_level = "WARNING"
    if IS_DEBUG:
        sqlalchemy_level = "INFO"  # 在调试模式下使用INFO而不是DEBUG，减少SQL日志

    # 创建日志配置
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": log_format,
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "access": {
                "format": "%(asctime)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "default",
                "level": LOG_LEVEL,
                "stream": sys.stdout,
            },
            "access": {
                "class": "logging.StreamHandler",
                "formatter": "access",
                "level": "INFO",
                "stream": sys.stdout,
            },
        },
        "loggers": {
            "": {  # 根日志记录器
                "handlers": ["console"],
                "level": LOG_LEVEL,
            },
            "fastapi_app": {
                "handlers": ["console"],
                "level": LOG_LEVEL,
                "propagate": False,
            },
            "fastapi_app.api": {
                "handlers": ["console"],
                "level": LOG_LEVEL,
                "propagate": False,
            },
            "fastapi_app.services": {
                "handlers": ["console"],
                "level": LOG_LEVEL,
                "propagate": False,
            },
            "fastapi_app.db": {
                "handlers": ["console"],
                "level": LOG_LEVEL,
                "propagate": False,
            },
            "sqlalchemy": {
                "handlers": ["console"],
                "level": sqlalchemy_level,
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "handlers": ["console"],
                "level": sqlalchemy_level,
                "propagate": False,
            },
            "uvicorn.access": {
                "handlers": ["access"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }

    # 应用日志配置
    dictConfig(log_config)

    # 输出当前日志级别和环境
    logging.info(f"日志级别: {LOG_LEVEL}, 环境: {ENV}")

# 调试日志装饰器
def debug_log(func):
    """装饰器：记录函数的调用和返回，但简化输出内容"""
    logger = logging.getLogger(func.__module__)

    async def async_wrapper(*args, **kwargs):
        if not IS_DEBUG:
            return await func(*args, **kwargs)

        # 简化参数记录，只记录函数名和参数数量
        arg_count = len(args) + len(kwargs)
        logger.debug(f"调用 {func.__name__} (参数: {arg_count})")

        try:
            start_time = time.time()
            result = await func(*args, **kwargs)
            elapsed = time.time() - start_time

            # 简化返回值记录，不记录具体内容，只记录执行时间
            logger.debug(f"{func.__name__} 完成 (耗时: {elapsed:.3f}秒)")
            return result
        except Exception as e:
            logger.exception(f"{func.__name__} 异常: {str(e)}")
            raise

    def sync_wrapper(*args, **kwargs):
        if not IS_DEBUG:
            return func(*args, **kwargs)

        # 简化参数记录，只记录函数名和参数数量
        arg_count = len(args) + len(kwargs)
        logger.debug(f"调用 {func.__name__} (参数: {arg_count})")

        try:
            start_time = time.time()
            result = func(*args, **kwargs)
            elapsed = time.time() - start_time

            # 简化返回值记录，不记录具体内容，只记录执行时间
            logger.debug(f"{func.__name__} 完成 (耗时: {elapsed:.3f}秒)")
            return result
        except Exception as e:
            logger.exception(f"{func.__name__} 异常: {str(e)}")
            raise

    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper

# 添加缺少的导入
import asyncio
import time
