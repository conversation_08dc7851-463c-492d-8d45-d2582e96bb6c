import os
from typing import Optional, Dict, Any, List

from pydantic import PostgresDsn, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "智能浮标互动体验平台"

    # SECURITY
    SECRET_KEY: str = os.environ.get("SECRET_KEY", "development_secret_key")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000", "http://frontend:3000"]

    # DATABASE
    POSTGRES_SERVER: str = os.environ.get("POSTGRES_SERVER", "db")
    POSTGRES_USER: str = os.environ.get("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.environ.get("POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB: str = os.environ.get("POSTGRES_DB", "buoydb")
    POSTGRES_PORT: str = os.environ.get("POSTGRES_PORT", "5432")
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            username=values.data.get("POSTGRES_USER"),
            password=values.data.get("POSTGRES_PASSWORD"),
            host=values.data.get("POSTGRES_SERVER"),
            port=int(values.data.get("POSTGRES_PORT", 5432)),
            path=f"{values.data.get('POSTGRES_DB', '')}"
        )

    # 限流与缓存参数（可由管理端动态调整）
    RATE_LIMIT_IP: str = os.environ.get("RATE_LIMIT_IP", "30/minute")  # 单IP限流
    RATE_LIMIT_USER: str = os.environ.get("RATE_LIMIT_USER", "20/minute")  # 单用户限流
    RATE_LIMIT_GLOBAL: str = os.environ.get("RATE_LIMIT_GLOBAL", "100/minute")  # 全局QPS
    ANALYSIS_CACHE_TTL: int = int(os.environ.get("ANALYSIS_CACHE_TTL", 3600))  # 分析结果缓存秒数，默认1小时
    ANALYSIS_CACHE_MAX_TTL: int = int(os.environ.get("ANALYSIS_CACHE_MAX_TTL", 86400))  # 最长1天

    # 监控与告警阈值
    ALERT_LLM_FAIL_COUNT: int = int(os.environ.get("ALERT_LLM_FAIL_COUNT", 5))  # 连续失败阈值
    ALERT_QPS_THRESHOLD: int = int(os.environ.get("ALERT_QPS_THRESHOLD", 80))  # QPS告警阈值

    # MQTT
    MQTT_BROKER_HOST: str = os.environ.get("MQTT_BROKER_HOST", "mqtt")
    MQTT_BROKER_PORT: int = int(os.environ.get("MQTT_BROKER_PORT", 1883))
    MQTT_CLIENT_ID: str = os.environ.get("MQTT_CLIENT_ID", "backend-client")
    MQTT_USERNAME: str = os.environ.get("MQTT_USERNAME", "")
    MQTT_PASSWORD: str = os.environ.get("MQTT_PASSWORD", "")
    MQTT_USE_TLS: bool = os.environ.get("MQTT_USE_TLS", "False").lower() == "true"
    MQTT_CA_CERT_PATH: str = os.environ.get("MQTT_CA_CERT_PATH", "/app/certs/mqtt-ca.crt")

    # LLM API
    DEEPSEEK_API_KEY: str = os.environ.get("DEEPSEEK_API_KEY", "")
    DEEPSEEK_API_ENDPOINT: str = os.environ.get("DEEPSEEK_API_ENDPOINT", "https://api.deepseek.com/v1")

    # 图像存储
    IMAGE_STORAGE_PATH: str = os.environ.get("IMAGE_STORAGE_PATH", "/app/images")

    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()