from pathlib import Path
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

from fastapi_app.api.api_v1.api import api_router
from fastapi_app.core.config import settings
from fastapi_app.core.logging_config import configure_logging, IS_DEBUG
from fastapi_app.services.mqtt_service import mqtt_service
from fastapi_app.services.websocket_service import websocket_service
from fastapi_app.services.heartbeat_monitor_service import heartbeat_monitor_service
from fastapi_app.db.init_db import create_tables

# 配置日志
configure_logging()
logger = logging.getLogger(__name__)

# 确保图像目录存在
image_path = Path(settings.IMAGE_STORAGE_PATH)
image_path.mkdir(parents=True, exist_ok=True)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时，初始化数据库并连接到MQTT Broker
    await create_tables()
    await mqtt_service.connect()
    # 调用 MQTTService 中的方法来设置订阅
    await mqtt_service.setup_subscriptions()
    # 启动心跳监控服务
    await heartbeat_monitor_service.start()
    logger.info("心跳监控服务已启动")

    yield

    # 关闭时，先停止心跳监控服务
    await heartbeat_monitor_service.stop()
    logger.info("心跳监控服务已停止")
    # 断开MQTT连接
    await mqtt_service.disconnect()


app = FastAPI(
    title=settings.PROJECT_NAME,
    lifespan=lifespan,
)

# 设置CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    return {"message": "欢迎使用智能浮标互动体验平台 API"}


# WebSocket路由
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    client_info = getattr(websocket, "client", None)
    client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"
    if IS_DEBUG:
        logger.debug(f"新的WebSocket连接请求: {client_addr}")

    try:
        await websocket.accept()
        if IS_DEBUG:
            logger.debug(f"WebSocket连接已接受: {client_addr}")
        await websocket_service.connect(websocket)

        # 记录当前活跃连接和主题情况
        if IS_DEBUG:
            logger.debug(f"[WebSocket调试] 当前活跃连接数: {len(websocket_service.active_connections)}")
            logger.debug(f"[WebSocket调试] 当前主题订阅情况: {[f'{t}({len(conns)})' for t, conns in websocket_service.topics.items()]}")

        try:
            while True:
                data = await websocket.receive_text()
                # 委托给websocket_service处理客户端消息
                should_continue = await websocket_service.handle_client_message(websocket, data)
                if not should_continue:
                    # 客户端请求断开连接
                    break
        except WebSocketDisconnect:
            if IS_DEBUG:
                logger.debug(f"WebSocket客户端断开连接: {client_addr}")
        except Exception as e:
            logger.error(f"WebSocket连接发生异常: {str(e)}")
            if IS_DEBUG:
                logger.debug(f"[WebSocket调试] 连接异常详情: {e}", exc_info=True)
    except Exception as e:
        logger.error(f"接受WebSocket连接时出错: {str(e)}")
    finally:
        if IS_DEBUG:
            logger.debug(f"清理WebSocket连接: {client_addr}")
        await websocket_service.disconnect(websocket)
        if IS_DEBUG:
            logger.debug(f"[WebSocket调试] 断开连接后，当前活跃连接数: {len(websocket_service.active_connections)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("fastapi_app.main:app", host="0.0.0.0", port=8000, reload=True)