import sys
import requests
from core.config import settings

def test_deepseek_llm():
    url = settings.DEEPSEEK_API_ENDPOINT.rstrip("/") + "/chat/completions"
    headers = {
        "Authorization": f"Bearer {settings.DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }
    data = {
        "model": "deepseek-chat",  # 如有具体模型名请替换
        "messages": [
            {"role": "user", "content": "你好，deepseek！"}
        ]
    }
    try:
        resp = requests.post(url, headers=headers, json=data, timeout=10)
        resp.raise_for_status()
        print("API 响应：", resp.json())
    except Exception as e:
        print("API 调用失败：", e, file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    test_deepseek_llm()