from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.ext.asyncio import AsyncSession

from fastapi_app.db.base import get_db

from fastapi_app.services.quiz_service import get_questions, submit_answer
from fastapi_app.schemas.quiz import QuizQuestionList

router = APIRouter()

@router.get("/questions", response_model=QuizQuestionList)
async def get_quiz_questions(
    limit: int = 5,
    offset: int = 0,
    random: bool = False,
    db: AsyncSession = Depends(get_db)
):
    """
    获取知识问答题目列表，支持分页和随机化。
    
    参数:
    - limit: 返回的题目数量，默认值为 5。
    - offset: 从第几条记录开始返回，用于分页，默认值为 0。
    - random: 是否随机返回题目列表，默认值为 False。如果为 True，则忽略 offset 参数。
    """
    try:
        questions, total = await get_questions(db, limit=limit, offset=offset, random=random)
        return QuizQuestionList(
            questions=questions,
            total=total,
            limit=limit,
            offset=offset,
            random=random
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.post("/submit")
async def submit_quiz_answer(
    data: dict = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """
    提交知识问答答案。
    
    参数:
    - data: 包含 questionId 和 selectedOption 的字典。
    """
    try:
        question_id = data.get("questionId")
        selected_option = data.get("selectedOption")
        if not question_id or not selected_option:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="缺少必要的参数")
        result = await submit_answer(db, question_id, selected_option)
        return result
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))