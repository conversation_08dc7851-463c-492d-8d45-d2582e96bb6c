from fastapi import APIRouter, Depends, Body, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import logging
from datetime import datetime, timezone
import json
from typing import Dict, Any, Optional

from fastapi_app.db.base import get_db
from fastapi_app.services.websocket_service import websocket_service
from fastapi_app.services.mqtt_service import mqtt_service
from fastapi_app.models.buoy_model import Buoy as BuoyModel

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/websocket-status")
async def get_websocket_status():
    """获取WebSocket连接和订阅状态"""
    active_connections = len(websocket_service.active_connections)
    topics = {topic: len(connections) for topic, connections in websocket_service.topics.items()}

    return {
        "active_connections": active_connections,
        "topics": topics
    }

@router.post("/simulate-mqtt-message")
async def simulate_mqtt_message(
    buoy_id: int,
    data_type: str = Body(..., description="数据类型，如temperature, humidity, location等"),
    value: Any = Body(..., description="传感器值或位置数据"),
    unit: Optional[str] = Body(None, description="单位（可选）"),
    db: AsyncSession = Depends(get_db)
):
    """
    模拟MQTT消息，用于测试WebSocket广播功能

    - 对于普通传感器数据，value应为数值
    - 对于位置数据，value应为包含longitude和latitude的对象
    """
    logger.info(f"模拟MQTT消息: buoy_id={buoy_id}, data_type={data_type}, value={value}, unit={unit}")

    # 检查浮标是否存在
    buoy = await db.get(BuoyModel, buoy_id)
    if not buoy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"浮标ID {buoy_id} 不存在"
        )

    # 构造MQTT主题和负载
    topic = f"buoy/data/{buoy_id}/{data_type}"
    payload = {
        "buoy_id": buoy_id,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "data_type": data_type,
        "value": value,
        "unit": unit
    }

    # 直接调用MQTT消息处理函数
    await mqtt_service._handle_buoy_data(topic, payload)

    return {
        "message": "模拟MQTT消息已处理",
        "topic": topic,
        "payload": payload
    }

@router.post("/broadcast-websocket")
async def broadcast_websocket(
    topic: str = Body(..., description="WebSocket主题"),
    message: Dict[str, Any] = Body(..., description="要广播的消息")
):
    """
    直接广播WebSocket消息到指定主题
    """
    logger.info(f"直接广播WebSocket消息: topic={topic}, message={json.dumps(message)}")

    # 直接调用WebSocket广播函数
    await websocket_service.broadcast(topic, message)

    return {
        "message": "WebSocket消息已广播",
        "topic": topic,
        "payload": message
    }
