from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi_app.schemas.analysis import AnalysisReportRequest, AnalysisReportResponse
from fastapi_app.services.llm_service import LLMService, LLMServiceException
from fastapi_app.api.api_v1.endpoints.auth import get_current_user
from fastapi_app.services.analysis_guard import analysis_guard, set_cache_result
from fastapi_app.services.local_cache_service import LocalCacheService
from fastapi_app.core.config import Settings
import logging
import json

router = APIRouter()
settings = Settings()
logger = logging.getLogger("analysis_report")

@router.post("/report", response_model=AnalysisReportResponse)
async def analysis_report(
    req: AnalysisReportRequest,
    request: Request,
    current_user=Depends(get_current_user),
    guard_result=Depends(analysis_guard)
):
    """
    智能分析报告接口：仅登录用户可用，带限流、缓存、监控、降级等防护
    """
    # 记录请求数据
    try:
        req_dict = req.model_dump()
        logger.info(f"收到分析请求: {json.dumps(req_dict, ensure_ascii=False)}")
    except Exception as e:
        logger.error(f"记录请求数据失败: {str(e)}")
        logger.error(f"原始请求数据: {await request.body()}")

    # 1. 缓存命中直接返回
    cached, cache_key = guard_result
    if cached:
        logger.info(f"analysis_report: cache hit for user {getattr(current_user, 'id', 'anonymous')}")
        try:
            # 将缓存的 JSON 字符串解析为 AnalysisReportResponse 对象
            return AnalysisReportResponse.model_validate_json(cached)
        except Exception as e:
            logger.error(f"解析缓存数据失败: {str(e)}")
            # 如果缓存数据解析失败，继续执行新的分析

    llm_service = LLMService()
    # 2. 监控计数
    await LocalCacheService.incr("monitor:analysis_report:total")
    await LocalCacheService.incr("monitor:analysis_report:llm_call")

    # 3. LLM 调用失败计数与降级
    fail_key = f"monitor:analysis_report:llm_fail:{getattr(current_user, 'id', 'anonymous')}"
    try:
        result = await llm_service.analyze_report(req)
        # 4. 写入缓存
        await set_cache_result(cache_key, result, settings.ANALYSIS_CACHE_TTL)
        # 5. 成功清除失败计数
        await LocalCacheService.delete(fail_key)
        return result
    except LLMServiceException as e:
        # 6. 失败计数+告警
        fail_count = await LocalCacheService.incr(fail_key)
        await LocalCacheService.expire(fail_key, 3600)
        logger.error(f"LLM 调用失败: {e}, user={getattr(current_user, 'id', 'anonymous')}, count={fail_count}")
        if fail_count >= settings.ALERT_LLM_FAIL_COUNT:
            # 触发告警（可扩展为钩子/第三方）
            logger.warning(f"LLM 连续失败超阈值，user={getattr(current_user, 'id', 'anonymous')}, count={fail_count}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="分析服务暂时不可用，请稍后再试（已自动告警）"
            )
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"分析服务异常: {e}"
        )