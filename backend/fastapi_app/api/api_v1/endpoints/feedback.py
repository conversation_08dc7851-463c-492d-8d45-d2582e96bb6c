from typing import Any, List, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

from fastapi_app.api.api_v1.endpoints.auth import get_current_active_user, get_current_user_optional
from fastapi_app.db.base import get_db
from fastapi_app.models.user_model import User
from fastapi_app.models.additional_model import Feedback
from fastapi_app.schemas.feedback import (
    FeedbackCreate, 
    FeedbackUpdate, 
    Feedback as FeedbackSchema,
    FeedbackList,
    FeedbackStats,
    FeedbackType,
    FeedbackStatus
)

router = APIRouter()


@router.post("/", response_model=FeedbackSchema)
async def create_feedback(
    feedback_data: FeedbackCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> Any:
    """提交反馈（支持匿名和登录用户）"""
    
    # 创建反馈记录
    feedback = Feedback(
        user_id=current_user.id if current_user else None,
        contact=feedback_data.contact,
        type=feedback_data.type,
        content=feedback_data.content,
        status=FeedbackStatus.pending,
    )
    
    db.add(feedback)
    await db.commit()
    await db.refresh(feedback)
    
    return feedback


@router.get("/my", response_model=FeedbackList)
async def get_my_feedback(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[FeedbackStatus] = Query(None, description="状态筛选"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """获取当前用户的反馈列表"""
    
    # 构建查询条件
    conditions = [Feedback.user_id == current_user.id]
    if status:
        conditions.append(Feedback.status == status)
    
    # 查询总数
    count_query = select(func.count(Feedback.id)).where(and_(*conditions))
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 查询数据
    offset = (page - 1) * size
    query = (
        select(Feedback)
        .where(and_(*conditions))
        .order_by(Feedback.created_at.desc())
        .offset(offset)
        .limit(size)
    )
    
    result = await db.execute(query)
    feedbacks = result.scalars().all()
    
    return FeedbackList(
        items=feedbacks,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/", response_model=FeedbackList)
async def get_all_feedback(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[FeedbackStatus] = Query(None, description="状态筛选"),
    type: Optional[FeedbackType] = Query(None, description="类型筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """获取所有反馈列表（仅管理员）"""
    
    # 检查管理员权限
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限",
        )
    
    # 构建查询条件
    conditions = []
    if status:
        conditions.append(Feedback.status == status)
    if type:
        conditions.append(Feedback.type == type)
    if search:
        conditions.append(
            or_(
                Feedback.content.ilike(f"%{search}%"),
                Feedback.contact.ilike(f"%{search}%"),
                Feedback.reply.ilike(f"%{search}%")
            )
        )
    
    # 查询总数
    count_query = select(func.count(Feedback.id))
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 查询数据
    offset = (page - 1) * size
    query = select(Feedback).options(selectinload(Feedback.user))
    if conditions:
        query = query.where(and_(*conditions))
    
    query = query.order_by(Feedback.created_at.desc()).offset(offset).limit(size)
    
    result = await db.execute(query)
    feedbacks = result.scalars().all()
    
    return FeedbackList(
        items=feedbacks,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.put("/{feedback_id}", response_model=FeedbackSchema)
async def update_feedback(
    feedback_id: int,
    feedback_data: FeedbackUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """更新反馈（仅管理员）"""
    
    # 检查管理员权限
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限",
        )
    
    # 查询反馈
    query = select(Feedback).where(Feedback.id == feedback_id)
    result = await db.execute(query)
    feedback = result.scalar_one_or_none()
    
    if not feedback:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="反馈不存在",
        )
    
    # 更新反馈
    update_data = feedback_data.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(feedback, key, value)
    
    feedback.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(feedback)
    
    return feedback


@router.get("/stats", response_model=FeedbackStats)
async def get_feedback_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """获取反馈统计信息（仅管理员）"""
    
    # 检查管理员权限
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限",
        )
    
    # 统计总数
    total_query = select(func.count(Feedback.id))
    total_result = await db.execute(total_query)
    total = total_result.scalar()
    
    # 按状态统计
    status_query = (
        select(Feedback.status, func.count(Feedback.id))
        .group_by(Feedback.status)
    )
    status_result = await db.execute(status_query)
    status_counts = dict(status_result.all())
    
    # 按类型统计
    type_query = (
        select(Feedback.type, func.count(Feedback.id))
        .group_by(Feedback.type)
    )
    type_result = await db.execute(type_query)
    type_counts = dict(type_result.all())
    
    return FeedbackStats(
        total=total,
        pending=status_counts.get("pending", 0),
        processing=status_counts.get("processing", 0),
        resolved=status_counts.get("resolved", 0),
        by_type=type_counts
    )
