from typing import Any
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from fastapi_app.api.api_v1.endpoints.auth import get_current_active_user
from fastapi_app.db.base import get_db
from fastapi_app.models.user_model import User
from fastapi_app.models.additional_model import UserSettings
from fastapi_app.schemas.settings import UserSettings as UserSettingsSchema, UserSettingsUpdate

router = APIRouter()


@router.get("", response_model=UserSettingsSchema)
async def get_user_settings(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """获取当前用户的设置"""
    # 查询用户设置
    result = await db.execute(
        select(UserSettings).where(UserSettings.user_id == current_user.id)
    )
    settings = result.scalar_one_or_none()

    # 如果用户还没有设置，则创建默认设置
    if not settings:
        settings = UserSettings(user_id=current_user.id)
        db.add(settings)
        await db.commit()
        await db.refresh(settings)

    return settings


@router.put("", response_model=UserSettingsSchema)
async def update_user_settings(
    settings_update: UserSettingsUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """更新当前用户的设置"""
    # 查询用户设置
    result = await db.execute(
        select(UserSettings).where(UserSettings.user_id == current_user.id)
    )
    settings = result.scalar_one_or_none()

    update_data = settings_update.model_dump(exclude_unset=True)

    if settings:
        # 更新设置
        update_data["updated_at"] = datetime.now(timezone.utc)  # 使用带UTC时区的datetime对象
        await db.execute(
            update(UserSettings)
            .where(UserSettings.user_id == current_user.id)
            .values(**update_data)
        )
        await db.commit()
        await db.refresh(settings)
    else:
        # 创建新设置
        settings = UserSettings(user_id=current_user.id, **update_data)
        db.add(settings)
        await db.commit()
        await db.refresh(settings)

    return settings