from typing import Any, List
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from fastapi_app.api.api_v1.endpoints.auth import get_current_active_user
from fastapi_app.db.base import get_db
from fastapi_app.models.user_model import User
from fastapi_app.services.security import verify_password, get_password_hash
from fastapi_app.schemas.user import User as UserSchema, UserUpdate, PasswordChange

router = APIRouter()


@router.get("/me", response_model=UserSchema)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """获取当前登录用户信息"""
    return current_user


@router.put("/me", response_model=UserSchema)
async def update_user_info(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """更新当前用户信息"""
    update_data = user_update.model_dump(exclude_unset=True)
    if update_data.get("password"):
        hashed_password = get_password_hash(update_data["password"])
        del update_data["password"]
        update_data["hashed_password"] = hashed_password

    # 检查用户名是否已被其他用户使用
    if update_data.get("username") and update_data["username"] != current_user.username:
        result = await db.execute(
            select(User).where(User.username == update_data["username"])
        )
        existing_user = result.scalar_one_or_none()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在",
            )

    # 检查邮箱是否已被其他用户使用
    if update_data.get("email") and update_data["email"] != current_user.email:
        result = await db.execute(
            select(User).where(User.email == update_data["email"])
        )
        existing_user = result.scalar_one_or_none()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 更新用户信息
    update_data["updated_at"] = datetime.now(timezone.utc)  # 使用带UTC时区的datetime对象
    await db.execute(
        update(User).
        where(User.id == current_user.id).
        values(**update_data)
    )
    await db.commit()
    await db.refresh(current_user)

    return current_user


@router.post("/me/password", response_model=dict)
async def change_password(
    password_change: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """修改当前用户密码"""
    # 验证当前密码
    if not verify_password(password_change.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码不正确",
        )

    # 更新密码
    hashed_password = get_password_hash(password_change.new_password)
    await db.execute(
        update(User).
        where(User.id == current_user.id).
        values(
            hashed_password=hashed_password,
            updated_at=datetime.now(timezone.utc)  # 使用带UTC时区的datetime对象
        )
    )
    await db.commit()

    return {"message": "密码修改成功"}