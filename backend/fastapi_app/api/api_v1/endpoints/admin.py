from typing import Any, List, Optional
from datetime import datetime, timezone, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, delete, text
from sqlalchemy.sql import text as sql_text

from fastapi_app.api.api_v1.endpoints.auth import get_current_active_user
from fastapi_app.db.base import get_db
from fastapi_app.models.user_model import User
from fastapi_app.models.buoy_model import Buoy, Image, BuoyThreshold
from fastapi_app.models.sensor_data_model import SensorData
from fastapi_app.models.additional_model import (
    ControlLog, Notification,
    UserSettings, Feedback
)
from fastapi_app.models.quiz_model import QuizQuestion, QuizOption, QuizAttempt
from fastapi_app.schemas.user import User as UserSchema, UserCreate, UserUpdate
from fastapi_app.services.auth_service import create_user, get_user_by_id
from fastapi_app.services.security import get_password_hash

router = APIRouter()


# 检查是否为管理员的依赖函数
async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """检查当前用户是否为管理员"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限",
        )
    return current_user


@router.get("/users", response_model=List[UserSchema])
async def get_all_users(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """获取所有用户列表（仅管理员）"""
    result = await db.execute(
        select(User).offset(skip).limit(limit)
    )
    users = result.scalars().all()
    return users


@router.post("/users", response_model=UserSchema)
async def create_new_user(
    user_data: UserCreate,
    role: str = "user",
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """创建新用户（仅管理员）"""
    # 创建用户
    user = await create_user(db, user_data.username, user_data.email, user_data.password, role)
    return user


@router.put("/users/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """更新用户信息（仅管理员）"""
    user = await get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )
    
    # 更新用户信息
    update_data = user_data.model_dump(exclude_unset=True)
    
    # 如果包含密码，需要哈希处理
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))
    
    # 更新时间戳
    update_data["updated_at"] = datetime.now(timezone.utc)
    
    # 更新用户
    for key, value in update_data.items():
        setattr(user, key, value)
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    return user


@router.delete("/users/{user_id}", response_model=dict)
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """删除用户（仅管理员）"""
    # 不允许删除自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除当前登录的管理员账户",
        )
    
    user = await get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )
    
    await db.delete(user)
    await db.commit()
    
    return {"message": f"用户 {user.username} 已成功删除"}


@router.get("/stats", response_model=dict)
async def get_database_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """获取数据库统计信息（仅管理员）"""
    # 获取各表的记录数
    async def get_count(model):
        result = await db.execute(select(func.count()).select_from(model))
        return result.scalar()
    
    stats = {
        "users": await get_count(User),
        "buoys": await get_count(Buoy),
        "sensor_data": await get_count(SensorData),
        "images": await get_count(Image),
        "control_logs": await get_count(ControlLog),
        "notifications": await get_count(Notification),
        "quiz_questions": await get_count(QuizQuestion),
        "quiz_attempts": await get_count(QuizAttempt),
        "buoy_thresholds": await get_count(BuoyThreshold),
        "feedback": await get_count(Feedback),
    }
    
    # 获取最近一周的传感器数据量
    one_week_ago = datetime.now(timezone.utc) - timedelta(days=7)
    result = await db.execute(
        select(func.count()).select_from(SensorData).where(SensorData.timestamp >= one_week_ago)
    )
    stats["sensor_data_last_7_days"] = result.scalar()
    
    # 获取数据库大小信息（PostgreSQL特有）
    try:
        # 获取当前数据库名称
        db_name_query = "SELECT current_database()"
        result = await db.execute(text(db_name_query))
        db_name = result.scalar()
        
        # 获取数据库大小
        size_query = f"""
        SELECT pg_size_pretty(pg_database_size('{db_name}')) as db_size,
               pg_size_pretty(pg_total_relation_size('sensor_data')) as sensor_data_size
        """
        result = await db.execute(text(size_query))
        size_info = result.mappings().one()
        
        stats["database_size"] = size_info["db_size"]
        stats["sensor_data_table_size"] = size_info["sensor_data_size"]
    except Exception as e:
        stats["database_size_error"] = str(e)
    
    return stats


@router.delete("/cleanup/sensor-data", response_model=dict)
async def cleanup_old_sensor_data(
    days: int = Query(30, ge=7, description="要保留的最近天数，最小为7天"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """清理指定天数前的传感器数据（仅管理员）"""
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # 执行删除操作
    result = await db.execute(
        delete(SensorData).where(SensorData.timestamp < cutoff_date)
    )
    
    # 获取删除的行数
    deleted_count = result.rowcount
    
    await db.commit()
    
    return {
        "message": f"已成功清理 {deleted_count} 条 {days} 天前的传感器数据",
        "deleted_count": deleted_count,
        "cutoff_date": cutoff_date.isoformat()
    }


@router.delete("/cleanup/notifications", response_model=dict)
async def cleanup_old_notifications(
    days: int = Query(30, ge=7, description="要保留的最近天数，最小为7天"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """清理指定天数前的通知数据（仅管理员）"""
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # 执行删除操作
    result = await db.execute(
        delete(Notification).where(Notification.timestamp < cutoff_date)
    )
    
    # 获取删除的行数
    deleted_count = result.rowcount
    
    await db.commit()
    
    return {
        "message": f"已成功清理 {deleted_count} 条 {days} 天前的通知",
        "deleted_count": deleted_count,
        "cutoff_date": cutoff_date.isoformat()
    }


@router.delete("/cleanup/control-logs", response_model=dict)
async def cleanup_old_control_logs(
    days: int = Query(30, ge=7, description="要保留的最近天数，最小为7天"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """清理指定天数前的控制日志（仅管理员）"""
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # 执行删除操作
    result = await db.execute(
        delete(ControlLog).where(ControlLog.timestamp < cutoff_date)
    )
    
    # 获取删除的行数
    deleted_count = result.rowcount
    
    await db.commit()
    
    return {
        "message": f"已成功清理 {deleted_count} 条 {days} 天前的控制日志",
        "deleted_count": deleted_count,
        "cutoff_date": cutoff_date.isoformat()
    }
