from typing import List, Optional
from datetime import datetime, timedelta, timezone
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, text

from fastapi_app.db.base import get_db
from fastapi_app.models.buoy_model import Buoy as BuoyModel
from fastapi_app.models.sensor_data_model import SensorData as SensorDataModel
from fastapi_app.schemas.buoy import SensorData, GeoPoint, GeoPointWithTimestamp
from fastapi_app.api.api_v1.endpoints.auth import get_current_active_user
from fastapi_app.models.user_model import User
from fastapi_app.services.data_service import get_sensor_data, get_location_history

# 创建路由器
router = APIRouter()

@router.get("/buoys/{buoy_id}/sensor", response_model=List[SensorData])
async def get_buoy_sensor_data(
    buoy_id: int,
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取特定浮标的传感器数据
    """
    return await get_sensor_data(buoy_id, limit, db)

@router.get("/buoys/{buoy_id}/locations", response_model=List[GeoPointWithTimestamp])
async def get_buoy_location_history(
    buoy_id: int,
    limit: int = Query(100, ge=1, le=1000),
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取特定浮标的位置历史数据，包含时间戳信息

    - **buoy_id**: 浮标ID
    - **limit**: 最大返回记录数
    - **start_time**: 可选，开始时间
    - **end_time**: 可选，结束时间
    """
    return await get_location_history(buoy_id, limit, db, start_time, end_time)