from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Union, Tuple, Literal
from pydantic import BaseModel, Field, field_validator
from typing_extensions import Annotated


# 基础模型类，用于处理时间戳字段
class UTCDateTimeModel(BaseModel):
    """基础模型类，确保所有datetime字段以ISO UTC格式序列化"""

    model_config = {
        "json_encoders": {
            # 确保datetime字段以ISO UTC格式序列化
            datetime: lambda dt: dt.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if dt.tzinfo is None else dt.astimezone(timezone.utc).isoformat().replace('+00:00', 'Z')
        }
    }


# 位置模式 - 统一使用 GeoPoint 作为位置数据模型
class GeoPoint(UTCDateTimeModel):
    longitude: float = Field(..., ge=-180, le=180)
    latitude: float = Field(..., ge=-90, le=90)
    timestamp: Optional[datetime] = None


# 支持带时间戳的位置历史数据
class GeoPointWithTimestamp(GeoPoint):
    timestamp: datetime


# 用于API响应的浮标模型
class Buoy(UTCDateTimeModel):
    name: str
    description: Optional[str] = None
    location: GeoPoint
    id: int
    owner_id: Optional[int] = None
    status: str
    last_heartbeat: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }


# 用于更新浮标
class BuoyUpdate(UTCDateTimeModel):
    name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[GeoPoint] = None
    status: Optional[str] = None


# 传感器数据基础模式
class SensorDataBase(UTCDateTimeModel):
    data_type: str
    value: float
    unit: str


# 用于创建传感器数据
class SensorDataCreate(SensorDataBase):
    buoy_id: int
    timestamp: Optional[datetime] = None


# 用于API响应的传感器数据模型
class SensorData(SensorDataBase):
    id: int
    buoy_id: int
    timestamp: datetime

    model_config = {
        "from_attributes": True
    }


# 浮标控制指令
class BuoyControlCommand(UTCDateTimeModel):
    command: str = Field(...)  # 如 "set_light"
    brightness: Optional[int] = Field(None, ge=0, le=100)
    color: Optional[str] = None  # 如 "#FF8C00"

    @field_validator('color')
    def validate_color(cls, v):
        if v is not None and not v.startswith('#'):
            raise ValueError('颜色值必须是以#开头的十六进制格式')
        return v


# 阈值基础模式
class ThresholdBase(UTCDateTimeModel):
    data_type: str
    warning_min: Optional[float] = None
    warning_max: Optional[float] = None
    critical_min: Optional[float] = None
    critical_max: Optional[float] = None


# 用于创建阈值
class ThresholdCreate(ThresholdBase):
    buoy_id: int


# 用于更新阈值
class ThresholdUpdate(UTCDateTimeModel):
    warning_min: Optional[float] = None
    warning_max: Optional[float] = None
    critical_min: Optional[float] = None
    critical_max: Optional[float] = None


# 用于API响应的阈值模型
class Threshold(ThresholdBase):
    id: int
    buoy_id: int
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

    def get_value(self) -> Any:
        """根据 data_type 返回正确的值 (value 或 location_value)"""
        if self.data_type == 'location' and self.location_value:
            # 假设 location_value 是 WKTElement 或类似对象，需要提取坐标
            # 这里简化处理，实际可能需要更复杂的解析
            # 注意：Pydantic v2 可能需要不同的方式处理 geometry 类型
            wkt = self.location_value.wkt if hasattr(self.location_value, 'wkt') else str(self.location_value)
            coords = wkt.replace("POINT(", "").replace(")", "").split()
            if len(coords) >= 2:
                return {"longitude": float(coords[0]), "latitude": float(coords[1])}
            return None
        return self.value


# 控制指令的数据模型
class ControlCommand(UTCDateTimeModel):
    command: str
    brightness: Optional[int] = Field(None, ge=0, le=100)
    color: Optional[str] = None

    @field_validator('color')
    def validate_color_format(cls, v):
        if v is None:
            return v
        if not isinstance(v, str) or not v.startswith('#') or len(v) != 7:
            raise ValueError("颜色必须是 # 开头的7位十六进制字符串")
        try:
            int(v[1:], 16)
        except ValueError:
            raise ValueError("无效的十六进制颜色代码")
        return v