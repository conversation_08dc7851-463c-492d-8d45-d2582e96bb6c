from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, EmailStr, Field


# 基础用户模式
class UserBase(BaseModel):
    username: str
    email: EmailStr
    bio: Optional[str] = ""
    

# 用于创建用户
class UserCreate(UserBase):
    password: str


# 用于更新用户
class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    bio: Optional[str] = None
    is_active: Optional[bool] = None


# 用于API响应的用户模型
class User(UserBase):
    id: int
    role: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 用于登录的模式
class UserLogin(BaseModel):
    username: str
    password: str


# Token模式
class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    refresh_token: Optional[str] = None


# 刷新Token模式
class RefreshToken(BaseModel):
    refresh_token: str


# Token内容模式
class TokenPayload(BaseModel):
    sub: int
    role: str
    exp: int


# 密码更改模式
class PasswordChange(BaseModel):
    current_password: str
    new_password: str 