from typing import List, Optional
from pydantic import BaseModel

class QuizOption(BaseModel):
    id: int
    text: str

    class Config:
        orm_mode = True

class QuizQuestion(BaseModel):
    id: int
    content: str
    options: List[QuizOption]
    correct_answer_id: Optional[int] = None
    category: Optional[str] = None
    difficulty: Optional[str] = None

    class Config:
        orm_mode = True

class QuizQuestionList(BaseModel):
    questions: List[QuizQuestion]
    total: int
    limit: int
    offset: int
    random: bool

    class Config:
        orm_mode = True