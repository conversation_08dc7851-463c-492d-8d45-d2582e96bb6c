from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict, field_validator

class SensorDataPoint(BaseModel):
    timestamp: str = Field(..., description="ISO8601格式的时间戳")
    value: float = Field(..., description="传感器数值")
    unit: str = Field(..., description="数值单位")

    @field_validator('timestamp')
    @classmethod
    def validate_timestamp(cls, v: str) -> str:
        try:
            # 验证时间戳格式是否正确
            datetime.fromisoformat(v.replace('Z', '+00:00'))
            return v
        except ValueError as e:
            raise ValueError(f"Invalid timestamp format: {e}")

class AnalysisRequestItem(BaseModel):
    buoy_id: int = Field(..., description="浮标ID")
    sensor_type: str = Field(..., description="传感器类型")
    data: SensorDataPoint

class AnalysisReportRequest(BaseModel):
    items: List[AnalysisRequestItem]

class Anomaly(BaseModel):
    timestamp: str
    value: float
    desc: str

class AnalysisReportResponse(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "analysis_text": "完整的分析报告文本..."
            }
        },
        json_encoders={
            str: lambda v: v.encode('utf-8').decode('utf-8')
        }
    )
    
    analysis_text: str = Field(..., description="完整的分析报告文本，包含趋势分析、异常检测和建议措施")

    def model_dump_json(self, **kwargs) -> str:
        """确保对象可以被 JSON 序列化"""
        return super().model_dump_json(**kwargs)