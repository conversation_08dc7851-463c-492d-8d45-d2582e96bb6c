from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime


class UserSettingsBase(BaseModel):
    theme: Optional[str] = "light"  # "light", "dark"
    language: Optional[str] = "zh_CN"
    notification_preferences: Optional[Dict[str, Any]] = {}


class UserSettingsCreate(UserSettingsBase):
    pass


class UserSettingsUpdate(UserSettingsBase):
    pass


class UserSettings(UserSettingsBase):
    user_id: int
    updated_at: datetime

    class Config:
        from_attributes = True 