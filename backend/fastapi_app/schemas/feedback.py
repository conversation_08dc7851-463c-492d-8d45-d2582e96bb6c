from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum


class FeedbackType(str, Enum):
    """反馈类型枚举"""
    suggestion = "suggestion"  # 建议
    problem = "problem"        # 问题
    bug = "bug"               # BUG
    other = "other"           # 其他


class FeedbackStatus(str, Enum):
    """反馈状态枚举"""
    pending = "pending"       # 未处理
    processing = "processing" # 处理中
    resolved = "resolved"     # 已处理


class FeedbackBase(BaseModel):
    """反馈基础模型"""
    contact: Optional[str] = Field(None, description="联系方式")
    type: FeedbackType = Field(FeedbackType.other, description="反馈类型")
    content: str = Field(..., min_length=1, max_length=2000, description="反馈内容")


class FeedbackCreate(FeedbackBase):
    """创建反馈请求模型"""
    pass


class FeedbackUpdate(BaseModel):
    """更新反馈模型（管理员用）"""
    status: Optional[FeedbackStatus] = Field(None, description="反馈状态")
    reply: Optional[str] = Field(None, max_length=2000, description="管理员回复")


class Feedback(FeedbackBase):
    """反馈响应模型"""
    id: int
    user_id: Optional[int] = None
    status: FeedbackStatus
    reply: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }


class FeedbackList(BaseModel):
    """反馈列表响应模型"""
    items: List[Feedback]
    total: int
    page: int = 1
    size: int = 20
    pages: int


class FeedbackStats(BaseModel):
    """反馈统计模型"""
    total: int
    pending: int
    processing: int
    resolved: int
    by_type: dict[str, int]
