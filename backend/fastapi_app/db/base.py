from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from typing import AsyncGenerator
import logging
import time
from contextlib import asynccontextmanager
import os

from fastapi_app.core.config import settings

# 获取环境变量中的日志级别和环境类型
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO").upper()
ENV = os.environ.get("ENV", "production").lower()
IS_DEBUG = LOG_LEVEL == "DEBUG" or ENV == "development"

# 配置日志
logger = logging.getLogger(__name__)

# 根据环境设置echo参数，但即使在调试模式下也不输出所有SQL
# 这样可以减少日志输出量，提高可读性
echo = False  # 默认关闭SQL回显
echo_pool = False  # 默认关闭连接池日志

engine = create_async_engine(
    str(settings.SQLALCHEMY_DATABASE_URI),
    echo=echo,
    echo_pool=echo_pool
)
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """依赖注入函数，用于在FastAPI路由中获取数据库会话"""
    # 只在DEBUG模式下记录会话时间
    session_start_time = time.time() if IS_DEBUG else 0

    async with async_session() as session:
        try:
            yield session
            await session.commit()
            # 只在DEBUG模式下记录会话时间
            if IS_DEBUG and session_start_time > 0:
                elapsed = time.time() - session_start_time
                # 只记录耗时超过100ms的会话，减少日志量
                if elapsed > 0.1:
                    logger.debug(f"数据库会话提交 (耗时: {elapsed:.3f}秒)")
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话回滚: {str(e)}")
            raise


@asynccontextmanager
async def get_db_context():
    """上下文管理器，用于非依赖注入场景获取数据库会话"""
    # 只在DEBUG模式下记录会话时间
    session_start_time = time.time() if IS_DEBUG else 0

    async with async_session() as session:
        try:
            yield session
            await session.commit()
            # 只在DEBUG模式下记录会话时间
            if IS_DEBUG and session_start_time > 0:
                elapsed = time.time() - session_start_time
                # 只记录耗时超过100ms的会话，减少日志量
                if elapsed > 0.1:
                    logger.debug(f"数据库上下文会话提交 (耗时: {elapsed:.3f}秒)")
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库上下文会话回滚: {str(e)}")
            raise