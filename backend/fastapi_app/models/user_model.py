from datetime import datetime, timezone
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Enum, Text
from sqlalchemy.orm import relationship

from fastapi_app.db.base import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    bio = Column(Text, nullable=True, default="")
    role = Column(String, default="user")  # "admin", "user"
    is_active = Column(Boolean, default=True)
    refresh_token = Column(Text, nullable=True)  # 存储刷新令牌
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    buoys = relationship("Buoy", back_populates="owner")
    settings = relationship("UserSettings", back_populates="user", uselist=False)
    notifications = relationship("Notification", back_populates="user")
    quiz_attempts = relationship("QuizAttempt", back_populates="user")
    control_logs = relationship("ControlLog", back_populates="user")
    feedback = relationship("Feedback", back_populates="user")
