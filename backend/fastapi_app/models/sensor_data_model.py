from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from geoalchemy2 import Geometry
from datetime import datetime, timezone

from fastapi_app.db.base import Base
from .buoy_model import Buoy # 更新导入路径

class SensorData(Base):
    __tablename__ = "sensor_data"

    id = Column(Integer, primary_key=True, index=True)
    buoy_id = Column(Integer, ForeignKey("buoys.id"), nullable=False, index=True)
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), index=True)
    data_type = Column(String, index=True, nullable=False) # e.g., 'temperature', 'ph', 'location', 'dissolved_oxygen'
    value = Column(Float, nullable=True) # 用于存储数值型数据
    unit = Column(String, nullable=True) # e.g., '°C', 'pH', 'mg/L', 'm'
    location_value = Column(Geometry(geometry_type='POINT', srid=4326), nullable=True) # 用于存储位置数据

    buoy = relationship("Buoy", back_populates="sensor_data")

    def __repr__(self):
        val_repr = f"value={self.value}" if self.value is not None else f"location={self.location_value}"
        return f"<SensorData(buoy_id={self.buoy_id}, type='{self.data_type}', {val_repr} @ {self.timestamp})>"

# 注意：此模型假设位置数据会存储在 location_value 字段中，
# 数值数据存储在 value 字段中。
# 如果有其他类型的数据，可以考虑添加新字段。
# 方案中提到 get_value 方法，在 SQLAlchemy 模型中通常不直接定义这种方法，
# 而是依赖于查询时选择正确的字段或在服务层处理。
