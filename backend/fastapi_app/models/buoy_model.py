from datetime import datetime, timezone
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Enum, Float
from sqlalchemy.orm import relationship
from geoalchemy2 import Geometry

from fastapi_app.db.base import Base


class Buoy(Base):
    __tablename__ = "buoys"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(Text, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    latest_location = Column(Geometry(geometry_type='POINT', srid=4326), nullable=True)
    status = Column(String, default="active")  # "active", "inactive", "error"
    last_heartbeat = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    owner = relationship("User", back_populates="buoys")
    sensor_data = relationship("SensorData", back_populates="buoy", cascade="all, delete-orphan")
    images = relationship("Image", back_populates="buoy")
    control_logs = relationship("ControlLog", back_populates="buoy")
    notifications = relationship("Notification", back_populates="buoy")
    thresholds = relationship("BuoyThreshold", back_populates="buoy")

    def __repr__(self):
        return f"<Buoy(id={self.id}, name='{self.name}', status='{self.status}')>"


class Image(Base):
    __tablename__ = "images"

    id = Column(Integer, primary_key=True, index=True)
    buoy_id = Column(Integer, ForeignKey("buoys.id"), nullable=False)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    storage_path = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    uploaded_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # 关系
    buoy = relationship("Buoy", back_populates="images")


class BuoyThreshold(Base):
    __tablename__ = "buoy_thresholds"

    id = Column(Integer, primary_key=True, index=True)
    buoy_id = Column(Integer, ForeignKey("buoys.id"), nullable=False)
    data_type = Column(String, nullable=False)
    warning_min = Column(Float, nullable=True)
    warning_max = Column(Float, nullable=True)
    critical_min = Column(Float, nullable=True)
    critical_max = Column(Float, nullable=True)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    buoy = relationship("Buoy", back_populates="thresholds") 
