import json
import logging
import time
import re
from typing import Dict, Any, Set, List, Optional, Tuple
import asyncio
from fastapi_app.core.logging_config import IS_DEBUG
from fastapi import WebSocket, HTTPException
from jose import JWTError
from fastapi_app.db.base import get_db_context
from fastapi_app.services.auth_service import validate_token
from fastapi_app.schemas.buoy import ControlCommand

from pydantic import ValidationError

logger = logging.getLogger(__name__)

class WebSocketManager:
    """
    WebSocket连接管理器，用于处理WebSocket连接和消息推送
    """
    def __init__(self):
        self.active_connections: Set = set()
        self.topics: Dict[str, Set] = {}  # topic -> set of connections
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("WebSocket管理器初始化")

    async def connect(self, websocket):
        """注册新的WebSocket连接"""
        self.active_connections.add(websocket)
        connection_id = id(websocket)
        client_info = getattr(websocket, "client", None)
        client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"

        # 获取用户信息（如果已认证）
        user_id = getattr(websocket, "user_id", None)
        username = getattr(websocket, "username", None)
        user_role = getattr(websocket, "user_role", None)

        # 记录连接信息
        auth_status = "已认证" if user_id is not None else "未认证"
        user_info = f", 用户: {username}(ID={user_id}, 角色={user_role})" if user_id is not None else ""

        self.logger.info(f"新的WebSocket连接: ID={connection_id}, 客户端={client_addr}, {auth_status}{user_info}, 当前连接数: {len(self.active_connections)}")
        if IS_DEBUG:
            self.logger.debug(f"WebSocket连接详情: {websocket}")
            self.logger.debug(f"当前活跃连接: {len(self.active_connections)}")
            self.logger.debug(f"当前主题订阅: {[f'{topic}({len(conns)})' for topic, conns in self.topics.items()]}")

    async def disconnect(self, websocket):
        """断开WebSocket连接"""
        if websocket not in self.active_connections:
            self.logger.warning(f"尝试断开不存在的WebSocket连接: {id(websocket)}")
            return

        # 获取用户信息（如果已认证）
        user_id = getattr(websocket, "user_id", None)
        username = getattr(websocket, "username", None)

        # 用户信息字符串，用于日志
        user_info = f", 用户: {username}(ID={user_id})" if user_id is not None else ", 未认证用户"

        self.active_connections.remove(websocket)
        connection_id = id(websocket)

        # 清理订阅
        removed_topics = []
        for topic in list(self.topics.keys()):
            if websocket in self.topics[topic]:
                self.topics[topic].remove(websocket)
                self.logger.debug(f"从主题 {topic} 中移除WebSocket {connection_id}{user_info}")
                if not self.topics[topic]:  # 如果没有连接，则删除主题
                    del self.topics[topic]
                    removed_topics.append(topic)

        if removed_topics:
            self.logger.info(f"已删除空主题: {', '.join(removed_topics)}")

        self.logger.info(f"WebSocket连接断开: ID={connection_id}{user_info}, 剩余连接数: {len(self.active_connections)}")
        if IS_DEBUG:
            self.logger.debug(f"当前活跃连接: {len(self.active_connections)}")
            self.logger.debug(f"当前主题订阅: {[f'{topic}({len(conns)})' for topic, conns in self.topics.items()]}")

    async def subscribe(self, websocket, topic: str):
        """订阅主题"""
        connection_id = id(websocket)

        # 获取用户信息（如果已认证）
        user_id = getattr(websocket, "user_id", None)
        username = getattr(websocket, "username", None)

        # 用户信息字符串，用于日志
        user_info = f", 用户: {username}(ID={user_id})" if user_id is not None else ", 未认证用户"

        if websocket not in self.active_connections:
            self.logger.warning(f"尝试为未连接的WebSocket {connection_id} 订阅主题: {topic}{user_info}")
            return

        if topic not in self.topics:
            self.topics[topic] = set()
            self.logger.info(f"创建新主题: {topic}")

        # 检查是否已经订阅
        if websocket in self.topics[topic]:
            self.logger.debug(f"WebSocket {connection_id} 已经订阅了主题: {topic}{user_info}")
            return

        self.topics[topic].add(websocket)
        self.logger.info(f"WebSocket {connection_id} 订阅主题: {topic}{user_info}, 该主题当前订阅数: {len(self.topics[topic])}")

        if IS_DEBUG:
            self.logger.debug(f"当前主题订阅情况: {[f'{t}({len(conns)})' for t, conns in self.topics.items()]}")

    async def unsubscribe(self, websocket, topic: str):
        """取消订阅主题"""
        connection_id = id(websocket)

        # 获取用户信息（如果已认证）
        user_id = getattr(websocket, "user_id", None)
        username = getattr(websocket, "username", None)

        # 用户信息字符串，用于日志
        user_info = f", 用户: {username}(ID={user_id})" if user_id is not None else ", 未认证用户"

        if topic not in self.topics:
            self.logger.warning(f"尝试从不存在的主题 {topic} 取消订阅{user_info}")
            return

        if websocket not in self.topics[topic]:
            self.logger.warning(f"WebSocket {connection_id} 未订阅主题: {topic}{user_info}")
            return

        self.topics[topic].remove(websocket)
        self.logger.info(f"WebSocket {connection_id} 取消订阅主题: {topic}{user_info}")

        if not self.topics[topic]:  # 如果没有连接，则删除主题
            del self.topics[topic]
            self.logger.info(f"删除空主题: {topic}")

        if IS_DEBUG:
            self.logger.debug(f"当前主题订阅情况: {[f'{t}({len(conns)})' for t, conns in self.topics.items()]}")

    async def send_personal_message(self, message: str, websocket):
        """发送个人消息"""
        connection_id = id(websocket)
        message_length = len(message)
        start_time = time.time()

        try:
            if IS_DEBUG:
                self.logger.debug(f"开始向WebSocket {connection_id} 发送消息, 长度: {message_length} 字节")
                if message_length < 1000:  # 只记录较短的消息内容
                    self.logger.debug(f"消息内容: {message}")
                else:
                    self.logger.debug(f"消息内容(截断): {message[:500]}...")

            await websocket.send_text(message)
            if IS_DEBUG:
                elapsed = time.time() - start_time
                self.logger.debug(f"向WebSocket {connection_id} 发送消息成功, 耗时: {elapsed:.3f}秒")
        except Exception as e:
            elapsed = time.time() - start_time
            self.logger.error(f"向WebSocket {connection_id} 发送消息失败: {str(e)}, 耗时: {elapsed:.3f}秒")
            # 断开连接
            self.logger.info(f"由于发送失败，断开WebSocket {connection_id} 连接")
            await self.disconnect(websocket)

    async def broadcast(self, topic: str, message: Dict[str, Any]):
        """向订阅特定主题的所有连接广播消息"""
        start_time = time.time()

        # 记录所有活跃连接和主题订阅情况
        if IS_DEBUG:
            self.logger.debug(f"[WebSocket调试] 当前活跃连接数: {len(self.active_connections)}")
            self.logger.debug(f"[WebSocket调试] 当前主题订阅情况: {[f'{t}({len(conns)})' for t, conns in self.topics.items()]}")

        if topic not in self.topics:
            # 将警告改为调试信息，因为这是正常情况
            self.logger.debug(f"广播到不存在的主题 {topic}, 跳过")
            return

        subscribers_count = len(self.topics[topic])
        if subscribers_count == 0:
            # 将警告改为调试信息
            self.logger.debug(f"主题 {topic} 没有订阅者, 跳过广播")
            return

        # 准备STOMP格式消息
        stomp_message = self._format_stomp_message(topic, message)
        message_length = len(stomp_message)

        # 只在调试模式下记录详细信息
        if IS_DEBUG:
            self.logger.debug(f"开始广播消息到主题 {topic}, 订阅者数量: {subscribers_count}, 消息长度: {message_length} 字节")
            if message_length < 1000:  # 只记录较短的消息内容
                self.logger.debug(f"广播消息内容: {stomp_message}")
            else:
                self.logger.debug(f"广播消息内容(截断): {stomp_message[:500]}...")

        tasks = []
        for connection in self.topics[topic]:
            tasks.append(asyncio.create_task(self.send_personal_message(stomp_message, connection)))

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            error_count = len(results) - success_count
            elapsed = time.time() - start_time

            # 只在有错误或调试模式下记录详细信息
            if error_count > 0:
                self.logger.info(f"广播消息到主题 {topic} 完成, 成功: {success_count}, 失败: {error_count}, 耗时: {elapsed:.3f}秒")
                # 记录错误详情
                if IS_DEBUG:
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            self.logger.debug(f"广播错误 #{i+1}: {str(result)}")
            elif IS_DEBUG:
                self.logger.debug(f"广播消息到主题 {topic} 完成, 成功: {success_count}, 耗时: {elapsed:.3f}秒")

    def parse_stomp_frame(self, data: str) -> Dict[str, Any]:
        """解析STOMP帧"""
        lines = data.split('\n')
        if not lines:
            return {}

        command = lines[0]
        headers = {}
        body = ""

        # 查找空行，分割headers和body
        header_end = -1
        for i, line in enumerate(lines[1:], 1):
            if not line.strip():
                header_end = i
                break

        # 解析headers
        if header_end > 0:
            for line in lines[1:header_end]:
                if ':' in line:
                    k, v = line.split(':', 1)
                    headers[k] = v

            # 解析body
            if header_end < len(lines) - 1:
                body = '\n'.join(lines[header_end+1:])
                # 移除尾部的NULL字符
                if body.endswith('\0'):
                    body = body[:-1]

        return {
            'command': command,
            'headers': headers,
            'body': body
        }

    async def handle_client_message(self, websocket: WebSocket, data: str) -> bool:
        """
        处理客户端消息

        Args:
            websocket: WebSocket连接
            data: 客户端发送的消息

        Returns:
            bool: 如果应该继续处理消息返回True，如果应该断开连接返回False
        """
        connection_id = id(websocket)
        client_info = getattr(websocket, "client", None)
        client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"

        self.logger.debug(f"[WebSocket调试] 收到消息: {data[:100]}...")

        try:
            # 解析STOMP命令
            frame = self.parse_stomp_frame(data)
            command = frame.get('command')
            self.logger.debug(f"[WebSocket调试] 解析STOMP命令: {command}")

            if command == 'CONNECT':
                await self.handle_connect_command(websocket, frame)
                # 检查是否认证成功
                if not hasattr(websocket, 'user_id') or websocket.user_id is None:
                    self.logger.warning(f"WebSocket客户端认证失败，断开连接: {client_addr}")
                    return False  # 认证失败，断开连接
            elif command == 'SUBSCRIBE':
                # 检查是否已认证
                if not hasattr(websocket, 'user_id') or websocket.user_id is None:
                    self.logger.warning(f"未认证客户端尝试订阅，拒绝: {client_addr}")
                    error_msg = f"ERROR\nmessage:未认证用户不能执行订阅操作\n\n\0"
                    await websocket.send_text(error_msg)
                    return False  # 未认证，断开连接
                await self.handle_subscribe_command(websocket, frame)
            elif command == 'UNSUBSCRIBE':
                # 检查是否已认证
                if not hasattr(websocket, 'user_id') or websocket.user_id is None:
                    self.logger.warning(f"未认证客户端尝试取消订阅，拒绝: {client_addr}")
                    error_msg = f"ERROR\nmessage:未认证用户不能执行取消订阅操作\n\n\0"
                    await websocket.send_text(error_msg)
                    return False  # 未认证，断开连接
                await self.handle_unsubscribe_command(websocket, frame)
            elif command == 'SEND':
                # 检查是否已认证
                if not hasattr(websocket, 'user_id') or websocket.user_id is None:
                    self.logger.warning(f"未认证客户端尝试发送消息，拒绝: {client_addr}")
                    error_msg = f"ERROR\nmessage:未认证用户不能发送消息\n\n\0"
                    await websocket.send_text(error_msg)
                    return False  # 未认证，断开连接
                await self.handle_send_command(websocket, frame)
            elif command == 'DISCONNECT':
                await self.handle_disconnect_command(websocket, frame)
                return False  # 表示客户端请求断开连接
            else:
                self.logger.warning(f"收到未知STOMP命令: {command} 来自 {client_addr}")
                error_msg = f"ERROR\nmessage:未知的STOMP命令: {command}\n\n\0"
                await websocket.send_text(error_msg)
        except Exception as e:
            self.logger.error(f"处理WebSocket消息时出错: {str(e)}")
            # 发送错误消息
            error_msg = f"ERROR\nmessage:处理消息时出错\n\n{str(e)}\0"
            await websocket.send_text(error_msg)
            self.logger.debug(f"[WebSocket调试] 已发送错误消息: {error_msg}")

        return True  # 表示继续处理消息

    async def handle_connect_command(self, websocket: WebSocket, frame: Dict[str, Any]) -> None:
        """
        处理CONNECT命令

        Args:
            websocket: WebSocket连接
            frame: 解析后的STOMP帧
        """
        client_info = getattr(websocket, "client", None)
        client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"

        # 从headers中获取认证令牌
        headers = frame.get('headers', {})
        auth_header = headers.get('Authorization', '')
        self.logger.debug(f"[WebSocket调试] 认证头部: {auth_header}")

        # 提取令牌
        token = None
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]  # 移除 "Bearer " 前缀

        # 验证令牌
        if not token:
            # 未提供认证令牌，拒绝连接
            self.logger.warning(f"WebSocket客户端未提供认证令牌，拒绝连接: {client_addr}")
            error_msg = f"ERROR\nmessage:未提供有效的认证令牌\n\n\0"
            await websocket.send_text(error_msg)
            # 不发送CONNECTED帧，客户端会认为连接失败
            return

        try:
            # 创建数据库上下文
            async with get_db_context() as db:
                # 验证令牌
                user = await validate_token(db, token)
                # 将用户ID存储在WebSocket对象中，以便后续使用
                websocket.user_id = user.id
                websocket.username = user.username
                websocket.user_role = user.role
                self.logger.info(f"WebSocket客户端认证成功: {client_addr}, 用户: {user.username}, ID: {user.id}")

                # 认证成功，发送CONNECTED帧
                await websocket.send_text("CONNECTED\nversion:1.2\n\n\0")
                self.logger.info(f"WebSocket客户端已连接: {client_addr}, 认证状态: 已认证")
        except (JWTError, HTTPException) as e:
            # 认证失败，拒绝连接
            self.logger.warning(f"WebSocket客户端认证失败，拒绝连接: {client_addr}, 错误: {str(e)}")
            error_msg = f"ERROR\nmessage:认证失败: {str(e)}\n\n\0"
            await websocket.send_text(error_msg)
            # 不发送CONNECTED帧，客户端会认为连接失败

    async def handle_subscribe_command(self, websocket: WebSocket, frame: Dict[str, Any]) -> None:
        """
        处理SUBSCRIBE命令

        Args:
            websocket: WebSocket连接
            frame: 解析后的STOMP帧
        """
        client_info = getattr(websocket, "client", None)
        client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"

        # 从headers中获取destination
        headers = frame.get('headers', {})
        destination = headers.get('destination')

        if not destination:
            self.logger.warning(f"客户端 {client_addr} 尝试订阅但未提供destination")
            error_msg = f"ERROR\nmessage:未提供订阅目标\n\n\0"
            await websocket.send_text(error_msg)
            return

        # 获取用户ID（已在handle_client_message中验证过用户是否已认证）
        user_id = getattr(websocket, 'user_id')

        # 检查主题格式是否符合 /topic/buoys/{buoy_id}/data
        buoy_id = None
        topic_match = re.match(r'^/topic/buoys/(\d+)/data$', destination)
        if topic_match:
            buoy_id = int(topic_match.group(1))

            # 这里可以添加更多的权限检查，例如检查用户是否有权限访问该浮标
            # 例如：
            # async with get_db_context() as db:
            #     # 检查浮标是否存在
            #     buoy = await db.get(BuoyModel, buoy_id)
            #     if not buoy:
            #         self.logger.warning(f"客户端 {client_addr} 尝试订阅不存在的浮标: {buoy_id}")
            #         error_msg = f"ERROR\nmessage:浮标不存在\n\n\0"
            #         await websocket.send_text(error_msg)
            #         return
            #
            #     # 检查用户是否有权限访问该浮标
            #     if buoy.owner_id != user_id and websocket.user_role != "admin":
            #         self.logger.warning(f"客户端 {client_addr} 尝试订阅无权访问的浮标: {buoy_id}")
            #         error_msg = f"ERROR\nmessage:无权访问该浮标\n\n\0"
            #         await websocket.send_text(error_msg)
            #         return

        # 订阅主题
        await self.subscribe(websocket, destination)
        self.logger.info(f"客户端 {client_addr} 订阅了主题: {destination}, 用户ID: {user_id}")
        self.logger.debug(f"[WebSocket调试] 订阅后主题情况: {[f'{t}({len(conns)})' for t, conns in self.topics.items()]}")

    async def handle_unsubscribe_command(self, websocket: WebSocket, frame: Dict[str, Any]) -> None:
        """
        处理UNSUBSCRIBE命令

        Args:
            websocket: WebSocket连接
            frame: 解析后的STOMP帧
        """
        client_info = getattr(websocket, "client", None)
        client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"

        # 从headers中获取destination
        headers = frame.get('headers', {})
        destination = headers.get('destination')

        if not destination:
            self.logger.warning(f"客户端 {client_addr} 尝试取消订阅但未提供destination")
            return

        # 取消订阅主题
        await self.unsubscribe(websocket, destination)
        self.logger.info(f"客户端 {client_addr} 取消订阅主题: {destination}")

    async def handle_send_command(self, websocket: WebSocket, frame: Dict[str, Any]) -> None:
        """
        处理SEND命令

        Args:
            websocket: WebSocket连接
            frame: 解析后的STOMP帧
        """
        client_info = getattr(websocket, "client", None)
        client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"
        user_id = getattr(websocket, "user_id", None)
        username = getattr(websocket, "username", None)

        # 获取目标主题和消息体
        headers = frame.get('headers', {})
        destination = headers.get('destination')
        body = frame.get('body', '')

        if not destination:
            self.logger.warning(f"客户端 {client_addr} 发送消息但未提供destination")
            error_msg = f"ERROR\nmessage:未提供目标主题\n\n\0"
            await websocket.send_text(error_msg)
            return

        self.logger.info(f"收到客户端 {client_addr} 发送的消息，目标: {destination}, 用户: {username}(ID={user_id})")

        # 检查是否是浮标控制命令
        # 控制命令主题格式: /topic/buoys/{buoy_id}/control
        control_match = re.match(r'^/topic/buoys/(\d+)/control$', destination)
        if control_match:
            buoy_id = int(control_match.group(1))
            self.logger.info(f"收到浮标控制命令: buoy_id={buoy_id}, 用户: {username}(ID={user_id})")

            try:
                # 解析控制命令
                command_data = json.loads(body)
                self.logger.debug(f"控制命令内容: {command_data}")

                # 验证命令格式
                if 'command' not in command_data:
                    self.logger.warning(f"控制命令缺少command字段: {command_data}")
                    error_msg = f"ERROR\nmessage:控制命令缺少command字段\n\n\0"
                    await websocket.send_text(error_msg)
                    return

                # 导入浮标控制服务
                from fastapi_app.services.buoy_control_service import send_control_command, broadcast_control_response
                from fastapi_app.schemas.buoy import ControlCommand
                from pydantic import ValidationError

                # 创建数据库上下文
                async with get_db_context() as db:
                    try:
                        # 创建ControlCommand对象
                        control_command = ControlCommand(**command_data)

                        # 发送控制命令
                        success = await send_control_command(buoy_id, control_command, db)

                        if success:
                            # 使用浮标控制服务广播响应
                            await broadcast_control_response(
                                buoy_id=buoy_id,
                                status="success",
                                message="控制指令已发送",
                                command_data=command_data
                            )

                            # 记录成功日志
                            self.logger.info(f"浮标控制命令已发送: buoy_id={buoy_id}, command={command_data['command']}")
                        else:
                            # 发送失败响应
                            error_msg = f"ERROR\nmessage:发送控制指令失败\n\n\0"
                            await websocket.send_text(error_msg)
                    except ValidationError as e:
                        # 验证错误
                        self.logger.warning(f"控制命令验证失败: {str(e)}")
                        error_msg = f"ERROR\nmessage:控制命令验证失败: {str(e)}\n\n\0"
                        await websocket.send_text(error_msg)
                    except Exception as e:
                        # 其他错误
                        self.logger.error(f"处理控制命令时出错: {str(e)}", exc_info=True)
                        error_msg = f"ERROR\nmessage:处理控制命令时出错: {str(e)}\n\n\0"
                        await websocket.send_text(error_msg)
            except json.JSONDecodeError as e:
                self.logger.warning(f"无法解析控制命令JSON: {str(e)}")
                error_msg = f"ERROR\nmessage:无法解析控制命令JSON: {str(e)}\n\n\0"
                await websocket.send_text(error_msg)
            except Exception as e:
                self.logger.error(f"处理控制命令时出错: {str(e)}", exc_info=True)
                error_msg = f"ERROR\nmessage:处理控制命令时出错: {str(e)}\n\n\0"
                await websocket.send_text(error_msg)
        else:
            # 不是控制命令，记录日志
            self.logger.debug(f"[WebSocket调试] 收到非控制命令消息: {destination}")

    async def handle_disconnect_command(self, websocket: WebSocket, frame: Dict[str, Any]) -> None:
        """
        处理DISCONNECT命令

        Args:
            websocket: WebSocket连接
            frame: 解析后的STOMP帧
        """
        client_info = getattr(websocket, "client", None)
        client_addr = f"{client_info.host}:{client_info.port}" if client_info else "未知"

        # 客户端要求断开连接
        self.logger.info(f"客户端 {client_addr} 请求断开连接")

    def _format_stomp_message(self, destination: str, payload: Dict[str, Any]) -> str:
        """
        将消息格式化为STOMP协议格式
        """
        try:
            body = json.dumps(payload)
            headers = [
                f"destination:{destination}",
                f"content-type:application/json",
                f"content-length:{len(body)}"
            ]

            stomp_frame = "MESSAGE\n" + "\n".join(headers) + "\n\n" + body + "\0"
            return stomp_frame
        except Exception as e:
            self.logger.error(f"格式化STOMP消息失败: {str(e)}")
            # 返回一个错误消息
            error_body = json.dumps({"error": f"消息格式化失败: {str(e)}"})
            error_headers = [
                f"destination:{destination}",
                f"content-type:application/json",
                f"content-length:{len(error_body)}"
            ]
            return "MESSAGE\n" + "\n".join(error_headers) + "\n\n" + error_body + "\0"

# 创建单例实例
websocket_service = WebSocketManager()