import asyncio
import time
from typing import Any, Optional


class LocalCacheService:
    _data = {}
    _expire = {}
    _lock = asyncio.Lock()

    @classmethod
    async def _cleanup(cls):
        now = time.time()
        keys_to_delete = [k for k, v in cls._expire.items() if v <= now]
        for k in keys_to_delete:
            cls._data.pop(k, None)
            cls._expire.pop(k, None)

    @classmethod
    async def get(cls, key: str) -> Optional[Any]:
        async with cls._lock:
            await cls._cleanup()
            return cls._data.get(key)

    @classmethod
    async def set(cls, key: str, value: Any, ex: int = None):
        async with cls._lock:
            cls._data[key] = value
            if ex:
                cls._expire[key] = time.time() + ex

    @classmethod
    async def delete(cls, key: str):
        async with cls._lock:
            cls._data.pop(key, None)
            cls._expire.pop(key, None)

    @classmethod
    async def exists(cls, key: str) -> bool:
        async with cls._lock:
            await cls._cleanup()
            return key in cls._data

    @classmethod
    async def incr(cls, key: str):
        async with cls._lock:
            await cls._cleanup()
            value = int(cls._data.get(key, 0)) + 1
            cls._data[key] = value
            return value

    @classmethod
    async def expire(cls, key: str, ex: int):
        async with cls._lock:
            if key in cls._data:
                cls._expire[key] = time.time() + ex 