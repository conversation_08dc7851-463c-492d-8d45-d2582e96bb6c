import json
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Set, Callable

from gmqtt import Client as MQTTClient
from gmqtt.mqtt.constants import MQTTv311

from fastapi_app.core.config import settings
from fastapi_app.db.base import async_session
from fastapi_app.models.buoy_model import Buoy as BuoyModel
from fastapi_app.models.sensor_data_model import SensorData as SensorDataModel
from geoalchemy2.elements import WKTElement
from fastapi_app.services.websocket_service import websocket_service
from fastapi_app.core.logging_config import IS_DEBUG

# 配置日志
logger = logging.getLogger(__name__)


class MQTTService:
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.client = MQTTClient(settings.MQTT_CLIENT_ID)
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect
        self.client.on_subscribe = self._on_subscribe

        # 设置认证信息
        if settings.MQTT_USERNAME:
            self.client.set_auth_credentials(
                settings.MQTT_USERNAME,
                settings.MQTT_PASSWORD
            )

        # 存储订阅的主题和回调函数
        self._subscriptions: Dict[str, List[Callable]] = {}
        # 连接状态
        self.is_connected = False

    async def connect(self):
        """连接到MQTT Broker"""
        try:
            ssl_context = None
            if settings.MQTT_USE_TLS:
                import ssl
                ssl_context = ssl.create_default_context()

                # 如果配置了CA证书路径，则加载证书
                if hasattr(settings, 'MQTT_CA_CERT_PATH') and settings.MQTT_CA_CERT_PATH:
                    try:
                        ssl_context.load_verify_locations(cafile=settings.MQTT_CA_CERT_PATH)
                        logger.info(f"已加载MQTT CA证书: {settings.MQTT_CA_CERT_PATH}")
                    except Exception as cert_err:
                        logger.error(f"加载MQTT CA证书失败: {str(cert_err)}")
                        # 如果证书加载失败，可以选择继续（不验证服务器证书）或抛出异常
                        ssl_context.check_hostname = False
                        ssl_context.verify_mode = ssl.CERT_NONE
                        logger.warning("已禁用MQTT服务器证书验证")

            await self.client.connect(
                settings.MQTT_BROKER_HOST,
                settings.MQTT_BROKER_PORT,
                ssl=ssl_context if settings.MQTT_USE_TLS else False,
                version=MQTTv311
            )
            logger.info(f"MQTT客户端已连接到{settings.MQTT_BROKER_HOST}:{settings.MQTT_BROKER_PORT} (TLS: {settings.MQTT_USE_TLS})")
        except Exception as e:
            logger.error(f"MQTT连接失败: {str(e)}")
            raise

    async def disconnect(self):
        """断开与MQTT Broker的连接"""
        if self.is_connected:
            await self.client.disconnect()
            logger.info("MQTT客户端已断开连接")

    async def publish(self, topic: str, payload: Dict[str, Any], qos: int = 0, retain: bool = False):
        """发布消息到指定主题"""
        if not self.is_connected:
            logger.warning("MQTT客户端未连接，无法发布消息")
            return False

        try:
            # 将字典转换为JSON字符串
            message = json.dumps(payload)
            self.client.publish(topic, message, qos=qos, retain=retain)
            logger.debug(f"已发布消息到主题 {topic}: {message}")
            return True
        except Exception as e:
            logger.error(f"发布消息失败: {str(e)}")
            return False

    async def subscribe(self, topic: str, qos: int = 0, callback: Optional[Callable] = None):
        """订阅主题"""
        if not self.is_connected:
            logger.warning("MQTT客户端未连接，无法订阅主题")
            return False

        try:
            self.client.subscribe(topic, qos)

            # 如果提供了回调函数，则存储它
            if callback:
                if topic not in self._subscriptions:
                    self._subscriptions[topic] = []
                self._subscriptions[topic].append(callback)

            logger.info(f"已订阅主题: {topic} (QoS {qos})")
            return True
        except Exception as e:
            logger.error(f"订阅主题失败: {str(e)}")
            return False

    def _on_connect(self, client, flags, rc, properties):
        """连接回调"""
        logger.info(f"MQTT客户端已连接，返回码: {rc}")
        self.is_connected = True

        # 重新订阅之前的主题
        for topic in self._subscriptions.keys():
            self.client.subscribe(topic)

    def _on_message(self, client, topic, payload, qos, properties):
        """消息回调"""
        try:
            payload_str = payload.decode('utf-8')

            # logger.info(f"[MQTT调试] 收到消息 - 主题: {topic}")

            # 增加调试日志，记录完整的消息内容
            logger.debug(f"[MQTT调试] 收到消息 - 主题: {topic}, QoS: {qos}")
            logger.debug(f"[MQTT调试] 消息内容: {payload_str}")
            logger.debug(f"[MQTT调试] 消息属性: {properties}")

            # 尝试解析JSON
            try:
                data = json.loads(payload_str)
                logger.debug(f"[MQTT调试] 成功解析JSON数据: {type(data)}")
            except json.JSONDecodeError as e:
                data = payload_str
                logger.debug(f"[MQTT调试] 无法解析为JSON，使用原始字符串: {e}")

            # 调用该主题的所有回调函数
            if topic in self._subscriptions:
                logger.debug(f"[MQTT调试] 找到精确匹配的主题订阅: {topic}")
                for callback in self._subscriptions[topic]:
                    logger.debug(f"[MQTT调试] 调用回调函数: {callback.__name__ if hasattr(callback, '__name__') else str(callback)}")
                    asyncio.create_task(callback(topic, data))

            # 使用通配符处理
            for subscribed_topic, callbacks in self._subscriptions.items():
                if self._is_matching_topic(subscribed_topic, topic) and subscribed_topic != topic:
                    logger.debug(f"[MQTT调试] 找到通配符匹配的主题订阅: {subscribed_topic} -> {topic}")
                    for callback in callbacks:
                        logger.debug(f"[MQTT调试] 调用通配符匹配的回调函数: {callback.__name__ if hasattr(callback, '__name__') else str(callback)}")
                        asyncio.create_task(callback(topic, data))

        except Exception as e:
            logger.error(f"处理MQTT消息时出错: {str(e)}", exc_info=True)

    def _on_disconnect(self, client, packet, exc=None):
        """断开连接回调"""
        self.is_connected = False
        logger.info("MQTT客户端已断开连接")
        if exc:
            logger.error(f"断开连接原因: {str(exc)}")

    def _on_subscribe(self, client, mid, qos, properties):
        """订阅回调"""
        logger.debug(f"订阅确认，消息ID: {mid}, QoS: {qos}")

    def _is_matching_topic(self, subscription: str, topic: str) -> bool:
        """检查主题是否匹配订阅 (支持通配符 # 和 +)"""
        sub_parts = subscription.split('/')
        topic_parts = topic.split('/')

        if len(sub_parts) > len(topic_parts) and sub_parts[-1] != '#':
            return False

        for i, sub_part in enumerate(sub_parts):
            if sub_part == '#':
                return True

            if i >= len(topic_parts):
                return False

            if sub_part != '+' and sub_part != topic_parts[i]:
                return False

        return len(sub_parts) == len(topic_parts)

    async def setup_subscriptions(self):
        """设置应用级别的 MQTT 订阅"""
        try:
            # 订阅所有浮标数据 (传感器和位置)
            await self.subscribe("buoy/data/+/+", qos=1, callback=self._handle_buoy_data) # 修改为订阅两级通配符
            self.logger.info("已设置 MQTT 订阅: buoy/data/+/+")

            # 订阅心跳 (如果需要)
            await self.subscribe("buoy/heartbeat/+", qos=0, callback=self._handle_heartbeat)
            self.logger.info("已设置 MQTT 订阅: buoy/heartbeat/+")

            # 订阅控制命令响应
            await self.subscribe("buoy/command/response/+", qos=1, callback=self._handle_command_response)
            self.logger.info("已设置 MQTT 订阅: buoy/command/response/+")

            # 订阅浮标状态（LWT/主动上下线）
            await self.subscribe("buoy/+/status", qos=1, callback=self._handle_buoy_status)
            self.logger.info("已设置 MQTT 订阅: buoy/+/status")
        except Exception as e:
            self.logger.error(f"设置应用级 MQTT 订阅失败: {e}", exc_info=True)

    async def _handle_buoy_data(self, topic: str, payload: dict | str):
        """处理从 buoy/data/+ 主题接收到的消息"""
        if IS_DEBUG:
            self.logger.debug(f"处理 MQTT 消息 - 主题: {topic}")
            self.logger.debug(f"[MQTT调试] 开始处理浮标数据 - 主题: {topic}")
            self.logger.debug(f"[MQTT调试] 原始数据类型: {type(payload)}")
            self.logger.debug(f"[MQTT调试] 原始数据内容: {payload}")

        try:
            if not isinstance(payload, dict):
                self.logger.warning("收到的 payload 不是字典格式，尝试解析JSON")
                self.logger.debug(f"[MQTT调试] 尝试将非字典数据解析为JSON: {payload}")
                try:
                    payload = json.loads(payload)
                    self.logger.debug(f"[MQTT调试] JSON解析成功，转换后类型: {type(payload)}")
                except json.JSONDecodeError as e:
                    self.logger.error(f"无法将 payload 解析为 JSON: {e}")
                    self.logger.debug(f"[MQTT调试] JSON解析失败: {e}")
                    return

            # 从主题中提取 buoy_id 和 data_type
            # 主题格式: buoy/data/{buoy_id}/{data_type}
            topic_parts = topic.split('/')
            self.logger.debug(f"[MQTT调试] 主题解析: {topic_parts}")
            if len(topic_parts) != 4 or topic_parts[0] != 'buoy' or topic_parts[1] != 'data':
                self.logger.warning(f"无效的主题格式: {topic}")
                self.logger.debug(f"[MQTT调试] 主题格式无效，期望格式: buoy/data/{{buoy_id}}/{{data_type}}")
                return

            buoy_id_str = topic_parts[2]
            data_type = topic_parts[3]
            self.logger.debug(f"[MQTT调试] 从主题提取 buoy_id={buoy_id_str}, data_type={data_type}")

            # 确保 buoy_id 与 payload 中的一致 (可选但推荐)
            payload_buoy_id = payload.get('buoy_id')
            self.logger.debug(f"[MQTT调试] Payload中的buoy_id: {payload_buoy_id}")
            if str(payload_buoy_id) != buoy_id_str:
                self.logger.warning(f"主题 buoy_id ({buoy_id_str}) 与 payload buoy_id ({payload_buoy_id}) 不匹配")
                self.logger.debug(f"[MQTT调试] buoy_id不匹配: 主题中={buoy_id_str}, payload中={payload_buoy_id}")
                # 可以选择返回或继续处理

            # 确保 buoy_id 是整数
            try:
                buoy_id = int(buoy_id_str)
                self.logger.debug(f"[MQTT调试] buoy_id转换为整数: {buoy_id}")
            except ValueError as e:
                self.logger.error(f"主题中的 buoy_id 无效: {buoy_id_str}")
                self.logger.debug(f"[MQTT调试] buoy_id转换为整数失败: {e}")
                return

            # 获取必要的数据字段
            timestamp_str = payload.get('timestamp')
            value = payload.get('value')
            unit = payload.get('unit')
            self.logger.debug(f"[MQTT调试] 提取的字段 - timestamp: {timestamp_str}, value: {value}, unit: {unit}")

            if timestamp_str is None or value is None:
                self.logger.warning(f"消息缺少必要的字段 (timestamp或value): {payload}")
                self.logger.debug(f"[MQTT调试] 缺少必要字段，终止处理")
                return

            # 解析时间戳
            try:
                # 模拟器发送的是 ISO 格式字符串
                timestamp = datetime.fromisoformat(timestamp_str)
                self.logger.debug(f"[MQTT调试] 时间戳解析成功: {timestamp}")
                # 确保时间戳有时区信息，如果没有则添加UTC时区
                if timestamp.tzinfo is None:
                    timestamp = timestamp.replace(tzinfo=timezone.utc)
                    self.logger.debug(f"[MQTT调试] 添加UTC时区到时间戳: {timestamp}")
                else:
                    # 如果已有时区，统一转换为UTC
                    timestamp = timestamp.astimezone(timezone.utc)
                    self.logger.debug(f"[MQTT调试] 将时间戳转换为UTC: {timestamp}")
            except ValueError as e:
                self.logger.error(f"无法解析时间戳: {timestamp_str}")
                self.logger.debug(f"[MQTT调试] 时间戳解析失败: {e}")
                return

            # 创建数据库会话
            self.logger.debug(f"[MQTT调试] 开始数据库操作")
            async with async_session() as db:
                try:
                    # 获取 Buoy 实例 (使用 select().where() 以便之后更新)
                    # result = await db.execute(select(BuoyModel).where(BuoyModel.id == buoy_id))
                    # buoy = result.scalar_one_or_none()
                    # 或者直接使用 get (如果只读)
                    self.logger.debug(f"[MQTT调试] 查询浮标信息: buoy_id={buoy_id}")
                    buoy = await db.get(BuoyModel, buoy_id)
                    self.logger.debug(f"[MQTT调试] 查询结果: {buoy is not None}")

                    if not buoy:
                        self.logger.warning(f"数据库中不存在 buoy_id: {buoy_id}。正在尝试创建...")
                        self.logger.debug(f"[MQTT调试] 浮标不存在，开始自动创建")

                        # 创建新的浮标记录
                        buoy_name = f"自动创建浮标 {buoy_id}"

                        # 如果是位置数据，使用当前位置作为浮标初始位置
                        latest_location = None
                        if data_type == 'location' and isinstance(value, dict) and 'longitude' in value and 'latitude' in value:
                            lon = value['longitude']
                            lat = value['latitude']
                            latest_location = WKTElement(f'POINT({lon} {lat})', srid=4326)
                            self.logger.debug(f"[MQTT调试] 使用当前位置数据作为浮标初始位置: 经度={lon}, 纬度={lat}")

                        # 创建浮标对象
                        buoy = BuoyModel(
                            id=buoy_id,
                            name=buoy_name,
                            description=f"由MQTT服务自动创建于 {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')}",
                            latest_location=latest_location,
                            status="active",
                            last_heartbeat=datetime.now(timezone.utc)
                            # owner_id是可选的，不再设置默认值
                        )

                        db.add(buoy)
                        await db.flush()  # 获取 buoy.id（如果id不是传入的）
                        self.logger.info(f"已自动创建浮标: ID={buoy_id}, 名称='{buoy_name}'")
                        self.logger.debug(f"[MQTT调试] 浮标创建成功: {buoy}")

                        # 注意：不需要return，继续处理数据

                    # 准备 SensorDataModel
                    self.logger.debug(f"[MQTT调试] 创建传感器数据记录: buoy_id={buoy_id}, data_type={data_type}")
                    sensor_data = SensorDataModel(
                        buoy_id=buoy_id,
                        timestamp=timestamp,
                        data_type=data_type,
                        unit=unit # unit 可能为 None
                    )

                    # 根据数据类型处理 value 和 location_value
                    if data_type == 'location':
                        self.logger.debug(f"[MQTT调试] 处理位置数据: {value}")
                        if isinstance(value, dict) and 'longitude' in value and 'latitude' in value:
                            lon = value['longitude']
                            lat = value['latitude']
                            self.logger.debug(f"[MQTT调试] 位置数据有效: 经度={lon}, 纬度={lat}")
                            # 使用 WKTElement 创建 Point 对象
                            point = WKTElement(f'POINT({lon} {lat})', srid=4326)
                            sensor_data.location_value = point
                            sensor_data.value = None # 清除数值字段
                            # 更新 buoys 表的 latest_location
                            buoy.latest_location = point
                            buoy.updated_at = datetime.now(timezone.utc) # 更新时间戳，使用UTC时区
                            db.add(buoy) # 将 buoy 添加到会话以更新
                            self.logger.debug(f"更新浮标 {buoy_id} 的位置为: {lon}, {lat}")
                            self.logger.debug(f"[MQTT调试] 已更新浮标位置")
                        else:
                            self.logger.warning(f"位置数据格式无效或缺少经纬度: {value}")
                            self.logger.debug(f"[MQTT调试] 位置数据格式无效，终止处理")
                            return
                    else:
                        self.logger.debug(f"[MQTT调试] 处理传感器数据: 类型={data_type}, 值={value}")
                        # 尝试将 value 转换为 float
                        try:
                            sensor_data.value = float(value)
                            sensor_data.location_value = None # 清除位置字段
                            self.logger.debug(f"[MQTT调试] 传感器值转换成功: {sensor_data.value}")
                        except (ValueError, TypeError) as e:
                            self.logger.warning(f"传感器值无法转换为浮点数: {value}, 类型: {data_type}")
                            self.logger.debug(f"[MQTT调试] 传感器值转换失败: {e}")
                            # 数据无法正确转换，不存储该条数据
                            return # 如果无法转换则不存储该条数据

                    # 添加并提交
                    if IS_DEBUG:
                        self.logger.debug(f"[MQTT调试] 添加传感器数据到数据库")
                    db.add(sensor_data)
                    await db.commit()
                    if IS_DEBUG:
                        self.logger.debug(f"成功存储 buoy_id={buoy_id}, data_type='{data_type}' 的数据")
                        self.logger.debug(f"[MQTT调试] 数据库提交成功，数据ID: {sensor_data.id}")

                    # 向WebSocket客户端推送新数据
                    if IS_DEBUG:
                        self.logger.debug(f"[MQTT调试] 准备WebSocket推送")
                    ws_payload = None

                    if data_type == 'location':
                        # 位置数据
                        if isinstance(value, dict) and 'longitude' in value and 'latitude' in value:
                            ws_payload = {
                                "id": str(sensor_data.id),
                                "buoyId": str(buoy_id),
                                "timestamp": sensor_data.timestamp.isoformat(),
                                "data_type": data_type,
                                "value": value,  # 经纬度字典
                                "unit": unit or ""
                            }
                            if IS_DEBUG:
                                self.logger.debug(f"[MQTT调试] 创建位置数据WebSocket负载: {ws_payload}")
                    else:
                        # 普通传感器数据
                        ws_payload = {
                            "id": str(sensor_data.id),
                            "buoyId": str(buoy_id),
                            "timestamp": sensor_data.timestamp.isoformat(),
                            "data_type": data_type,
                            "value": sensor_data.value,
                            "unit": unit or ""
                        }
                        if IS_DEBUG:
                            self.logger.debug(f"[MQTT调试] 创建传感器数据WebSocket负载: {ws_payload}")

                    if ws_payload:
                        # 发送到特定浮标的主题: /topic/buoys/{buoy_id}/data
                        specific_topic = f"/topic/buoys/{buoy_id}/data"
                        if IS_DEBUG:
                            self.logger.debug(f"[MQTT调试] 广播到WebSocket主题: {specific_topic}")
                        await websocket_service.broadcast(specific_topic, ws_payload)

                        # 也可以广播到通用主题，所有客户端都能收到
                        # await websocket_service.broadcast("/topic/sensor_data", ws_payload)
                        if IS_DEBUG:
                            self.logger.debug(f"[MQTT调试] WebSocket广播完成")

                except Exception as e:
                    await db.rollback() # 回滚事务
                    self.logger.error(f"数据库操作失败: {str(e)}", exc_info=True)
                    self.logger.debug(f"[MQTT调试] 数据库操作异常: {e}", exc_info=True)

        except Exception as e:
            self.logger.error(f"处理 MQTT 消息时发生意外错误: {str(e)}", exc_info=True)

    # 处理心跳的函数 (如果需要)
    async def _handle_heartbeat(self, topic: str, payload: dict | str):
        """处理心跳消息"""
        if IS_DEBUG:
            self.logger.debug(f"处理 MQTT 心跳消息 - 主题: {topic}")
            self.logger.debug(f"[MQTT调试] 开始处理心跳消息: {topic}")
        try:
            # 从 topic 中提取 buoy_id，假设 topic 格式为 "buoy/heartbeat/{buoy_id}"
            parts = topic.split('/')
            self.logger.debug(f"[MQTT调试] 心跳主题解析: {parts}")
            if len(parts) == 3 and parts[0] == 'buoy' and parts[1] == 'heartbeat':
                buoy_id_str = parts[2]
                try:
                    buoy_id = int(buoy_id_str)
                    self.logger.debug(f"[MQTT调试] 心跳buoy_id解析成功: {buoy_id}")
                except ValueError as e:
                    self.logger.warning(f"无效的 buoy_id 格式: {buoy_id_str} 来自主题: {topic}")
                    self.logger.debug(f"[MQTT调试] 心跳buoy_id解析失败: {e}")
                    return

                # 解析心跳消息内容
                status = "active"  # 默认状态
                timestamp = datetime.now(timezone.utc).isoformat()  # 默认时间戳

                # 尝试从payload中提取状态和时间戳
                if isinstance(payload, dict):
                    if 'status' in payload:
                        status = payload['status']
                    if 'timestamp' in payload:
                        timestamp = payload['timestamp']
                elif isinstance(payload, str):
                    # 尝试解析JSON字符串
                    try:
                        payload_dict = json.loads(payload)
                        if isinstance(payload_dict, dict):
                            if 'status' in payload_dict:
                                status = payload_dict['status']
                            if 'timestamp' in payload_dict:
                                timestamp = payload_dict['timestamp']
                    except json.JSONDecodeError:
                        # 如果不是JSON，使用默认值
                        pass

                self.logger.debug(f"[MQTT调试] 心跳消息内容: status={status}, timestamp={timestamp}")

                self.logger.debug(f"[MQTT调试] 开始更新浮标心跳状态: buoy_id={buoy_id}")
                async with async_session() as db:
                    buoy = await db.get(BuoyModel, buoy_id)
                    self.logger.debug(f"[MQTT调试] 心跳查询浮标结果: {buoy is not None}")
                    if buoy:
                        # 记录旧状态
                        old_status = buoy.status

                        # 使用带时区的UTC时间替代已弃用的utcnow()
                        current_time = datetime.now(timezone.utc)
                        buoy.last_heartbeat = current_time
                        buoy.status = status  # 使用从消息中提取的状态
                        db.add(buoy)
                        await db.commit()

                        if IS_DEBUG:
                            self.logger.debug(f"浮标 {buoy_id} 心跳更新成功，状态从 '{old_status}' 设置为 '{status}'")
                            self.logger.debug(f"[MQTT调试] 心跳状态更新成功")

                        # 如果状态发生变化，广播状态变化
                        if old_status != status:
                            await self._broadcast_buoy_status_change(buoy_id, status, old_status, "heartbeat")
                        else:
                            # 即使状态没有变化，也发送心跳数据更新
                            try:
                                heartbeat_payload = {
                                    "type": "heartbeat",
                                    "buoyId": str(buoy_id),
                                    "timestamp": current_time.isoformat(),
                                    "data_type": "heartbeat",
                                    "value": status,
                                    "source": "heartbeat"
                                }

                                specific_topic = f"/topic/buoys/{buoy_id}/data"
                                await websocket_service.broadcast(specific_topic, heartbeat_payload)

                                if IS_DEBUG:
                                    self.logger.debug(f"[MQTT调试] 心跳数据广播完成: {specific_topic}")
                            except Exception as ws_err:
                                self.logger.error(f"广播心跳数据失败: {str(ws_err)}")
                                self.logger.debug(f"[MQTT调试] WebSocket广播异常: {ws_err}", exc_info=True)
                    else:
                        self.logger.warning(f"未找到 buoy_id: {buoy_id} 来自主题: {topic}，正在尝试创建...")
                        self.logger.debug(f"[MQTT调试] 心跳对应的浮标不存在，开始自动创建")

                        # 创建新的浮标记录
                        buoy_name = f"自动创建浮标 {buoy_id}"

                        # 创建浮标对象
                        current_time = datetime.now(timezone.utc)
                        buoy = BuoyModel(
                            id=buoy_id,
                            name=buoy_name,
                            description=f"由MQTT心跳服务自动创建于 {current_time.strftime('%Y-%m-%d %H:%M:%S')}",
                            status=status,  # 使用从消息中提取的状态
                            last_heartbeat=current_time
                            # owner_id是可选的，不再设置默认值
                        )

                        db.add(buoy)
                        await db.commit()
                        self.logger.info(f"已自动创建浮标: ID={buoy_id}, 名称='{buoy_name}'")
                        if IS_DEBUG:
                            self.logger.debug(f"[MQTT调试] 浮标创建成功: {buoy}")

                        # 通过WebSocket广播新创建的浮标状态
                        await self._broadcast_buoy_status_change(buoy_id, status, None, "heartbeat_new_buoy")
            else:
                self.logger.warning(f"未知的心跳主题格式: {topic}")
                self.logger.debug(f"[MQTT调试] 心跳主题格式无效，期望格式: buoy/heartbeat/{{buoy_id}}")

        except Exception as e:
            self.logger.error(f"处理心跳消息时发生错误: {str(e)}", exc_info=True)
            self.logger.debug(f"[MQTT调试] 心跳处理异常: {e}", exc_info=True)
            # 注意：心跳处理失败不应影响主循环，这里不需要 rollback，因为没有事务需要回滚，只是更新操作可能失败

    async def _handle_command_response(self, topic: str, payload: dict | str):
        """处理控制命令响应"""
        if IS_DEBUG:
            self.logger.debug(f"处理控制命令响应 - 主题: {topic}")
            self.logger.debug(f"[MQTT调试] 开始处理控制命令响应: {topic}")
            self.logger.debug(f"[MQTT调试] 响应数据: {payload}")

        try:
            # 从主题中提取浮标ID
            # 主题格式: buoy/command/response/{buoy_id}
            topic_parts = topic.split('/')
            if len(topic_parts) != 4 or topic_parts[0] != 'buoy' or topic_parts[1] != 'command' or topic_parts[2] != 'response':
                self.logger.warning(f"无效的命令响应主题格式: {topic}")
                return

            buoy_id_str = topic_parts[3]
            try:
                buoy_id = int(buoy_id_str)
            except ValueError:
                self.logger.warning(f"无效的浮标ID格式: {buoy_id_str}")
                return

            # 确保payload是字典
            if not isinstance(payload, dict):
                try:
                    payload = json.loads(payload)
                except json.JSONDecodeError:
                    self.logger.warning(f"无法解析命令响应JSON: {payload}")
                    return

            # 构造WebSocket消息
            ws_payload = {
                "type": "command_response",
                "buoyId": str(buoy_id),
                "timestamp": payload.get('timestamp', datetime.now(timezone.utc).isoformat()),
                "status": payload.get('status', 'unknown'),
                "command": payload.get('command', ''),
                "message": payload.get('message', '控制指令已成功发送到浮标'),
                "current_settings": payload.get('current_settings', {})
            }

            # 广播到WebSocket
            response_topic = f"/topic/buoys/{buoy_id}/control_response"
            await websocket_service.broadcast(response_topic, ws_payload)

            self.logger.info(f"已广播浮标控制响应: buoy_id={buoy_id}, status={ws_payload['status']}")
            if IS_DEBUG:
                self.logger.debug(f"[MQTT调试] 控制响应广播完成: {response_topic}")

        except Exception as e:
            self.logger.error(f"处理控制命令响应时出错: {str(e)}", exc_info=True)

    async def _handle_buoy_status(self, topic: str, payload: dict | str):
        """处理浮标状态（LWT/主动上下线）消息"""
        if IS_DEBUG:
            self.logger.debug(f"处理浮标状态消息 - 主题: {topic}, 内容: {payload}")
        try:
            # 主题格式: buoy/{buoy_id}/status
            logger.info(f"[MQTT] 收到浮标状态消息：{topic}: {payload}")

            parts = topic.split('/')
            if len(parts) == 3 and parts[0] == 'buoy' and parts[2] == 'status':
                try:
                    buoy_id = int(parts[1])
                except ValueError:
                    self.logger.warning(f"浮标状态主题buoy_id无效: {parts[1]}")
                    return
                # 解析状态
                status_str = str(payload).strip().lower()
                if status_str == 'online':
                    new_status = 'active'
                elif status_str == 'offline':
                    new_status = 'inactive'
                else:
                    self.logger.warning(f"未知的浮标状态消息: {status_str}")
                    return

                # 更新数据库
                async with async_session() as db:
                    buoy = await db.get(BuoyModel, buoy_id)
                    if buoy:
                        old_status = buoy.status
                        buoy.status = new_status
                        buoy.last_heartbeat = datetime.now(timezone.utc)
                        db.add(buoy)
                        await db.commit()

                        self.logger.info(f"浮标 {buoy_id} 状态从 {old_status} 更新为 {new_status}")

                        # 广播状态变化到WebSocket
                        await self._broadcast_buoy_status_change(buoy_id, new_status, old_status)
                    else:
                        self.logger.warning(f"收到状态消息但未找到浮标: {buoy_id}")
            else:
                self.logger.warning(f"未知的浮标状态主题格式: {topic}")
        except Exception as e:
            self.logger.error(f"处理浮标状态消息时发生错误: {str(e)}", exc_info=True)

    async def _broadcast_buoy_status_change(self, buoy_id: int, new_status: str, old_status: str = None, source: str = "mqtt_lwt"):
        """
        广播浮标状态变化到WebSocket

        Args:
            buoy_id: 浮标ID
            new_status: 新状态
            old_status: 旧状态（可选）
            source: 消息来源（可选）
        """
        try:
            current_time = datetime.now(timezone.utc)

            # 构造状态变化消息
            status_change_payload = {
                "type": "status_change",
                "buoyId": str(buoy_id),
                "timestamp": current_time.isoformat(),
                "data_type": "status",
                "value": new_status,
                "previous_value": old_status,
                "source": source  # 标识消息来源
            }

            # 广播到特定浮标的数据主题
            specific_topic = f"/topic/buoys/{buoy_id}/data"
            await websocket_service.broadcast(specific_topic, status_change_payload)

            # 广播到全局浮标状态变化主题，供前端全局监听
            global_topic = "/topic/buoys/status_changes"
            await websocket_service.broadcast(global_topic, status_change_payload)

            self.logger.info(f"已广播浮标 {buoy_id} 状态变化: {old_status} -> {new_status}")

            if IS_DEBUG:
                self.logger.debug(f"状态变化广播到主题: {specific_topic}, {global_topic}")

        except Exception as e:
            self.logger.error(f"广播浮标状态变化失败: {str(e)}", exc_info=True)

# 创建 MQTTService 实例
mqtt_service = MQTTService()