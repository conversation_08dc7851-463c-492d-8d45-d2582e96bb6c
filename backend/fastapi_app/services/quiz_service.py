from typing import List, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.sql import func
from fastapi_app.models.quiz_model import QuizQuestion, QuizOption
from fastapi_app.schemas.quiz import QuizQuestion as QuizQuestionSchema, QuizOption as QuizOptionSchema

async def get_questions(db: AsyncSession, limit: int = 5, offset: int = 0, random: bool = False) -> <PERSON><PERSON>[List[QuizQuestionSchema], int]:
    """
    获取知识问答题目列表，支持分页和随机化。
    
    参数:
    - db: 数据库会话
    - limit: 返回的题目数量
    - offset: 从第几条记录开始返回
    - random: 是否随机返回题目列表
    
    返回:
    - 题目列表和总数
    """
    try:
        # 查询总数
        total_result = await db.execute(select(func.count()).select_from(QuizQuestion))
        total = total_result.scalar_one()

        if random:
            # 随机查询
            result = await db.execute(
                select(QuizQuestion)
                .order_by(func.random())
                .limit(limit)
            )
        else:
            # 按顺序查询
            result = await db.execute(
                select(QuizQuestion)
                .offset(offset)
                .limit(limit)
            )
        
        questions = result.scalars().all()
        
        # 为每个题目查询选项
        question_schemas = []
        for question in questions:
            options_result = await db.execute(
                select(QuizOption)
                .where(QuizOption.question_id == question.id)
            )
            options = options_result.scalars().all()
            question_schema = QuizQuestionSchema(
                id=question.id,
                content=question.content,
                options=[QuizOptionSchema(id=opt.id, text=opt.text, question_id=opt.question_id) for opt in options],
                correct_answer_id=question.correct_answer_id,
                category=question.category,
                difficulty=question.difficulty
            )
            question_schemas.append(question_schema)
        
        return question_schemas, total
    except Exception as e:
        raise Exception(f"获取题目列表失败: {str(e)}")

async def submit_answer(db: AsyncSession, question_id: str, selected_option: str):
    """
    提交知识问答答案，并检查是否正确。
    
    参数:
    - db: 数据库会话
    - question_id: 题目ID
    - selected_option: 用户选择的选项ID
    
    返回:
    - 提交结果，包括是否正确和正确答案ID
    """
    try:
        # 将字符串ID转换为整数
        question_id_int = int(question_id)
        selected_option_int = int(selected_option)
        
        # 查询题目和正确答案
        result = await db.execute(
            select(QuizQuestion)
            .where(QuizQuestion.id == question_id_int)
        )
        question = result.scalars().first()
        if not question:
            raise Exception(f"题目 {question_id} 未找到")
        
        # 检查答案是否正确
        is_correct = question.correct_answer_id == selected_option_int
        
        # 这里可以添加记录用户答案的逻辑，如果需要的话
        
        print(f"Question ID: {question_id}, Selected Option: {selected_option}, Is Correct: {is_correct}, Correct Answer ID: {question.correct_answer_id}")
        return {
            "questionId": question_id,
            "selectedOption": selected_option,
            "isCorrect": is_correct,
            "correctAnswerId": question.correct_answer_id
        }
    except Exception as e:
        raise Exception(f"提交答案失败: {str(e)}")