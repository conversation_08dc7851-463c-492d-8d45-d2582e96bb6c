from typing import List, Optional
from datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, text
from fastapi import HTTPException, status
import logging
import time

from fastapi_app.models.buoy_model import Buoy as BuoyModel
from fastapi_app.models.sensor_data_model import SensorData as SensorDataModel
from fastapi_app.schemas.buoy import SensorData, GeoPoint, GeoPointWithTimestamp
from fastapi_app.services.geo_service import wkt_to_geo_point, get_geometry_as_text
from fastapi_app.core.logging_config import IS_DEBUG, debug_log

# 配置日志
logger = logging.getLogger(__name__)

@debug_log
async def check_buoy_exists(buoy_id: int, db: AsyncSession) -> BuoyModel:
    """检查浮标是否存在"""
    logger.info(f"检查浮标是否存在: buoy_id={buoy_id}")

    result = await db.execute(select(BuoyModel).filter(BuoyModel.id == buoy_id))
    buoy = result.scalar_one_or_none()

    if not buoy:
        logger.warning(f"浮标不存在: buoy_id={buoy_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="浮标不存在"
        )

    logger.debug(f"浮标存在: buoy_id={buoy_id}, name={buoy.name}")
    return buoy

@debug_log
async def get_sensor_data(buoy_id: int, limit: int, db: AsyncSession) -> List[SensorData]:
    """获取特定浮标的传感器数据"""
    start_time_func = time.time()
    logger.info(f"获取浮标传感器数据: buoy_id={buoy_id}, limit={limit}")

    # 检查浮标是否存在
    await check_buoy_exists(buoy_id, db)

    # 查询近期的数据，按时间倒序排序
    end_time = datetime.now(timezone.utc)
    start_time = end_time - timedelta(days=7)  # 默认查询最近7天的数据

    logger.debug(f"查询时间范围: {start_time.isoformat()} 至 {end_time.isoformat()}")

    query = select(SensorDataModel).filter(
        SensorDataModel.buoy_id == buoy_id,
        SensorDataModel.data_type != 'location',
        SensorDataModel.timestamp >= start_time,
        SensorDataModel.timestamp <= end_time
    ).order_by(desc(SensorDataModel.timestamp)).limit(limit)

    query_start = time.time()
    result = await db.execute(query)
    sensor_data = result.scalars().all()
    query_time = time.time() - query_start

    logger.debug(f"数据库查询耗时: {query_time:.3f}秒, 获取到 {len(sensor_data)} 条记录")

    # 转换为API响应格式
    result = []
    for data in sensor_data:
        sensor_item = SensorData(
            id=data.id,
            buoy_id=data.buoy_id,
            data_type=data.data_type,
            value=data.value,
            unit=data.unit or "",
            timestamp=data.timestamp
        )
        result.append(sensor_item)

    total_time = time.time() - start_time_func
    logger.info(f"获取浮标传感器数据完成: buoy_id={buoy_id}, 记录数={len(result)}, 总耗时: {total_time:.3f}秒")

    if IS_DEBUG and result:
        data_types = set(item.data_type for item in result)
        logger.debug(f"数据类型: {', '.join(data_types)}")

    return result

@debug_log
async def get_location_history(
    buoy_id: int,
    limit: int,
    db: AsyncSession,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None
) -> List[GeoPointWithTimestamp]:
    """获取特定浮标的位置历史数据"""
    func_start_time = time.time()
    logger.info(f"获取浮标位置历史: buoy_id={buoy_id}, limit={limit}")

    # 检查浮标是否存在
    await check_buoy_exists(buoy_id, db)

    # 获取浮标位置历史数据
    # 如果未提供时间范围，使用默认值（最近7天）
    if end_time is None:
        end_time = datetime.now(timezone.utc)
    if start_time is None:
        start_time = end_time - timedelta(days=7)  # 默认查询最近7天的数据

    logger.debug(f"查询时间范围: {start_time.isoformat()} 至 {end_time.isoformat()}")

    # 优化查询：直接包含WKT格式的位置数据，避免后续重复查询
    query = select(
        SensorDataModel,
        text("ST_AsText(location_value) as location_wkt")
    ).filter(
        SensorDataModel.buoy_id == buoy_id,
        SensorDataModel.data_type == 'location',
        SensorDataModel.timestamp >= start_time,
        SensorDataModel.timestamp <= end_time
    ).order_by(SensorDataModel.timestamp).limit(limit)

    query_start = time.time()
    result = await db.execute(query)
    location_records = result.all()
    query_time = time.time() - query_start

    logger.debug(f"位置数据查询耗时: {query_time:.3f}秒, 获取到 {len(location_records)} 条记录")

    # 转换为API响应格式
    result = []
    wkt_parse_start = time.time()

    for record in location_records:
        data = record[0]  # SensorDataModel对象
        location_wkt = record[1]  # WKT文本

        # 解析WKT字符串获取经纬度
        if location_wkt:
            try:
                geo_point = await wkt_to_geo_point(location_wkt)
                # 添加时间戳信息
                result.append(GeoPointWithTimestamp(
                    longitude=geo_point.longitude,
                    latitude=geo_point.latitude,
                    timestamp=data.timestamp
                ))
            except Exception as e:
                logger.error(f"解析WKT失败: {location_wkt}, 错误: {str(e)}")

    wkt_parse_time = time.time() - wkt_parse_start
    logger.debug(f"WKT解析耗时: {wkt_parse_time:.3f}秒")

    total_time = time.time() - func_start_time
    logger.info(f"获取浮标位置历史完成: buoy_id={buoy_id}, 记录数={len(result)}, 总耗时: {total_time:.3f}秒")

    if IS_DEBUG and result:
        # 记录第一个和最后一个位置点
        first_point = result[0]
        last_point = result[-1]
        logger.debug(f"第一个位置点: 经度={first_point.longitude}, 纬度={first_point.latitude}, 时间={first_point.timestamp}")
        logger.debug(f"最后一个位置点: 经度={last_point.longitude}, 纬度={last_point.latitude}, 时间={last_point.timestamp}")

    return result