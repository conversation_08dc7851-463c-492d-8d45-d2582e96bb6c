from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from geoalchemy2.elements import WKTElement
from fastapi_app.schemas.buoy import GeoPoint

async def wkt_to_geo_point(wkt_str: str) -> GeoPoint:
    """从WKT字符串提取GeoPoint对象"""
    if wkt_str:
        coords = wkt_str.replace("POINT(", "").replace(")", "").split()
        if len(coords) >= 2:
            lon = float(coords[0])
            lat = float(coords[1])
            return GeoPoint(longitude=lon, latitude=lat)
    
    return GeoPoint(longitude=0, latitude=0)

async def get_geometry_as_text(db: AsyncSession, table: str, geom_column: str, id_column: str, id_value: int) -> Optional[str]:
    """以文本格式获取几何对象"""
    query = text(f"SELECT ST_AsText({geom_column}) FROM {table} WHERE {id_column} = :id")
    result = await db.execute(query, {"id": id_value})
    return result.scalar()

async def parse_location_to_coordinates(wkt_str: str) -> Tuple[float, float]:
    """从WKT字符串解析经纬度坐标"""
    # 复用wkt_to_geo_point函数，避免代码重复
    geo_point = await wkt_to_geo_point(wkt_str)
    return (geo_point.longitude, geo_point.latitude)

def create_wkt_point(longitude: float, latitude: float, srid: int = 4326) -> WKTElement:
    """创建WKT点元素"""
    return WKTElement(f'POINT({longitude} {latitude})', srid=srid)

def geo_point_to_wkt(point: GeoPoint, srid: int = 4326) -> WKTElement:
    """将GeoPoint对象转换为WKT元素"""
    return create_wkt_point(point.longitude, point.latitude, srid) 