import hashlib
import json
import logging
from fastapi import Request, HTTPException, status, Depends
from fastapi.concurrency import run_in_threadpool
from fastapi_app.core.config import Settings
from fastapi_app.services.local_cache_service import LocalCacheService
from fastapi_app.schemas.analysis import AnalysisReportRequest, AnalysisReportResponse

settings = Settings()
logger = logging.getLogger("analysis_guard")

def get_rate_limit_key(ip: str, user_id: str):
    return f"rate:analysis:ip:{ip}", f"rate:analysis:user:{user_id}", "rate:analysis:global"

async def check_rate_limit(ip: str, user_id: str):
    # 限流参数解析
    ip_limit = settings.RATE_LIMIT_IP
    user_limit = settings.RATE_LIMIT_USER
    global_limit = settings.RATE_LIMIT_GLOBAL

    # 解析如 "30/minute"
    def parse_limit(limit_str):
        count, per = limit_str.split("/")
        count = int(count)
        if "minute" in per:
            window = 60
        elif "second" in per:
            window = 1
        elif "hour" in per:
            window = 3600
        else:
            window = 60
        return count, window

    ip_count, ip_window = parse_limit(ip_limit)
    user_count, user_window = parse_limit(user_limit)
    global_count, global_window = parse_limit(global_limit)

    ip_key, user_key, global_key = get_rate_limit_key(ip, user_id)

    # incr 并设置过期
    for key, count, window in [
        (ip_key, ip_count, ip_window),
        (user_key, user_count, user_window),
        (global_key, global_count, global_window)
    ]:
        cur = await LocalCacheService.incr(key)
        if cur == 1:
            await LocalCacheService.expire(key, window)
        if cur > count:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="请求过于频繁，请稍后再试"
            )

async def get_cache_key(user_id: str, req_body: dict):
    # 以用户+参数hash为key
    param_str = json.dumps(req_body, sort_keys=True, ensure_ascii=False)
    param_hash = hashlib.sha256(param_str.encode("utf-8")).hexdigest()
    return f"analysis:cache:{user_id}:{param_hash}"

async def get_cached_result(cache_key: str):
    cached = await LocalCacheService.get(cache_key)
    if cached:
        return json.loads(cached)
    return None

async def set_cache_result(cache_key: str, result: AnalysisReportResponse, ttl: int):
    """缓存分析结果
    
    Args:
        cache_key: 缓存键
        result: AnalysisReportResponse 对象
        ttl: 缓存过期时间（秒）
    """
    try:
        # 使用 Pydantic v2 的序列化方法
        json_str = result.model_dump_json()
        await LocalCacheService.set(cache_key, json_str, ex=ttl)
    except Exception as e:
        logger.error(f"缓存分析结果失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="缓存分析结果失败"
        )

async def analysis_guard(request: Request, current_user=Depends(lambda: None)):
    # 1. 限流
    ip = request.client.host
    user_id = getattr(current_user, "id", "anonymous") if current_user else "anonymous"
    await check_rate_limit(ip, user_id)

    # 2. 缓存去重 - 使用 Pydantic 模型验证
    try:
        # 读取原始请求体
        body_bytes = await request.body()
        body_str = body_bytes.decode()
        logger.debug(f"原始请求体: {body_str}")
        
        # 使用 Pydantic 模型验证
        body_dict = json.loads(body_str)
        validated_body = AnalysisReportRequest.model_validate(body_dict)
        
        # 使用验证后的数据生成缓存键
        cache_key = await get_cache_key(user_id, validated_body.model_dump())
        cached = await get_cached_result(cache_key)
        if cached:
            return cached, cache_key

        return None, cache_key
    except json.JSONDecodeError as e:
        logger.error(f"JSON 解析错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="无效的 JSON 格式"
        )
    except Exception as e:
        logger.error(f"请求验证错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"请求数据验证失败: {str(e)}"
        )