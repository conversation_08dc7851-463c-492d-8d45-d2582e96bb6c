"""
浮标心跳监控服务
定期检查所有浮标的最后心跳时间，并将超时的浮标状态更新为inactive
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from fastapi_app.db.base import async_session
from fastapi_app.models.buoy_model import Buoy
from fastapi_app.services.websocket_service import websocket_service

# 导入调试标志
from fastapi_app.core.logging_config import IS_DEBUG

class HeartbeatMonitorService:
    """浮标心跳监控服务"""

    def __init__(self, heartbeat_timeout_seconds: int = 120):
        """
        初始化心跳监控服务

        Args:
            heartbeat_timeout_seconds: 心跳超时时间（秒），默认为120秒（2分钟）
        """
        self.logger = logging.getLogger(__name__)
        self.heartbeat_timeout_seconds = heartbeat_timeout_seconds
        self.is_running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.check_interval_seconds = 30  # 检查间隔，默认30秒

    async def start(self) -> None:
        """启动心跳监控服务"""
        if self.is_running:
            self.logger.warning("心跳监控服务已经在运行中")
            return

        self.is_running = True
        self.logger.info(f"启动心跳监控服务，超时时间: {self.heartbeat_timeout_seconds}秒，检查间隔: {self.check_interval_seconds}秒")

        # 创建异步任务
        self.monitor_task = asyncio.create_task(self._monitor_loop())

    async def stop(self) -> None:
        """停止心跳监控服务"""
        if not self.is_running:
            self.logger.warning("心跳监控服务未运行")
            return

        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            self.monitor_task = None

        self.logger.info("心跳监控服务已停止")

    async def _monitor_loop(self) -> None:
        """心跳监控循环"""
        try:
            while self.is_running:
                try:
                    await self._check_heartbeats()
                except Exception as e:
                    self.logger.error(f"检查心跳时发生错误: {str(e)}", exc_info=True)

                # 等待下一次检查
                await asyncio.sleep(self.check_interval_seconds)
        except asyncio.CancelledError:
            self.logger.info("心跳监控循环已取消")
            raise
        except Exception as e:
            self.logger.error(f"心跳监控循环发生未处理的异常: {str(e)}", exc_info=True)

    async def _check_heartbeats(self) -> None:
        """检查所有浮标的心跳状态"""
        if IS_DEBUG:
            self.logger.debug("开始检查浮标心跳状态")

        # 计算超时时间点
        timeout_threshold = datetime.now(timezone.utc) - timedelta(seconds=self.heartbeat_timeout_seconds)

        async with async_session() as db:
            # 查询所有状态为active但最后心跳时间超过阈值的浮标
            query = select(Buoy).where(
                (Buoy.status == 'active') &
                ((Buoy.last_heartbeat < timeout_threshold) | (Buoy.last_heartbeat.is_(None)))
            )

            result = await db.execute(query)
            timed_out_buoys = result.scalars().all()

            if timed_out_buoys:
                if IS_DEBUG:
                    self.logger.debug(f"发现 {len(timed_out_buoys)} 个浮标心跳超时")

                # 更新每个超时浮标的状态
                for buoy in timed_out_buoys:
                    if IS_DEBUG:
                        self.logger.debug(f"浮标 {buoy.id} 心跳超时，最后心跳时间: {buoy.last_heartbeat}")

                    # 记录旧状态
                    old_status = buoy.status

                    # 更新浮标状态为inactive
                    buoy.status = 'inactive'
                    db.add(buoy)

                    # 通过WebSocket广播状态更新
                    await self._broadcast_status_update(buoy.id, 'inactive', old_status)

                # 提交所有更改
                await db.commit()
                self.logger.info(f"已将 {len(timed_out_buoys)} 个心跳超时的浮标状态更新为inactive")
            else:
                if IS_DEBUG:
                    self.logger.debug("未发现心跳超时的浮标")

    async def _broadcast_status_update(self, buoy_id: int, status: str, old_status: str = None) -> None:
        """
        通过WebSocket广播浮标状态更新

        Args:
            buoy_id: 浮标ID
            status: 新状态
            old_status: 旧状态（可选）
        """
        try:
            # 构造WebSocket消息
            current_time = datetime.now(timezone.utc)
            status_change_payload: Dict[str, Any] = {
                "type": "status_change",
                "buoyId": str(buoy_id),
                "timestamp": current_time.isoformat(),
                "data_type": "status",
                "value": status,
                "previous_value": old_status,
                "source": "heartbeat_timeout"  # 标识消息来源
            }

            # 广播到特定浮标的数据主题
            specific_topic = f"/topic/buoys/{buoy_id}/data"
            await websocket_service.broadcast(specific_topic, status_change_payload)

            # 广播到全局浮标状态变化主题
            global_topic = "/topic/buoys/status_changes"
            await websocket_service.broadcast(global_topic, status_change_payload)

            self.logger.info(f"已广播浮标 {buoy_id} 心跳超时状态变化: {old_status} -> {status}")

            if IS_DEBUG:
                self.logger.debug(f"心跳超时状态广播到主题: {specific_topic}, {global_topic}")
        except Exception as e:
            self.logger.error(f"广播心跳超时状态到WebSocket失败: {str(e)}", exc_info=True)

# 创建全局服务实例
heartbeat_monitor_service = HeartbeatMonitorService()
