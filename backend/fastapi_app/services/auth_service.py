from datetime import datetime, timedelta, timezone
import secrets
from typing import Any, Optional

from fastapi import HTTP<PERSON>x<PERSON>, status
from jose import jwt, JWTError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from fastapi_app.core.config import settings
from fastapi_app.models.user_model import User
from fastapi_app.services.security import verify_password, get_password_hash


def create_access_token(data: dict, expires_delta: timedelta = None) -> str:
    """创建JWT访问令牌"""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + (expires_delta or timedelta(minutes=15))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
    return encoded_jwt


def create_refresh_token() -> str:
    """创建刷新令牌"""
    return secrets.token_urlsafe(64)


async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
    """通过用户名获取用户"""
    result = await db.execute(select(User).where(User.username == username))
    return result.scalar_one_or_none()


async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
    """通过ID获取用户"""
    result = await db.execute(select(User).where(User.id == user_id))
    return result.scalar_one_or_none()


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """通过邮箱获取用户"""
    result = await db.execute(select(User).where(User.email == email))
    return result.scalar_one_or_none()


async def get_user_by_refresh_token(db: AsyncSession, refresh_token: str) -> Optional[User]:
    """通过刷新令牌获取用户"""
    result = await db.execute(select(User).where(User.refresh_token == refresh_token))
    return result.scalar_one_or_none()


async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    """验证用户凭据"""
    user = await get_user_by_username(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return None
    return user


async def validate_token(db: AsyncSession, token: str) -> User:
    """验证JWT令牌并返回用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        user_id = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = await get_user_by_id(db, int(user_id))
    if user is None:
        raise credentials_exception
    return user


async def update_refresh_token(db: AsyncSession, user_id: int, refresh_token: Optional[str] = None) -> None:
    """更新用户的刷新令牌"""
    # 使用带UTC时区的datetime对象，确保时区一致性
    current_time = datetime.now(timezone.utc)
    await db.execute(
        update(User).where(User.id == user_id).values(
            refresh_token=refresh_token,
            updated_at=current_time
        )
    )
    await db.commit()


async def create_user(db: AsyncSession, username: str, email: str, password: str, role: str = "user") -> User:
    """创建新用户"""
    user = User(
        username=username,
        email=email,
        hashed_password=get_password_hash(password),
        role=role,
    )
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user